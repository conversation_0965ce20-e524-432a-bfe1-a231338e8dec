<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => ':attribute трябва да бъде приет.',
    'accepted_if' => ':attribute трябва да бъде приет, когато :other е :value.',
    'active_url' => ':attribute не е валиден URL адрес.',
    'after' => ':attribute трябва да бъде дата след :date.',
    'after_or_equal' => ':attribute трябва да бъде дата след или равна на :date.',
    'alpha' => ':attribute може да съдържа само букви.',
    'alpha_dash' => ':attribute може да съдържа само букви, цифри, тирета и подчертавки.',
    'alpha_num' => ':attribute може да съдържа само букви и цифри.',
    'array' => ':attribute трябва да бъде масив.',
    'before' => ':attribute трябва да бъде дата преди :date.',
    'before_or_equal' => ':attribute трябва да бъде дата преди или равна на :date.',
    'between' => [
        'array' => ':attribute трябва да има между :min и :max елемента.',
        'file' => ':attribute трябва да бъде между :min и :max килобайта.',
        'numeric' => ':attribute трябва да бъде между :min и :max.',
        'string' => ':attribute трябва да бъде между :min и :max символа.',
    ],
    'boolean' => 'Полето :attribute трябва да бъде вярно или невярно.',
    'confirmed' => 'Потвърждението на :attribute не съвпада.',
    'current_password' => 'Грешна парола.',
    'date' => ':attribute не е валидна дата.',
    'date_equals' => ':attribute трябва да бъде дата, равна на :date.',
    'date_format' => ':attribute не съответства на формата :format.',
    'declined' => ':attribute трябва да бъде отхвърлен.',
    'declined_if' => ':attribute трябва да бъде отхвърлен, когато :other е :value.',
    'different' => ':attribute и :other трябва да бъдат различни.',
    'digits' => ':attribute трябва да бъде :digits цифри.',
    'digits_between' => ':attribute трябва да бъде между :min и :max цифри.',
    'dimensions' => ':attribute има невалидни размери на изображението.',
    'distinct' => 'Полето :attribute има дублираща се стойност.',
    'email' => ':attribute трябва да бъде валиден имейл адрес.',
    'ends_with' => ':attribute трябва да завършва с едно от следните: :values.',
    'enum' => 'Избраният :attribute е невалиден.',
    'exists' => 'Избраният :attribute е невалиден.',
    'file' => ':attribute трябва да бъде файл.',
    'filled' => 'Полето :attribute трябва да има стойност.',
    'gt' => [
        'array' => ':attribute трябва да има повече от :value елемента.',
        'file' => ':attribute трябва да бъде по-голям от :value килобайта.',
        'numeric' => ':attribute трябва да бъде по-голям от :value.',
        'string' => ':attribute трябва да съдържа повече от :value символа.',
    ],
    'gte' => [
        'array' => ':attribute трябва да има поне :value елемента.',
        'file' => ':attribute трябва да бъде по-голям или равен на :value килобайта.',
        'numeric' => ':attribute трябва да бъде по-голям или равен на :value.',
        'string' => ':attribute трябва да съдържа поне :value символа.',
    ],
    'image' => ':attribute трябва да бъде изображение.',
    'in' => 'Избраният :attribute е невалиден.',
    'in_array' => 'Полето :attribute не съществува в :other.',
    'integer' => ':attribute трябва да бъде цяло число.',
    'ip' => ':attribute трябва да бъде валиден IP адрес.',
    'ipv4' => ':attribute трябва да бъде валиден IPv4 адрес.',
    'ipv6' => ':attribute трябва да бъде валиден IPv6 адрес.',
    'json' => ':attribute трябва да бъде валиден JSON низ.',
    'lt' => [
        'array' => ':attribute трябва да има по-малко от :value елемента.',
        'file' => ':attribute трябва да бъде по-малък от :value килобайта.',
        'numeric' => ':attribute трябва да бъде по-малък от :value.',
        'string' => ':attribute трябва да съдържа по-малко от :value символа.',
    ],
    'lte' => [
        'array' => ':attribute не трябва да има повече от :value елемента.',
        'file' => ':attribute трябва да бъде по-малък или равен на :value килобайта.',
        'numeric' => ':attribute трябва да бъде по-малък или равен на :value.',
        'string' => ':attribute трябва да съдържа най-много :value символа.',
    ],
    'mac_address' => ':attribute трябва да бъде валиден MAC адрес.',
    'max' => [
        'array' => ':attribute не трябва да има повече от :max елемента.',
        'file' => ':attribute не трябва да бъде по-голям от :max килобайта.',
        'numeric' => ':attribute не трябва да бъде по-голям от :max.',
        'string' => ':attribute не трябва да съдържа повече от :max символа.',
    ],
    'mimes' => ':attribute трябва да бъде файл от тип: :values.',
    'mimetypes' => ':attribute трябва да бъде файл от тип: :values.',
    'min' => [
        'array' => ':attribute трябва да има поне :min елемента.',
        'file' => ':attribute трябва да бъде поне :min килобайта.',
        'numeric' => ':attribute трябва да бъде поне :min.',
        'string' => ':attribute трябва да съдържа поне :min символа.',
    ],
    'multiple_of' => ':attribute трябва да бъде кратно на :value.',
    'not_in' => 'Избраният :attribute е невалиден.',
    'not_regex' => 'Форматът на :attribute е невалиден.',
    'numeric' => ':attribute трябва да бъде число.',
    'password' => 'Паролата е неправилна.',
    'present' => 'Полето :attribute трябва да присъства.',
    'prohibited' => 'Полето :attribute е забранено.',
    'prohibited_if' => 'Полето :attribute е забранено, когато :other е :value.',
    'prohibited_unless' => 'Полето :attribute е забранено, освен ако :other не е в :values.',
    'prohibits' => 'Полето :attribute забранява присъствието на :other.',
    'regex' => 'Форматът на :attribute е невалиден.',
    'required' => 'Полето :attribute е задължително.',
    'required_array_keys' => 'Полето :attribute трябва да съдържа записи за: :values.',
    'required_if' => 'Полето :attribute е задължително, когато :other е :value.',
    'required_unless' => 'Полето :attribute е задължително, освен ако :other не е в :values.',
    'required_with' => 'Полето :attribute е задължително, когато :values присъства.',
    'required_with_all' => 'Полето :attribute е задължително, когато :values присъстват.',
    'required_without' => 'Полето :attribute е задължително, когато :values не присъства.',
    'required_without_all' => 'Полето :attribute е задължително, когато никое от :values не присъства.',
    'same' => ':attribute и :other трябва да съвпадат.',
    'size' => [
        'array' => ':attribute трябва да съдържа :size елемента.',
        'file' => ':attribute трябва да бъде :size килобайта.',
        'numeric' => ':attribute трябва да бъде :size.',
        'string' => ':attribute трябва да бъде :size символа.',
    ],
    'unique' => ':attribute вече е зает.',
    'uploaded' => 'Неуспешно качване на :attribute.',
    'url' => ':attribute трябва да бъде валиден URL адрес.',
    'uuid' => ':attribute трябва да бъде валиден UUID.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [],

];
