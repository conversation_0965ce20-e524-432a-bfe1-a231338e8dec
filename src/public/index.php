<?php

// $maintenanceHtml = '
// <!DOCTYPE html>
// <html lang="en">
// <head>
//     <meta charset="UTF-8">
//     <meta name="viewport" content="width=device-width, initial-scale=1.0">
//     <title>Under Maintenance</title>
//     <style>
//       body { text-align: center; padding: 150px; }
//       h1 { font-size: 30px; }
//       body { font: 20px Helvetica, sans-serif; color: #333; }
//       article { display: block; text-align: left; width: 650px; margin: 0 auto; }
//       a { color: #dc8100; text-decoration: none; }
//       a:hover { color: #333; text-decoration: none; }
//     </style>
// </head>
// <body>
//     <h1>Здравей!</h1>
//     <h1>Сайта ни се обновява и няма да бъде достъпен за потребители до 08:00 на 19-ти февруари.</h1>
//     <h1>Ако си направил плащане по кредита си, то ще бъде отразено в системата ни още в понеделник.</h1>
// </body>
// </html>
// ';
// echo $maintenanceHtml; exit;


use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

/*
|--------------------------------------------------------------------------
| Check If The Application Is Under Maintenance
|--------------------------------------------------------------------------
|
| If the application is in maintenance / demo mode via the "down" command
| we will load this file so that any pre-rendered content can be shown
| instead of starting the framework, which could cause an exception.
|
*/

if (file_exists($maintenance = __DIR__.'/../storage/framework/maintenance.php')) {
    require $maintenance;
}

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| this application. We just need to utilize it! We'll simply require it
| into the script here so we don't need to manually load our classes.
|
*/

require __DIR__.'/../vendor/autoload.php';

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
|
| Once we have the application, we can handle the incoming request using
| the application's HTTP kernel. Then, we will send the response back
| to this client's browser, allowing them to enjoy our application.
|
*/

$app = require_once __DIR__.'/../bootstrap/app.php';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
