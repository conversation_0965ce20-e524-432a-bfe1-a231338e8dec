# Front End Resources Usage

The styles of the Lendivo project as written in SCSS and then compiled to a minified CSS file.

## Compiler
For the compiling process is used the (Prepros Compiler)[https://prepros.io/]

**License Key: 4PCUF-2DMJZ-RBTGC-C6NG7-N4YE3-3CWJS**

## Setup
1. Install the compiler
2. Drag and drop the Root Project in the compiler
3. Choose **main.scss** from the compiler and hit **Process file**
4. That's it! From now on, the watcher will detect each of your changes.

## SCSS File Structure
1. **Main File:** /css/main.scss
2. **Components:** /css/components/
3. **Helpers:** /css/helpers/

## JS File Structure


### SRC - Raw Files
Path: **/src/public/js/src**

#### Global.js
Serves as autoloader of JS modules and their dependencies

Compiled to: **/src/public/js/dist/global.js**

**Autoload Example Object:**
```js
{
    script: 'example-js-module-file-name',
    className: 'ExampleJSModuleClassName',
    onload: () => {

        // Called when the scrip has loaded (not needed for every module)
        window.ExampleJSModuleClassName.init();
    },
    loadWhen: () => {

        // Set of rules when to load the module
        return (
            typeof document.querySelector('.js-filters') !== 'undefined' &&
            document.querySelector('.js-filters') !== null
        );
    },
    worksWith: [ // Dependencies
        {
            script: 'nice-select2',
        },
        {
            script: 'noui-slider',
        },
    ]
},
```

#### Autoload
Autoloaded JS Modules must be placed within: **/src/public/js/src/autoload/**

Compiled JS Modules go to: **/src/public/js/dist/autoload/**

**Note:** File name should be the same in **SRC** and **DIST**

#### Dependencies
Dependecy JS Modules must be placed within: **/src/public/js/src/dependencies/**

Compiled JS Modules go to: **/src/public/js/dist/dependencies/**

**Note:** File name should be the same in **SRC** and **DIST**