<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Fri Dec  7 11:28:02 2018
 By deploy user
Copyright &#194;&#169; 2018 by Piotr &#197;&#129;apa. All rights reserved.
</metadata>
<defs>
<font id="Gilmer-Heavy" horiz-adv-x="736" >
  <font-face 
    font-family="Gilmer Heavy"
    font-weight="800"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 9 0 0 0 0 0 0 0"
    ascent="790"
    descent="-210"
    x-height="510"
    cap-height="700"
    bbox="-423 -272 1495 984"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-U+FB02"
  />
<missing-glyph horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="655" 
d="M608 510v-510h-160v370h-180v-370h-160v370h-93v140h93v36q0 92 55 145t159 53q109 0 186 -44l-38 -127q-65 33 -129 33q-30 0 -51.5 -16.5t-21.5 -43.5v-36h340z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="668" 
d="M325 744q164 0 283 -69v-675h-160v588q-47 18 -106 18q-31 0 -52.5 -16.5t-21.5 -43.5v-36h120v-140h-120v-370h-160v370h-93v140h93v36q0 92 55.5 145t161.5 53z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="757" 
d="M608 546v-36h140v-140h-140v-370h-160v370h-180v-370h-160v370h-93v140h93v36q0 92 49 145t140 53q71 0 120 -22l-38 -127q-26 11 -57 11q-25 0 -39.5 -16t-14.5 -44v-36h180v36q0 92 49 145t140 53q71 0 120 -22l-38 -127q-26 11 -57 11q-25 0 -39.5 -16t-14.5 -44z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="995" 
d="M948 510v-510h-160v370h-180v-370h-160v370h-180v-370h-160v370h-93v140h93v36q0 92 51.5 145t147.5 53q71 0 120 -22l-38 -127q-26 11 -57 11q-26 0 -45 -16.5t-19 -43.5v-36h180v36q0 92 55 145t159 53q109 0 186 -44l-38 -127q-65 33 -129 33q-30 0 -51.5 -16.5
t-21.5 -43.5v-36h340z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="1008" 
d="M665 744q164 0 283 -69v-675h-160v588q-47 18 -106 18q-31 0 -52.5 -16.5t-21.5 -43.5v-36h120v-140h-120v-370h-160v370h-180v-370h-160v370h-93v140h93v36q0 92 51.5 145t147.5 53q71 0 120 -22l-38 -127q-26 11 -57 11q-29 0 -46.5 -16t-17.5 -44v-36h180v36
q0 92 55.5 145t161.5 53z" />
    <glyph glyph-name=".notdef" horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="240" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="289" 
d="M80 245l-21 455h172l-21 -455h-130zM145 -8q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="410" 
d="M58 453l-18 247h148l-18 -247h-112zM240 453l-18 247h148l-18 -247h-112z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="725" 
d="M686 391h-129l-17 -112h129l-19 -119h-129l-25 -160h-130l25 160h-112l-25 -160h-130l25 160h-129l19 119h129l17 112h-129l19 119h129l26 165h130l-26 -165h112l26 165h130l-26 -165h129zM410 279l17 112h-112l-17 -112h112z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="671" 
d="M636 208q0 -86 -65.5 -146.5t-173.5 -72.5v-134h-136v140q-90 20 -152 76t-89 127l158 49q21 -52 68 -84t113 -32q49 0 79 19.5t30 46.5q0 43 -75 67l-180 56q-73 23 -117 71t-43 114q-1 77 57 134.5t151 70.5v135h136v-140q82 -18 141.5 -66t82.5 -109l-153 -45
q-21 37 -62.5 60.5t-91.5 23.5q-42 0 -67.5 -16.5t-25.5 -41.5q0 -36 47 -50l176 -56q92 -29 142 -72.5t50 -124.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1071" 
d="M245 299q-88 0 -149 59t-61 145t60.5 145t149.5 59t149.5 -59t60.5 -145t-60.5 -145t-149.5 -59zM246 0l454 700h124l-453 -700h-125zM245 414q39 0 64.5 26t25.5 63q0 38 -25.5 63.5t-64.5 25.5t-64.5 -25.5t-25.5 -63.5q0 -37 25.5 -63t64.5 -26zM826 -8q-88 0 -149 59
t-61 145t60.5 145t149.5 59t149.5 -59t60.5 -145t-60.5 -145t-149.5 -59zM826 107q39 0 64.5 26t25.5 63q0 38 -25.5 63.5t-64.5 25.5t-64.5 -25.5t-25.5 -63.5q0 -37 25.5 -63t64.5 -26z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="729" 
d="M729 0h-193l-53 56q-93 -71 -201 -71q-96 0 -164 58.5t-68 151.5q0 57 31 109.5t91 89.5q-48 62 -48 118q0 96 63 149t169 53q86 0 146.5 -50t69.5 -120l-143 -41q-6 32 -26.5 51.5t-50.5 19.5q-32 0 -51 -18t-19 -46q0 -7 2.5 -15t4.5 -13.5t8 -13.5l8 -10.5l9 -11.5
l8 -8l154 -166q26 48 36 95l140 -40q-17 -84 -72 -166zM300 130q42 0 86 31l-123 132q-53 -37 -53 -84q0 -33 24.5 -56t65.5 -23z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="228" 
d="M58 453l-18 247h148l-18 -247h-112z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="325" 
d="M315 -135h-155q-113 184 -115 440q-2 251 115 440h155q-115 -189 -115 -440t115 -440z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="325" 
d="M10 -135q115 189 115 440t-115 440h155q117 -189 115 -440q-2 -256 -115 -440h-155z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="428" 
d="M388 589l-94 -49l94 -49l-45 -77l-89 57l5 -106h-90l5 106l-89 -57l-45 77l94 49l-94 49l45 77l89 -56l-5 105h90l-5 -105l89 56z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="560" 
d="M505 413v-125h-162v-162h-125v162h-163v125h163v163h125v-163h162z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="289" 
d="M40 -137l44 295h165l-85 -295h-124z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="480" 
d="M55 240v130h370v-130h-370z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="269" 
d="M135 -8q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="569" 
d="M25 -91l396 881h123l-396 -881h-123z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="655" 
d="M328 -14q-139 0 -218.5 101t-79.5 263t79.5 263t218.5 101t218 -101t79 -263t-79 -263t-218 -101zM328 136q66 0 98.5 56.5t32.5 157.5t-32.5 157.5t-98.5 56.5t-99 -56.5t-33 -157.5t33 -157.5t99 -56.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="446" 
d="M229 700h142v-700h-168v517l-148 -68l-40 141z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="593" 
d="M289 150h264v-150h-519v111l270 251q39 35 57 61.5t18 56.5q0 40 -24 63t-66 23q-41 0 -70.5 -28.5t-39.5 -75.5l-149 43q11 86 81.5 147.5t173.5 61.5q117 0 188 -58.5t71 -165.5q0 -63 -30.5 -114t-95.5 -110z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="609" 
d="M402 437q75 -18 121 -72.5t46 -136.5q0 -102 -74 -172t-184 -70q-124 0 -195 64.5t-86 155.5l147 42q9 -48 43.5 -83t80.5 -35q50 0 79 29t29 69t-28 68.5t-77 28.5q-37 0 -63 -13l-27 96l113 144h-275v148h493v-109z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="624" 
d="M604 272v-146h-83v-126h-166v126h-325v120l231 454h178l-215 -428h131v126h166v-126h83z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="599" 
d="M333 461q100 0 165.5 -67.5t65.5 -165.5q0 -103 -73 -172.5t-184 -69.5q-113 0 -183.5 56t-93.5 139l149 42q14 -40 46 -66.5t71 -26.5q50 0 79 28.5t29 72.5t-28.5 72t-80.5 28q-61 0 -104 -30l-132 39l30 360h443v-148h-296l-8 -108q48 17 105 17z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="596" 
d="M323 484q109 -8 178.5 -76t69.5 -170q0 -108 -78 -180t-195 -72t-195 71.5t-78 180.5q0 70 49 148l199 314h187zM298 134q49 0 78.5 29t29.5 75q0 44 -29.5 73.5t-78.5 29.5q-48 0 -78 -29.5t-30 -73.5q0 -46 29.5 -75t78.5 -29z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="549" 
d="M15 700h514v-113l-256 -587h-176l242 550h-324v150z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="616" 
d="M470 382q116 -54 116 -177q0 -93 -77 -156t-201 -63q-125 0 -201.5 62.5t-76.5 156.5q0 124 116 177q-86 46 -86 143q0 80 68 134.5t180 54.5t180 -54.5t68 -134.5q0 -97 -86 -143zM308 585q-40 0 -64 -22t-24 -53q0 -32 24 -54t64 -22t64 22.5t24 53.5t-24 53t-64 22z
M308 124q51 0 81 27t30 68q0 42 -30 69t-81 27q-52 0 -81.5 -27t-29.5 -69q0 -41 29.5 -68t81.5 -27z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="596" 
d="M298 714q117 0 195 -71.5t78 -180.5q0 -70 -49 -148l-199 -314h-187l137 216q-109 8 -178.5 76t-69.5 170q0 108 78 180t195 72zM298 359q48 0 78 29.5t30 73.5q0 46 -29.5 75t-78.5 29t-78.5 -29t-29.5 -75q0 -44 29.5 -73.5t78.5 -29.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="269" 
d="M135 333q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27zM135 -8q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="293" 
d="M159 334q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27zM40 -137l44 295h165l-85 -295h-124z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="555" 
d="M495 593v-135l-302 -107l302 -108v-135l-450 170v145z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="560" 
d="M55 388v125h450v-125h-450zM55 188v125h450v-125h-450z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="555" 
d="M60 593l450 -170v-145l-450 -170v135l302 108l-302 107v135z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="575" 
d="M200 245q0 38 17 71t41 55.5l48 43.5t41 43.5t17 45.5q0 29 -22 46.5t-59 17.5q-41 0 -69.5 -30.5t-39.5 -80.5l-144 44q11 89 78.5 151t175.5 62t177 -57t69 -145q0 -42 -18 -78.5t-43.5 -61t-51 -45.5t-44 -42t-18.5 -40h-155zM278 -8q-40 0 -67.5 27t-27.5 67
q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="972" 
d="M478 -177q-192 0 -317.5 125t-125.5 308q0 123 61.5 225t166.5 160.5t230 58.5q194 0 319 -124t125 -315q0 -116 -60 -184t-152 -68q-106 0 -144 81q-53 -59 -125 -59q-91 0 -149.5 63t-58.5 161t58.5 161.5t149.5 63.5q69 0 118 -54v42h123v-312q0 -56 46 -56
q37 0 61.5 43t24.5 117q0 149 -91.5 244t-243.5 95q-148 0 -250 -98.5t-102 -244.5q0 -142 96.5 -237t242.5 -95q124 0 195 58l28 -98q-85 -61 -226 -61zM480 147q44 0 72.5 30t28.5 79q0 48 -28 77.5t-73 29.5q-47 0 -75.5 -29.5t-28.5 -77.5t28.5 -78.5t75.5 -30.5z" />
    <glyph glyph-name="A" unicode="A" 
d="M565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="695" 
d="M556 366q109 -53 109 -166q0 -94 -65.5 -147t-179.5 -53h-360v700h342q107 0 172.5 -52.5t65.5 -137.5q0 -96 -84 -144zM402 558h-180v-140h180q33 0 54.5 21t21.5 51t-21.5 49.5t-54.5 18.5zM420 143q37 0 58.5 20.5t21.5 52.5q0 33 -21 52.5t-59 19.5h-198v-145h198z
" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="773" 
d="M403 -14q-159 0 -264.5 104t-105.5 260q0 155 105 259.5t262 104.5q139 0 229.5 -71t113.5 -164l-162 -40q-14 48 -63 83.5t-117 35.5q-89 0 -143.5 -60t-54.5 -147q0 -86 55 -147.5t143 -61.5q69 0 117.5 34.5t62.5 82.5l162 -41q-23 -92 -113 -162t-227 -70z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="747" 
d="M383 700q148 0 239.5 -97t91.5 -253q0 -154 -91 -252t-240 -98h-323v700h323zM379 149q72 0 119.5 56.5t47.5 147.5q0 90 -47.5 144t-119.5 54h-155v-402h155z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="627" 
d="M587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700h527v-150z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="596" 
d="M581 549h-355v-139h304v-148h-304v-262h-166v700h521v-151z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="799" 
d="M766 398v-83q0 -144 -98 -236.5t-258 -92.5q-163 0 -270 104.5t-107 259.5t106 259.5t263 104.5q127 0 216.5 -62t124.5 -155l-154 -44q-55 105 -185 105q-89 0 -146 -60t-57 -147t58.5 -149t163.5 -62q71 0 114.5 34t54.5 82l-184 -1v143h358z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="728" 
d="M502 700h166v-700h-166v281h-276v-281h-166v700h166v-269h276v269z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="288" 
d="M60 0v700h168v-700h-168z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="626" 
d="M571 700v-461q0 -116 -74 -184.5t-197 -68.5q-105 0 -176.5 51.5t-103.5 140.5l155 45q29 -93 122 -93q50 0 79.5 32.5t29.5 81.5v311h-307v145h472z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="715" 
d="M509 0l-196 275l-87 -98v-177h-166v700h166v-304l254 304h210l-268 -302l288 -398h-201z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="575" 
d="M226 152h329v-152h-495v700h166v-548z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="863" 
d="M645 700h158v-700h-166v410l-206 -281l-205 281v-410h-166v700h158l214 -311z" />
    <glyph glyph-name="N" unicode="N" 
d="M510 700h166v-700h-135l-315 401v-401h-166v700h135l315 -401v401z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="808" 
d="M404 -14q-160 0 -265.5 104t-105.5 260t105.5 260t265.5 104t265.5 -104t105.5 -260t-105.5 -260t-265.5 -104zM404 139q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="663" 
d="M397 700q112 0 181.5 -77t69.5 -175q0 -99 -70.5 -175.5t-187.5 -76.5h-166v-196h-164v700h337zM386 344q44 0 72.5 31t28.5 72t-27 72t-67 31h-169v-206h162z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="802" 
d="M769 0h-162l-29 33q-85 -47 -179 -47q-152 0 -259 105t-107 259t107 259t259 105t259 -105t107 -259q0 -134 -88 -237zM399 138q41 0 75 12l-154 175h185l71 -88q27 51 27 113q0 94 -56.5 153t-147.5 59t-147.5 -59t-56.5 -153t56.5 -153t147.5 -59z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="704" 
d="M493 0l-149 230h-120v-230h-164v700h350q111 0 179.5 -71.5t68.5 -164.5q0 -69 -37.5 -127t-105.5 -86l169 -251h-191zM224 552v-177h174q43 0 71 26t28 63q0 36 -27 62t-65 26h-181z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="671" 
d="M354 -14q-124 0 -210 61.5t-119 150.5l158 49q21 -52 68 -84t113 -32q49 0 79 19.5t30 46.5q0 43 -75 67l-180 56q-73 23 -117 71t-43 114q-1 87 73 148t186 61q111 0 195.5 -53.5t113.5 -130.5l-153 -45q-21 37 -62.5 60.5t-91.5 23.5q-42 0 -67.5 -16.5t-25.5 -41.5
q0 -36 47 -50l176 -56q92 -29 142 -72.5t50 -124.5q0 -95 -79.5 -158.5t-207.5 -63.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="642" 
d="M627 700v-150h-223v-550h-166v550h-223v150h612z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="732" 
d="M509 700h168v-409q0 -138 -82 -221.5t-229 -83.5q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="728" 
d="M545 700h183l-273 -700h-182l-273 700h184l180 -487z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1009" 
d="M834 700h175l-209 -700h-176l-119 416l-120 -416h-176l-209 700h175l126 -462l129 462h149l129 -462z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="720" 
d="M715 0h-200l-155 224l-155 -224h-200l246 352l-240 348h200l149 -219l149 219h200l-240 -348z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="691" 
d="M505 700h186l-261 -448v-252h-168v252l-262 448h187l159 -286z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="638" 
d="M261 147h347v-147h-578v111l340 442h-335v147h563v-113z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="350" 
d="M330 605h-115v-600h115v-140h-270v880h270v-140z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="569" 
d="M544 -91h-123l-396 881h123z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="350" 
d="M290 745v-880h-270v140h115v600h-115v140h270z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="605" 
d="M575 380h-159l-113 206l-114 -206h-159l194 350h158z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="620" 
d="M35 -160v120h550v-120h-550z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="249" 
d="M249 588h-135l-114 142h174z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="645" 
d="M278 -14q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510h-160v47q-58 -61 -147 -61zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="645" 
d="M367 524q104 0 176 -77t72 -192t-72 -192t-176 -77q-89 0 -147 61v-47h-160v730h160v-267q56 61 147 61zM338 128q52 0 85.5 35.5t33.5 92.5q0 56 -33.5 91t-85.5 35q-53 0 -87.5 -35t-34.5 -91q0 -57 34.5 -92.5t87.5 -35.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="581" 
d="M303 -14q-118 0 -195.5 77t-77.5 192t77 192t194 77q102 0 170.5 -53t84.5 -120l-148 -42q-7 30 -36 52t-70 22q-52 0 -84 -37t-32 -90t32 -91t84 -38q41 0 70 21.5t36 50.5l148 -42q-16 -67 -84 -119t-169 -52z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="645" 
d="M425 730h160v-730h-160v47q-58 -61 -147 -61q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v267zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="597" 
d="M310 -14q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-24 -55 -90 -99t-159 -44zM186 303h225q-4 36 -34.5 61.5t-77.5 25.5t-77 -26t-36 -61z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="417" 
d="M268 546v-36h140v-140h-140v-370h-160v370h-93v140h93v36q0 92 49 145t140 53q71 0 120 -22l-38 -127q-26 11 -57 11q-25 0 -39.5 -16t-14.5 -44z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="640" 
d="M420 510h160v-491q0 -108 -73 -175.5t-195 -67.5q-174 0 -247 133l138 50q34 -53 113 -53q51 0 77.5 31t26.5 81v49q-56 -61 -141 -61q-108 0 -178.5 74.5t-70.5 184.5q0 116 70 187.5t179 71.5q85 0 141 -61v47zM307 149q53 0 85 32t32 84t-32 84t-85 32t-86 -32
t-33 -84t33 -84t86 -32z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="625" 
d="M371 524q99 0 149 -61t50 -166v-297h-160v265q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v730h160v-273q58 67 151 67z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="278" 
d="M139 569q-38 0 -65 26.5t-27 64.5q0 37 27 63t65 26t65 -26t27 -63q0 -38 -27 -64.5t-65 -26.5zM57 0v510h160v-510h-160z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="280" 
d="M141 569q-38 0 -65 26.5t-27 64.5q0 37 27 63t65 26t65 -26t27 -63q0 -38 -27 -64.5t-65 -26.5zM59 -15v525h160v-510q0 -106 -46.5 -165t-141.5 -59q-50 0 -101 22l35 122q18 -10 39 -10q55 0 55 75z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="574" 
d="M394 0l-125 177l-49 -56v-121h-160v730h160v-407l153 187h184l-189 -218l206 -292h-180z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="280" 
d="M60 0v730h160v-730h-160z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="935" 
d="M677 524q93 0 148 -58t55 -165v-301h-160v265q0 111 -81 111q-89 0 -89 -123v-253h-160v265q0 111 -81 111q-89 0 -89 -123v-253h-160v510h160v-50q55 64 141 64q101 0 148 -68q64 68 168 68z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="625" 
d="M371 524q99 0 149 -61t50 -166v-297h-160v265q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v510h160v-53q58 67 151 67z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="608" 
d="M304 -14q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77t196.5 -77t77.5 -192t-77.5 -192t-196.5 -77zM304 130q54 0 86.5 35t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="645" 
d="M367 524q104 0 176 -77t72 -192t-72 -192t-176 -77q-89 0 -147 61v-257h-160v720h160v-47q56 61 147 61zM338 128q52 0 85.5 35.5t33.5 92.5q0 56 -33.5 91t-85.5 35q-53 0 -87.5 -35t-34.5 -91q0 -57 34.5 -92.5t87.5 -35.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="645" 
d="M425 510h160v-720h-160v257q-58 -61 -147 -61q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="400" 
d="M220 441q23 42 64 62.5t96 13.5v-150q-82 14 -121 -23t-39 -131v-213h-160v510h160v-69z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="524" 
d="M267 -14q-94 0 -164.5 46.5t-90.5 111.5l143 42q13 -33 44 -53.5t74 -20.5q29 0 46.5 11t17.5 26q0 25 -48 39l-122 36q-129 39 -129 142q0 67 62.5 112.5t149.5 45.5q82 0 144 -35t87 -95l-140 -40q-34 48 -92 48q-22 0 -38 -9t-16 -23q0 -20 29 -30l118 -33
q35 -10 58.5 -20t47 -27t35 -43t11.5 -61q0 -74 -63.5 -122t-163.5 -48z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="414" 
d="M356 144l38 -127q-63 -25 -125 -25q-90 0 -135 51t-45 141v186h-79v140h79v160h160v-160h137v-140h-137v-186q0 -53 53 -53q29 0 54 13z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="625" 
d="M405 510h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="592" 
d="M417 510h175l-206 -510h-180l-206 510h176l120 -328z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="833" 
d="M668 510h165l-176 -510h-148l-92 286l-93 -286h-148l-176 510h165l93 -306l92 306h133l93 -306z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="558" 
d="M558 0h-180l-99 139l-99 -139h-180l184 257l-176 253h179l92 -132l92 132h179l-176 -253z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="604" 
d="M433 510h171l-303 -720h-163l91 222l7 1l-7 -1l-229 498h175l133 -311z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="537" 
d="M249 131h253v-131h-464v100l243 279h-237v131h449v-100z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="355" 
d="M265 426q0 -38 -17 -72t-56 -49q39 -15 56 -49t17 -72v-124q0 -33 10.5 -46.5t39.5 -13.5h20v-140h-63q-162 0 -162 170v154q0 22 -14.5 36.5t-36.5 14.5h-19v140h19q22 0 36.5 14.5t14.5 36.5v154q0 170 162 170h63v-140h-20q-29 0 -39.5 -13.5t-10.5 -46.5v-124z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="250" 
d="M55 -125v930h140v-930h-140z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="355" 
d="M296 375h19v-140h-19q-22 0 -36.5 -14.5t-14.5 -36.5v-154q0 -170 -162 -170h-63v140h20q29 0 39.5 13.5t10.5 46.5v124q0 38 17 72t56 49q-39 15 -56 49t-17 72v124q0 33 -10.5 46.5t-39.5 13.5h-20v140h63q162 0 162 -170v-154q0 -22 14.5 -36.5t36.5 -14.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="586" 
d="M187 252l-142 41q0 63 33 109t100 46q47 0 110.5 -28t74.5 -28q36 0 36 56l142 -41q0 -64 -32.5 -109.5t-98.5 -45.5q-47 0 -111 28t-75 28q-37 0 -37 -56z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="269" 
d="M134 518q40 0 67.5 -27t27.5 -67q0 -38 -27.5 -64.5t-67.5 -26.5q-39 0 -66.5 26.5t-27.5 64.5q0 40 27.5 67t66.5 27zM199 265l21 -455h-172l21 455h130z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="576" 
d="M302 222q41 0 70 21.5t36 50.5l148 -42q-13 -54 -63 -101t-125 -63v-104h-135v105q-91 22 -147 93.5t-56 167.5q0 97 56 168.5t147 92.5v104h135v-103q76 -16 125.5 -63.5t62.5 -102.5l-148 -42q-7 30 -36 52t-70 22q-52 0 -84 -37t-32 -90t32 -91t84 -38z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="638" 
d="M269 154h339v-154h-568v154h65v118h-53v131h53v87q0 107 65.5 165.5t174.5 58.5q92 0 155 -56t72 -133l-149 -43q-17 84 -82 84q-34 0 -53 -23t-19 -63v-77h223v-131h-223v-118z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="628" 
d="M547 320q0 -64 -29 -116l85 -84l-89 -89l-83 84q-54 -30 -117 -30t-117 30l-83 -84l-89 89l85 84q-29 52 -29 116t29 116l-85 84l89 89l83 -84q54 30 117 30q66 0 117 -30l83 84l89 -89l-85 -84q29 -52 29 -116zM313 220q43 0 72 28.5t30 71.5q-1 43 -30 71.5t-72 28.5
q-42 0 -70.5 -28.5t-28.5 -71.5t28.5 -71.5t70.5 -28.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="701" 
d="M696 700l-210 -360h129v-85h-178l-2 -3v-50h180v-85h-180v-117h-168v117h-172v85h172v50l-2 3h-170v85h121l-211 360h187l159 -286l159 286h186z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="250" 
d="M55 445v360h140v-360h-140zM55 -125v360h140v-360h-140z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="564" 
d="M460 225q42 -39 42 -105q0 -75 -63 -124t-161 -49q-90 0 -156 45t-82 106l138 42q29 -70 108 -67q27 1 44 13.5t17 26.5q0 24 -47 38l-124 38q-132 40 -132 146q0 71 73 114q-45 40 -45 104q0 69 60.5 115t145.5 46q79 0 138.5 -34.5t82.5 -93.5l-140 -40q-27 44 -81 45
q-21 1 -37.5 -9t-16.5 -25q0 -21 31 -30l118 -35q72 -21 111.5 -55t39.5 -99q0 -76 -64 -113zM244 305l96 -26q29 19 29 48q0 26 -49 42l-81 26q-40 -16 -40 -45q0 -33 45 -45z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="353" 
d="M77 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM275 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="737" 
d="M369 14q-144 0 -241.5 97t-97.5 241q0 141 97.5 237.5t241.5 96.5t241 -96.5t97 -237.5q0 -144 -97 -241t-241 -97zM369 90q111 0 186 75t75 187q0 111 -75 184.5t-186 73.5t-186.5 -73.5t-75.5 -184.5q0 -112 75.5 -187t186.5 -75zM373 177q-76 0 -125.5 49.5
t-49.5 123.5t49.5 123t123.5 49q66 0 109 -33.5t54 -77.5l-77 -19q-6 23 -29.5 39.5t-55.5 16.5q-42 0 -68 -28t-26 -70q0 -41 26 -70t68 -29q32 0 55.5 16.5t29.5 39.5l77 -20q-11 -43 -53.5 -76.5t-107.5 -33.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="565" 
d="M300 510l-100 -190l100 -190h-140l-105 190l105 190h140zM510 510l-100 -190l100 -190h-140l-105 190l105 190h140z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="590" 
d="M535 465v-270h-135v145h-345v125h480z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="737" 
d="M369 686q143 0 240.5 -96.5t97.5 -239.5t-97.5 -239.5t-240.5 -96.5q-144 0 -241.5 96.5t-97.5 239.5t97.5 239.5t241.5 96.5zM369 89q111 0 185.5 75t74.5 186q0 112 -74.5 186.5t-185.5 74.5t-186 -74.5t-75 -186.5q0 -111 75 -186t186 -75zM519 404q0 -32 -18 -59.5
t-50 -41.5l75 -118h-90l-65 108h-58v-108h-78v332h166q53 0 85.5 -34.5t32.5 -78.5zM313 445v-83h82q21 0 34 12t13 30q0 17 -12.5 29t-30.5 12h-86z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="300" 
d="M0 588v110h300v-110h-300z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="410" 
d="M206 404q-75 0 -125.5 49t-50.5 121t50.5 121t125.5 49q74 0 124 -49t50 -121q0 -71 -50.5 -120.5t-123.5 -49.5zM206 505q29 0 49 20t20 49t-20 49t-49 20q-32 0 -51.5 -19.5t-19.5 -49.5q0 -29 19.5 -49t51.5 -20z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="560" 
d="M343 502h162v-125h-162v-140h-125v140h-163v125h163v141h125v-141zM55 57v125h450v-125h-450z" />
    <glyph glyph-name="two.sups" unicode="&#xb2;" horiz-adv-x="357" 
d="M198 552h139v-102h-320v73l156 145q37 36 37 58q0 13 -9.5 22t-26.5 9q-39 0 -52 -61l-107 31q6 55 49.5 94.5t107.5 39.5q73 0 116 -34.5t43 -96.5q0 -34 -14 -59.5t-46 -53.5z" />
    <glyph glyph-name="three.sups" unicode="&#xb3;" horiz-adv-x="363" 
d="M257 699q39 -12 62.5 -43t23.5 -76q0 -60 -44.5 -100.5t-111.5 -40.5q-78 0 -120.5 38.5t-51.5 94.5l103 30q5 -29 22.5 -46t43.5 -17q22 0 34.5 11.5t12.5 30.5t-13 32t-40 13q-18 0 -36 -3l-17 62l61 66h-156v99h299v-80z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="249" 
d="M0 588l75 142h174l-114 -142h-135z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="751" 
d="M706 700v-152h-83v-758h-145v758h-80v-758h-150v465q-96 0 -159.5 63.5t-63.5 157.5q0 95 66.5 159.5t176.5 64.5h438z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="269" 
d="M135 197q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="229" 
d="M108 -224q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l29 92h111l-17 -53q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25z" />
    <glyph glyph-name="one.sups" unicode="&#xb9;" horiz-adv-x="287" 
d="M135 850h102v-400h-119v268l-89 -41l-29 102z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="565" 
d="M195 510l105 -190l-105 -190h-140l100 190l-100 190h140zM405 510l105 -190l-105 -190h-140l100 190l-100 190h140z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="820" 
d="M118 300v268l-89 -41l-29 102l135 71h102v-400h-119zM539 700h105l-432 -700h-105zM810 169v-100h-44v-69h-112v69h-185v89l128 242h123l-119 -231h53v55h112v-55h44z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="821" 
d="M118 300v268l-89 -41l-29 102l135 71h102v-400h-119zM539 700h105l-432 -700h-105zM662 102h139v-102h-320v73l156 145q37 36 37 58q0 13 -9.5 22t-26.5 9q-39 0 -52 -61l-107 31q6 55 49.5 94.5t107.5 39.5q73 0 116 -34.5t43 -96.5q0 -34 -14 -59.5t-46 -53.5z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="896" 
d="M343 430q0 -60 -44.5 -100.5t-111.5 -40.5q-78 0 -120.5 38.5t-51.5 94.5l103 30q5 -29 22.5 -46t43.5 -17q22 0 34.5 11.5t12.5 30.5t-13 32t-40 13q-18 0 -36 -3l-17 62l61 66h-156v99h299v-80l-72 -71q39 -12 62.5 -43t23.5 -76zM615 700h105l-432 -700h-105zM886 169
v-100h-44v-69h-112v69h-185v89l128 242h123l-119 -231h53v55h112v-55h44z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="555" 
d="M282 518q40 0 67.5 -27t27.5 -67q0 -38 -27.5 -64.5t-67.5 -26.5q-39 0 -66.5 26.5t-27.5 64.5q0 40 27.5 67t66.5 27zM360 265q0 -38 -17 -71t-41 -55.5l-48 -43.5t-41 -43.5t-17 -45.5q0 -29 22 -46.5t59 -17.5q41 0 69.5 30.5t39.5 80.5l144 -44q-11 -89 -78.5 -151
t-175.5 -62t-177 57t-69 145q0 42 18 78.5t43.5 61t51 45.5t44 42t18.5 40h155z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" 
d="M448 778h-135l-114 142h174zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" 
d="M537 920l-114 -142h-135l75 142h174zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" 
d="M368 840l-44 -70h-140l109 150h150l109 -150h-140zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" 
d="M315 807q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156q-32 0 -73.5 23.5t-50.5 23.5zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" 
d="M268 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM466 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700
h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" 
d="M368 732q-56 0 -93 36t-37 90t37 90t93 36t93 -36t37 -90t-37 -90t-93 -36zM368 900q-19 0 -31 -12t-12 -30q0 -19 12 -30.5t31 -11.5t31 11.5t12 30.5q0 18 -12 30t-31 12zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1001" 
d="M961 550h-322v-122h271v-145h-271v-133h321v-150h-480v136h-222l-79 -136h-179l409 700h552v-150zM343 284h137v236z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="773" 
d="M401 142q69 0 117.5 34.5t62.5 82.5l162 -41q-20 -80 -93 -146t-185 -82l-14 -43q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l26 81q-141 17 -231 117.5t-90 243.5q0 155 105 259.5t262 104.5
q139 0 229.5 -71t113.5 -164l-162 -40q-14 48 -63 83.5t-117 35.5q-89 0 -143.5 -60t-54.5 -147q0 -86 55 -147.5t143 -61.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="627" 
d="M401 778h-135l-114 142h174zM587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700h527v-150z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="627" 
d="M490 920l-114 -142h-135l75 142h174zM587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700h527v-150z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="627" 
d="M321 840l-44 -70h-140l109 150h150l109 -150h-140zM587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700h527v-150z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="627" 
d="M221 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM419 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700
h527v-150z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="288" 
d="M224 778h-135l-114 142h174zM60 0v700h168v-700h-168z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="288" 
d="M64 778l75 142h174l-114 -142h-135zM60 0v700h168v-700h-168z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="288" 
d="M144 840l-44 -70h-140l109 150h150l109 -150h-140zM60 0v700h168v-700h-168z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="288" 
d="M44 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM242 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM60 0v700h168v-700h-168z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="777" 
d="M413 700q148 0 239.5 -97t91.5 -253q0 -154 -91 -252t-240 -98h-323v275h-70v140h70v285h323zM409 149q72 0 119.5 56.5t47.5 147.5q0 90 -47.5 144t-119.5 54h-155v-136h146v-140h-146v-126h155z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" 
d="M315 807q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156q-32 0 -73.5 23.5t-50.5 23.5zM510 700h166v-700h-135l-315 401v-401h-166v700h135l315 -401v401z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="808" 
d="M484 778h-135l-114 142h174zM404 -14q-160 0 -265.5 104t-105.5 260t105.5 260t265.5 104t265.5 -104t105.5 -260t-105.5 -260t-265.5 -104zM404 139q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="808" 
d="M324 778l75 142h174l-114 -142h-135zM404 -14q-160 0 -265.5 104t-105.5 260t105.5 260t265.5 104t265.5 -104t105.5 -260t-105.5 -260t-265.5 -104zM404 139q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="808" 
d="M404 840l-44 -70h-140l109 150h150l109 -150h-140zM404 714q160 0 265.5 -104t105.5 -260t-105.5 -260t-265.5 -104t-265.5 104t-105.5 260t105.5 260t265.5 104zM404 139q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="808" 
d="M475 760q-32 0 -73.5 23.5t-50.5 23.5q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156zM404 -14q-160 0 -265.5 104t-105.5 260t105.5 260t265.5 104t265.5 -104t105.5 -260t-105.5 -260t-265.5 -104zM404 139
q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="808" 
d="M304 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM502 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM404 -14q-160 0 -265.5 104t-105.5 260t105.5 260t265.5 104
t265.5 -104t105.5 -260t-105.5 -260t-265.5 -104zM404 139q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="531" 
d="M476 473l-122 -123l121 -121l-88 -89l-121 122l-122 -122l-88 89l121 121l-122 123l89 88l122 -122l121 122z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="808" 
d="M650 627q125 -106 125 -277q0 -156 -105.5 -260t-265.5 -104q-97 0 -175 40l-29 -41h-105l62 88q-59 50 -91.5 122t-32.5 155q0 156 105.5 260t265.5 104q95 0 175 -40l28 41h105zM201 350q0 -84 49 -144l238 339q-40 16 -84 16q-91 0 -147 -60t-56 -151zM404 139
q91 0 147 60t56 151q0 86 -50 144l-237 -339q40 -16 84 -16z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="732" 
d="M446 778h-135l-114 142h174zM509 700h168v-409q0 -138 -82 -221.5t-229 -83.5q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="732" 
d="M535 920l-114 -142h-135l75 142h174zM509 700h168v-409q0 -138 -82 -221.5t-229 -83.5q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="732" 
d="M366 840l-44 -70h-140l109 150h150l109 -150h-140zM509 700h168v-409q0 -138 -82 -221.5t-229 -83.5q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="732" 
d="M266 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM464 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM509 700h168v-409q0 -138 -82 -221.5t-229 -83.5
q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="691" 
d="M515 920l-114 -142h-135l75 142h174zM505 700h186l-261 -448v-252h-168v252l-262 448h187l159 -286z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="663" 
d="M397 611q112 0 181.5 -77t69.5 -175q0 -99 -70.5 -175.5t-187.5 -76.5h-166v-107h-164v700h164v-89h173zM386 255q44 0 72.5 31t28.5 72t-27 72t-67 31h-169v-206h162z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="618" 
d="M480 394q108 -57 108 -176q0 -103 -67.5 -160.5t-187.5 -57.5h-50v140h44q48 0 74.5 22t26.5 63q0 85 -106 85h-34v127h15q40 0 65 23t25 64q0 35 -22.5 56.5t-60.5 21.5q-44 0 -69.5 -33t-25.5 -87v-482h-160v479q0 117 69 190t183 73q106 0 175.5 -58.5t69.5 -150.5
q0 -91 -72 -139z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="645" 
d="M394 588h-135l-114 142h174zM278 -14q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510h-160v47q-58 -61 -147 -61zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="645" 
d="M234 588l75 142h174l-114 -142h-135zM278 -14q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510h-160v47q-58 -61 -147 -61zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="645" 
d="M314 650l-44 -70h-140l109 150h150l109 -150h-140zM425 510h160v-510h-160v47q-58 -61 -147 -61q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5
t85.5 -35.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="645" 
d="M385 570q-32 0 -73.5 23.5t-50.5 23.5q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156zM278 -14q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510h-160v47q-58 -61 -147 -61zM307 128q53 0 87.5 35.5
t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="645" 
d="M214 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM412 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM278 -14q-104 0 -176 77t-72 192t72 192t176 77
q91 0 147 -61v47h160v-510h-160v47q-58 -61 -147 -61zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="645" 
d="M314 542q-56 0 -93 36t-37 90t37 90t93 36t93 -36t37 -90t-37 -90t-93 -36zM314 626q19 0 31 11.5t12 30.5q0 18 -12 30t-31 12t-31 -12t-12 -30q0 -19 12 -30.5t31 -11.5zM278 -14q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510h-160v47
q-58 -61 -147 -61zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="997" 
d="M967 260q0 -30 -5 -62h-375q10 -38 42 -60t81 -22q62 0 89 42l143 -43q-30 -58 -87 -93.5t-132 -35.5q-101 0 -158 64v-50h-140v47q-58 -61 -147 -61q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h140v-54q58 68 154 68q109 0 178.5 -73.5t69.5 -190.5z
M584 302h229q-5 39 -34 64t-76 25q-48 0 -80 -26t-39 -63zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="581" 
d="M302 127q41 0 70 21.5t36 50.5l148 -42q-13 -55 -63.5 -102t-127.5 -63l-14 -45q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l26 82q-99 16 -161.5 89.5t-62.5 175.5q0 115 77 192t194 77
q102 0 170.5 -53t84.5 -120l-148 -42q-7 30 -36 52t-70 22q-52 0 -84 -37t-32 -90t32 -91t84 -38z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="597" 
d="M381 588h-135l-114 142h174zM310 -14q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-24 -55 -90 -99t-159 -44zM186 303h225q-4 36 -34.5 61.5t-77.5 25.5t-77 -26t-36 -61
z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="597" 
d="M221 588l75 142h174l-114 -142h-135zM310 -14q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-24 -55 -90 -99t-159 -44zM186 303h225q-4 36 -34.5 61.5t-77.5 25.5t-77 -26
t-36 -61z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="597" 
d="M301 650l-44 -70h-140l109 150h150l109 -150h-140zM567 260q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-24 -55 -90 -99t-159 -44q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189zM186 303h225q-4 36 -34.5 61.5
t-77.5 25.5t-77 -26t-36 -61z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="597" 
d="M201 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM399 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM310 -14q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5
t195.5 76.5q123 0 195 -75t72 -189q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-24 -55 -90 -99t-159 -44zM186 303h225q-4 36 -34.5 61.5t-77.5 25.5t-77 -26t-36 -61z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="280" 
d="M220 588h-135l-114 142h174zM60 0v510h160v-510h-160z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="280" 
d="M60 588l75 142h174l-114 -142h-135zM60 0v510h160v-510h-160z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="280" 
d="M140 650l-44 -70h-140l109 150h150l109 -150h-140zM60 0v510h160v-510h-160z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="280" 
d="M46 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM233 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM60 0v510h160v-510h-160z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="603" 
d="M435 578q138 -152 138 -323q0 -120 -76 -194.5t-194 -74.5q-117 0 -195 72t-78 180q0 106 74.5 173t185.5 67q34 0 59 -8q-29 38 -60 71l-172 -43l-20 75l127 32q-54 47 -123 95h202q10 -8 68 -58l145 36l20 -75zM303 132q49 0 78.5 29.5t29.5 75.5q0 45 -29.5 74.5
t-78.5 29.5t-78.5 -29.5t-29.5 -74.5q0 -46 29.5 -75.5t78.5 -29.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="625" 
d="M265 617q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156q-32 0 -73.5 23.5t-50.5 23.5zM371 524q99 0 149 -61t50 -166v-297h-160v265q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v510h160v-53q58 67 151 67z
" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="608" 
d="M384 588h-135l-114 142h174zM304 -14q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77t196.5 -77t77.5 -192t-77.5 -192t-196.5 -77zM304 130q54 0 86.5 35t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="608" 
d="M224 588l75 142h174l-114 -142h-135zM304 -14q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77t196.5 -77t77.5 -192t-77.5 -192t-196.5 -77zM304 130q54 0 86.5 35t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="608" 
d="M304 650l-44 -70h-140l109 150h150l109 -150h-140zM304 524q119 0 196.5 -77t77.5 -192t-77.5 -192t-196.5 -77t-196.5 77t-77.5 192t77.5 192t196.5 77zM304 130q54 0 86.5 35t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="608" 
d="M375 570q-32 0 -73.5 23.5t-50.5 23.5q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156zM304 -14q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77t196.5 -77t77.5 -192t-77.5 -192t-196.5 -77zM304 130q54 0 86.5 35
t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="608" 
d="M204 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM402 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM304 -14q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77
t196.5 -77t77.5 -192t-77.5 -192t-196.5 -77zM304 130q54 0 86.5 35t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="580" 
d="M290 463q-31 0 -53 22t-22 53q0 29 22 50.5t53 21.5t53 -21.5t22 -50.5q0 -31 -22 -53t-53 -22zM55 288v125h470v-125h-470zM290 91q-31 0 -53 22t-22 53q0 29 22 50.5t53 21.5t53 -21.5t22 -50.5q0 -31 -22 -53t-53 -22z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="608" 
d="M490 457q88 -78 88 -202q0 -115 -77.5 -192t-196.5 -77q-67 0 -124 27l-20 -28h-90l48 68q-88 78 -88 202q0 115 77.5 192t196.5 77q67 0 124 -27l20 28h90zM185 255q0 -44 21 -76l137 195q-18 6 -39 6q-54 0 -86.5 -35t-32.5 -90zM304 130q54 0 86.5 35t32.5 90
q0 45 -21 76l-137 -195q18 -6 39 -6z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="625" 
d="M392 588h-135l-114 142h174zM405 510h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="625" 
d="M481 730l-114 -142h-135l75 142h174zM405 510h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="625" 
d="M312 650l-44 -70h-140l109 150h150l109 -150h-140zM405 510h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="625" 
d="M212 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM410 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM405 510h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61
t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="604" 
d="M475 730l-114 -142h-135l75 142h174zM433 510h171l-303 -720h-163l91 222l7 1l-7 -1l-229 498h175l133 -311z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="645" 
d="M367 524q104 0 176 -77t72 -192t-72 -192t-176 -77q-89 0 -147 61v-257h-160v940h160v-267q56 61 147 61zM338 128q52 0 85.5 35.5t33.5 92.5q0 56 -33.5 91t-85.5 35q-53 0 -87.5 -35t-34.5 -91q0 -57 34.5 -92.5t87.5 -35.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="604" 
d="M206 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM404 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM433 510h171l-303 -720h-163l91 222l7 1l-7 -1l-229 498h175
l133 -311z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" 
d="M518 888v-110h-300v110h300zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="645" 
d="M164 588v110h300v-110h-300zM278 -14q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510h-160v47q-58 -61 -147 -61zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" 
d="M368 757q-86 0 -130.5 43.5t-44.5 110.5h121q0 -59 54 -59t54 59h121q0 -67 -44.5 -110.5t-130.5 -43.5zM565 0l-49 134h-296l-49 -134h-171l259 700h218l259 -700h-171zM273 279h190l-95 262z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="645" 
d="M314 567q-86 0 -130.5 43.5t-44.5 110.5h121q0 -59 54 -59t54 59h121q0 -67 -44.5 -110.5t-130.5 -43.5zM278 -14q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510h-160v47q-58 -61 -147 -61zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35
q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" 
d="M752 -93l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 65 73 123h-58l-49 134h-296l-49 -134h-171l259 700h218l259 -700q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29zM273 279h190l-95 262z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="645" 
d="M601 -93l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 65 73 123h-47v47q-58 -61 -147 -61q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v47h160v-510q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29zM307 128q53 0 87.5 35.5t34.5 92.5
q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="773" 
d="M321 778l75 142h174l-114 -142h-135zM403 -14q-159 0 -264.5 104t-105.5 260q0 155 105 259.5t262 104.5q139 0 229.5 -71t113.5 -164l-162 -40q-14 48 -63 83.5t-117 35.5q-89 0 -143.5 -60t-54.5 -147q0 -86 55 -147.5t143 -61.5q69 0 117.5 34.5t62.5 82.5l162 -41
q-23 -92 -113 -162t-227 -70z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="581" 
d="M221 588l75 142h174l-114 -142h-135zM303 -14q-118 0 -195.5 77t-77.5 192t77 192t194 77q102 0 170.5 -53t84.5 -120l-148 -42q-7 30 -36 52t-70 22q-52 0 -84 -37t-32 -90t32 -91t84 -38q41 0 70 21.5t36 50.5l148 -42q-16 -67 -84 -119t-169 -52z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="773" 
d="M401 757q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM403 -14q-159 0 -264.5 104t-105.5 260q0 155 105 259.5t262 104.5q139 0 229.5 -71t113.5 -164l-162 -40q-14 48 -63 83.5t-117 35.5q-89 0 -143.5 -60t-54.5 -147
q0 -86 55 -147.5t143 -61.5q69 0 117.5 34.5t62.5 82.5l162 -41q-23 -92 -113 -162t-227 -70z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="581" 
d="M301 567q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM303 -14q-118 0 -195.5 77t-77.5 192t77 192t194 77q102 0 170.5 -53t84.5 -120l-148 -42q-7 30 -36 52t-70 22q-52 0 -84 -37t-32 -90t32 -91t84 -38q41 0 70 21.5
t36 50.5l148 -42q-16 -67 -84 -119t-169 -52z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="773" 
d="M326 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM401 142q69 0 117.5 34.5t62.5 82.5l162 -41q-23 -92 -113 -162t-227 -70q-159 0 -264.5 104t-105.5 260q0 155 105 259.5t262 104.5q139 0 229.5 -71t113.5 -164l-162 -40q-14 48 -63 83.5t-117 35.5
q-89 0 -143.5 -60t-54.5 -147q0 -86 55 -147.5t143 -61.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="581" 
d="M226 580l-109 150h140l44 -70l44 70h140l-109 -150h-150zM302 127q41 0 70 21.5t36 50.5l148 -42q-16 -67 -84 -119t-169 -52q-118 0 -195.5 77t-77.5 192t77 192t194 77q102 0 170.5 -53t84.5 -120l-148 -42q-7 30 -36 52t-70 22q-52 0 -84 -37t-32 -90t32 -91t84 -38z
" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="747" 
d="M275 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM383 700q148 0 239.5 -97t91.5 -253q0 -154 -91 -252t-240 -98h-323v700h323zM379 149q72 0 119.5 56.5t47.5 147.5q0 90 -47.5 144t-119.5 54h-155v-402h155z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="782" 
d="M425 730h160v-730h-160v47q-58 -61 -147 -61q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v267zM649 730h143l-33 -212h-123zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="777" 
d="M413 700q148 0 239.5 -97t91.5 -253q0 -154 -91 -252t-240 -98h-323v275h-70v140h70v285h323zM409 149q72 0 119.5 56.5t47.5 147.5q0 90 -47.5 144t-119.5 54h-155v-136h146v-140h-146v-126h155z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="649" 
d="M649 655v-90h-64v-565h-160v47q-58 -61 -147 -61q-104 0 -176 77t-72 192t72 192t176 77q91 0 147 -61v102h-146v90h146v75h160v-75h64zM307 128q53 0 87.5 35.5t34.5 92.5q0 56 -34.5 91t-87.5 35q-52 0 -85.5 -35t-33.5 -91q0 -57 33.5 -92.5t85.5 -35.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="627" 
d="M471 888v-110h-300v110h300zM587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700h527v-150z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="597" 
d="M151 588v110h300v-110h-300zM310 -14q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-24 -55 -90 -99t-159 -44zM186 303h225q-4 36 -34.5 61.5t-77.5 25.5t-77 -26t-36 -61
z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="627" 
d="M321 757q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700h527v-150z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="597" 
d="M301 567q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM310 -14q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42
q-24 -55 -90 -99t-159 -44zM186 303h225q-4 36 -34.5 61.5t-77.5 25.5t-77 -26t-36 -61z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="627" 
d="M602 -93l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 65 73 123h-413v700h527v-150h-365v-122h314v-145h-314v-133h364v-150q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="597" 
d="M567 260q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-25 -56 -84 -96q-41 -31 -66.5 -66t-25.5 -59q0 -30 28 -30q35 0 58 29l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 60 56 109h-13q-125 0 -202.5 74.5t-77.5 193.5
q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189zM186 303h225q-4 36 -34.5 61.5t-77.5 25.5t-77 -26t-36 -61z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="627" 
d="M246 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM587 550h-365v-122h314v-145h-314v-133h364v-150h-526v700h527v-150z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="597" 
d="M226 580l-109 150h140l44 -70l44 70h140l-109 -150h-150zM567 260q0 -30 -4 -53h-377q18 -88 127 -88q73 0 102 52l144 -42q-24 -55 -90 -99t-159 -44q-125 0 -202.5 74.5t-77.5 193.5q0 117 74.5 193.5t195.5 76.5q123 0 195 -75t72 -189zM186 303h225q-4 36 -34.5 61.5
t-77.5 25.5t-77 -26t-36 -61z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="799" 
d="M405 757q-86 0 -130.5 43.5t-44.5 110.5h121q0 -59 54 -59t54 59h121q0 -67 -44.5 -110.5t-130.5 -43.5zM766 398v-83q0 -144 -98 -236.5t-258 -92.5q-163 0 -270 104.5t-107 259.5t106 259.5t263 104.5q127 0 216.5 -62t124.5 -155l-154 -44q-55 105 -185 105
q-89 0 -146 -60t-57 -147t58.5 -149t163.5 -62q71 0 114.5 34t54.5 82l-184 -1v143h358z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="640" 
d="M315 567q-86 0 -130.5 43.5t-44.5 110.5h121q0 -59 54 -59t54 59h121q0 -67 -44.5 -110.5t-130.5 -43.5zM420 510h160v-491q0 -108 -73 -175.5t-195 -67.5q-174 0 -247 133l138 50q34 -53 113 -53q51 0 77.5 31t26.5 81v49q-56 -61 -141 -61q-108 0 -178.5 74.5
t-70.5 184.5q0 116 70 187.5t179 71.5q85 0 141 -61v47zM307 149q53 0 85 32t32 84t-32 84t-85 32t-86 -32t-33 -84t33 -84t86 -32z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="799" 
d="M405 757q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM766 398v-83q0 -144 -98 -236.5t-258 -92.5q-163 0 -270 104.5t-107 259.5t106 259.5t263 104.5q127 0 216.5 -62t124.5 -155l-154 -44q-55 105 -185 105
q-89 0 -146 -60t-57 -147t58.5 -149t163.5 -62q71 0 114.5 34t54.5 82l-184 -1v143h358z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="640" 
d="M315 567q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM420 510h160v-491q0 -108 -73 -175.5t-195 -67.5q-174 0 -247 133l138 50q34 -53 113 -53q51 0 77.5 31t26.5 81v49q-56 -61 -141 -61q-108 0 -178.5 74.5
t-70.5 184.5q0 116 70 187.5t179 71.5q85 0 141 -61v47zM307 149q53 0 85 32t32 84t-32 84t-85 32t-86 -32t-33 -84t33 -84t86 -32z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="799" 
d="M766 398v-83q0 -144 -98 -236.5t-258 -92.5q-163 0 -270 104.5t-107 259.5t106 259.5t263 104.5q127 0 216.5 -62t124.5 -155l-154 -44q-55 105 -185 105q-89 0 -146 -60t-57 -147t58.5 -149t163.5 -62q71 0 114.5 34t54.5 82l-184 -1v143h358zM328 -270l13 210h143
l-33 -210h-123z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="640" 
d="M398 804l-13 -210h-143l33 210h123zM420 510h160v-491q0 -108 -73 -175.5t-195 -67.5q-174 0 -247 133l138 50q34 -53 113 -53q51 0 77.5 31t26.5 81v49q-56 -61 -141 -61q-108 0 -178.5 74.5t-70.5 184.5q0 116 70 187.5t179 71.5q85 0 141 -61v47zM307 149q53 0 85 32
t32 84t-32 84t-85 32t-86 -32t-33 -84t33 -84t86 -32z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="760" 
d="M750 614v-100h-65v-514h-166v281h-276v-281h-166v514h-67v100h67v86h166v-86h276v86h166v-86h65zM519 431v83h-276v-83h276z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="629" 
d="M375 524q99 0 149 -61t50 -166v-297h-160v265q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v565h-64v90h64v75h160v-75h146v-90h-146v-108q58 67 151 67z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="288" 
d="M-6 778v110h300v-110h-300zM60 0v700h168v-700h-168z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="280" 
d="M-10 588v110h300v-110h-300zM60 0v510h160v-510h-160z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="288" 
d="M244 -93l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 65 73 123h-55v700h168v-700q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="280" 
d="M140 567q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM236 -93l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 65 73 123h-47v510h160v-510q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="288" 
d="M144 757q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM60 0v700h168v-700h-168z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="280" 
d="M60 0v510h160v-510h-160z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="715" 
d="M422 398l288 -398h-201l-196 275l-87 -98v-177h-166v700h166v-304l254 304h210zM267 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="574" 
d="M368 292l206 -292h-180l-125 177l-49 -56v-121h-160v730h160v-407l153 187h184zM220 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="575" 
d="M324 920l-114 -142h-135l75 142h174zM226 152h329v-152h-495v700h166v-548z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="280" 
d="M60 808l75 142h174l-114 -142h-135zM60 0v730h160v-730h-160z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="575" 
d="M226 152h329v-152h-495v700h166v-548zM222 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="280" 
d="M60 0v730h160v-730h-160zM55 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="575" 
d="M226 700v-548h329v-152h-495v700h166zM555 700l-32 -182h-125l10 182h147z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="419" 
d="M60 0v730h160v-730h-160zM273 518l13 212h143l-33 -212h-123z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="575" 
d="M226 152h329v-152h-495v233l-45 -19v140l45 19v327h166v-255l187 81v-140l-187 -81v-153z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="336" 
d="M326 535v-140l-78 -37v-353h-160v279l-78 -37v140l78 37v311h160v-237z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" 
d="M537 920l-114 -142h-135l75 142h174zM510 700h166v-700h-135l-315 401v-401h-166v700h135l315 -401v401z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="625" 
d="M487 730l-114 -142h-135l75 142h174zM371 524q99 0 149 -61t50 -166v-297h-160v265q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v510h160v-53q58 67 151 67z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" 
d="M510 700h166v-700h-135l-315 401v-401h-166v700h135l315 -401v401zM290 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="625" 
d="M371 524q99 0 149 -61t50 -166v-297h-160v265q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v510h160v-53q58 67 151 67zM235 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" 
d="M293 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM510 700h166v-700h-135l-315 401v-401h-166v700h135l315 -401v401z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="625" 
d="M243 580l-109 150h140l44 -70l44 70h140l-109 -150h-150zM371 524q99 0 149 -61t50 -166v-297h-160v265q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v510h160v-53q58 67 151 67z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" 
d="M510 700h166v-700q0 -101 -46.5 -156.5t-141.5 -55.5q-50 0 -101 22l35 122q18 -10 45 -10q44 0 44 65v51l-285 363v-401h-166v700h135l315 -401v401z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="625" 
d="M371 524q99 0 149 -61t50 -166v-297q0 -107 -46.5 -166t-141.5 -59q-51 0 -101 23l35 122q18 -10 39 -10q55 0 55 75v280q0 111 -91 111q-46 0 -72.5 -34t-26.5 -93v-249h-160v510h160v-53q58 67 151 67z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="808" 
d="M254 778v110h300v-110h-300zM404 -14q-160 0 -265.5 104t-105.5 260t105.5 260t265.5 104t265.5 -104t105.5 -260t-105.5 -260t-265.5 -104zM404 139q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="608" 
d="M154 588v110h300v-110h-300zM304 -14q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77t196.5 -77t77.5 -192t-77.5 -192t-196.5 -77zM304 130q54 0 86.5 35t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="808" 
d="M234 778l82 142h143l-87 -142h-138zM424 778l90 142h143l-95 -142h-138zM404 -14q-160 0 -265.5 104t-105.5 260t105.5 260t265.5 104t265.5 -104t105.5 -260t-105.5 -260t-265.5 -104zM404 139q91 0 147 60t56 151t-56 151t-147 60t-147 -60t-56 -151t56 -151t147 -60z
" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="608" 
d="M134 588l82 142h143l-87 -142h-138zM324 588l90 142h143l-95 -142h-138zM304 -14q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77t196.5 -77t77.5 -192t-77.5 -192t-196.5 -77zM304 130q54 0 86.5 35t32.5 90t-32.5 90t-86.5 35t-86.5 -35t-32.5 -90t32.5 -90t86.5 -35z
" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1008" 
d="M968 550h-322v-122h271v-145h-271v-133h321v-150h-595q-150 0 -244.5 98.5t-94.5 251.5q0 156 94 253t245 97h596v-150zM372 150h110v400h-110q-74 0 -122.5 -53.5t-48.5 -143.5t49 -146.5t122 -56.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="990" 
d="M960 260q0 -29 -5 -59h-373q10 -38 42.5 -61.5t79.5 -23.5q60 0 90 42l143 -43q-24 -52 -87.5 -90.5t-144.5 -38.5q-131 0 -206 88q-74 -88 -195 -88q-119 0 -196.5 77t-77.5 192t77.5 192t196.5 77q121 0 195 -88q73 88 196 88q124 0 194.5 -75t70.5 -189zM579 303h227
q-5 38 -34 63t-76 25t-78.5 -26t-38.5 -62zM304 130q53 0 86 36t33 89t-33 89t-86 36t-86 -36t-33 -89t33 -89t86 -36z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="704" 
d="M499 920l-114 -142h-135l75 142h174zM493 0l-149 230h-120v-230h-164v700h350q111 0 179.5 -71.5t68.5 -164.5q0 -69 -37.5 -127t-105.5 -86l169 -251h-191zM224 552v-177h174q43 0 71 26t28 63q0 36 -27 62t-65 26h-181z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="400" 
d="M378 730l-114 -142h-135l75 142h174zM220 441q23 42 64 62.5t96 13.5v-150q-82 14 -121 -23t-39 -131v-213h-160v510h160v-69z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="704" 
d="M515 251l169 -251h-191l-149 230h-120v-230h-164v700h350q111 0 179.5 -71.5t68.5 -164.5q0 -69 -37.5 -127t-105.5 -86zM224 552v-177h174q43 0 71 26t28 63q0 36 -27 62t-65 26h-181zM264 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="400" 
d="M220 441q23 42 64 62.5t96 13.5v-150q-82 14 -121 -23t-39 -131v-213h-160v510h160v-69zM56 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="704" 
d="M255 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM493 0l-149 230h-120v-230h-164v700h350q111 0 179.5 -71.5t68.5 -164.5q0 -69 -37.5 -127t-105.5 -86l169 -251h-191zM224 552v-177h174q43 0 71 26t28 63q0 36 -27 62t-65 26h-181z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="400" 
d="M134 580l-109 150h140l44 -70l44 70h140l-109 -150h-150zM220 441q23 42 64 62.5t96 13.5v-150q-82 14 -121 -23t-39 -131v-213h-160v510h160v-69z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="671" 
d="M253 778l75 142h174l-114 -142h-135zM354 -14q-124 0 -210 61.5t-119 150.5l158 49q21 -52 68 -84t113 -32q49 0 79 19.5t30 46.5q0 43 -75 67l-180 56q-73 23 -117 71t-43 114q-1 87 73 148t186 61q111 0 195.5 -53.5t113.5 -130.5l-153 -45q-21 37 -62.5 60.5
t-91.5 23.5q-42 0 -67.5 -16.5t-25.5 -41.5q0 -36 47 -50l176 -56q92 -29 142 -72.5t50 -124.5q0 -95 -79.5 -158.5t-207.5 -63.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="524" 
d="M175 588l75 142h174l-114 -142h-135zM267 -14q-94 0 -164.5 46.5t-90.5 111.5l143 42q13 -33 44 -53.5t74 -20.5q29 0 46.5 11t17.5 26q0 25 -48 39l-122 36q-129 39 -129 142q0 67 62.5 112.5t149.5 45.5q82 0 144 -35t87 -95l-140 -40q-34 48 -92 48q-22 0 -38 -9
t-16 -23q0 -20 29 -30l118 -33q35 -10 58.5 -20t47 -27t35 -43t11.5 -61q0 -74 -63.5 -122t-163.5 -48z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="671" 
d="M641 208q0 -85 -63.5 -145.5t-169.5 -73.5l-14 -42q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l26 82q-102 13 -172.5 71.5t-99.5 136.5l158 49q21 -52 68 -84t113 -32q49 0 79 19.5t30 46.5
q0 43 -75 67l-180 56q-73 23 -117 71t-43 114q-1 87 73 148t186 61q111 0 195.5 -53.5t113.5 -130.5l-153 -45q-21 37 -62.5 60.5t-91.5 23.5q-42 0 -67.5 -16.5t-25.5 -41.5q0 -36 47 -50l176 -56q92 -29 142 -72.5t50 -124.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="524" 
d="M494 156q0 -62 -45.5 -106.5t-122.5 -58.5l-14 -44q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l26 82q-76 12 -131 55.5t-72 98.5l143 42q13 -33 44 -53.5t74 -20.5q29 0 46.5 11t17.5 26
q0 25 -48 39l-122 36q-129 39 -129 142q0 67 62.5 112.5t149.5 45.5q82 0 144 -35t87 -95l-140 -40q-34 48 -92 48q-22 0 -38 -9t-16 -23q0 -20 29 -30l118 -33q35 -10 58.5 -20t47 -27t35 -43t11.5 -61z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="671" 
d="M258 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM449 405q92 -29 142 -72.5t50 -124.5q0 -95 -79.5 -158.5t-207.5 -63.5q-124 0 -210 61.5t-119 150.5l158 49q21 -52 68 -84t113 -32q49 0 79 19.5t30 46.5q0 43 -75 67l-180 56q-73 23 -117 71t-43 114
q-1 87 73 148t186 61q111 0 195.5 -53.5t113.5 -130.5l-153 -45q-21 37 -62.5 60.5t-91.5 23.5q-42 0 -67.5 -16.5t-25.5 -41.5q0 -36 47 -50z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="524" 
d="M180 580l-109 150h140l44 -70l44 70h140l-109 -150h-150zM342 307q35 -10 58.5 -20t47 -27t35 -43t11.5 -61q0 -74 -63.5 -122t-163.5 -48q-94 0 -164.5 46.5t-90.5 111.5l143 42q13 -33 44 -53.5t74 -20.5q29 0 46.5 11t17.5 26q0 25 -48 39l-122 36q-129 39 -129 142
q0 67 62.5 112.5t149.5 45.5q82 0 144 -35t87 -95l-140 -40q-34 48 -92 48q-22 0 -38 -9t-16 -23q0 -20 29 -30z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="642" 
d="M404 550v-550l-17 -53q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l29 92h-55v550h-223v150h612v-150h-223z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="414" 
d="M300 -53q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l29 92q-117 32 -117 184v186h-79v140h79v160h160v-160h137v-140h-137v-186q0 -53 53 -53q29 0 54 13l38 -127q-40 -16 -79 -22z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="642" 
d="M246 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM627 700v-150h-223v-550h-166v550h-223v150h612z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="443" 
d="M310 730h143l-32 -182h-121zM302 131q29 0 54 13l38 -127q-63 -25 -125 -25q-90 0 -135 51t-45 141v186h-79v140h79v160h160v-160h137v-140h-137v-186q0 -53 53 -53z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="642" 
d="M627 550h-223v-154h135v-130h-135v-266h-166v266h-139v130h139v154h-223v150h612v-150z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="417" 
d="M359 144l38 -127q-63 -25 -128 -25q-90 0 -135 51t-45 141v50h-72v100h72v66h-79v110h79v160h160v-160h140v-110h-140v-66h119v-100h-119v-50q0 -53 58 -53q29 0 52 13z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="732" 
d="M516 888v-110h-300v110h300zM509 700h168v-409q0 -138 -82 -221.5t-229 -83.5q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="625" 
d="M462 698v-110h-300v110h300zM405 510h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="732" 
d="M366 732q-56 0 -93 36t-37 90t37 90t93 36t93 -36t37 -90t-37 -90t-93 -36zM366 900q-19 0 -31 -12t-12 -30q0 -19 12 -30.5t31 -11.5t31 11.5t12 30.5q0 18 -12 30t-31 12zM509 700h168v-409q0 -138 -82 -221.5t-229 -83.5q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406
q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="625" 
d="M312 542q-56 0 -93 36t-37 90t37 90t93 36t93 -36t37 -90t-37 -90t-93 -36zM312 710q-19 0 -31 -12t-12 -30q0 -19 12 -30.5t31 -11.5t31 11.5t12 30.5q0 18 -12 30t-31 12zM405 510h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265
q0 -111 91 -111q46 0 72.5 34t26.5 93v249z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="732" 
d="M421 920l-87 -142h-138l82 142h143zM524 778h-138l90 142h143zM509 700h168v-409q0 -138 -82 -221.5t-229 -83.5q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43t36.5 113v406z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="625" 
d="M367 730l-87 -142h-138l82 142h143zM470 588h-138l90 142h143zM405 261v249h160v-510h-160v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="732" 
d="M509 700h168v-409q0 -167 -114 -249q-40 -31 -65.5 -67t-25.5 -59q0 -28 28 -28q35 0 58 29l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 51 48 101q-24 -2 -38 -2q-148 0 -229.5 83.5t-81.5 221.5v409h168v-406q0 -70 36.5 -113t106.5 -43t106.5 43
t36.5 113v406z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="625" 
d="M581 -93l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5q-48 0 -80 28t-32 73q0 65 73 123h-47v53q-58 -67 -151 -67q-99 0 -149 61t-50 166v297h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249h160v-510q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1009" 
d="M505 840l-44 -70h-140l109 150h150l109 -150h-140zM834 700h175l-209 -700h-176l-119 416l-120 -416h-176l-209 700h175l126 -462l129 462h149l129 -462z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="833" 
d="M417 650l-44 -70h-140l109 150h150l109 -150h-140zM668 510h165l-176 -510h-148l-92 286l-93 -286h-148l-176 510h165l93 -306l92 306h133l93 -306z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="691" 
d="M346 840l-44 -70h-140l109 150h150l109 -150h-140zM505 700h186l-261 -448v-252h-168v252l-262 448h187l159 -286z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="604" 
d="M306 650l-44 -70h-140l109 150h150l109 -150h-140zM433 510h171l-303 -720h-163l91 222l7 1l-7 -1l-229 498h175l133 -311z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="691" 
d="M246 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM444 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM505 700h186l-261 -448v-252h-168v252l-262 448h187
l159 -286z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="638" 
d="M487 920l-114 -142h-135l75 142h174zM261 147h347v-147h-578v111l340 442h-335v147h563v-113z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="537" 
d="M437 730l-114 -142h-135l75 142h174zM249 131h253v-131h-464v100l243 279h-237v131h449v-100z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="638" 
d="M318 757q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM261 147h347v-147h-578v111l340 442h-335v147h563v-113z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="537" 
d="M268 567q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM249 131h253v-131h-464v100l243 279h-237v131h449v-100z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="638" 
d="M243 770l-109 150h140l44 -70l44 70h140l-109 -150h-150zM261 147h347v-147h-578v111l340 442h-335v147h563v-113z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="537" 
d="M193 580l-109 150h140l44 -70l44 70h140l-109 -150h-150zM249 131h253v-131h-464v100l243 279h-237v131h449v-100z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="545" 
d="M406 745q83 0 129 -43l-59 -119q-25 28 -63 28q-56 0 -65 -76l-10 -82h126v-130h-143l-34 -269q-13 -100 -66.5 -154.5t-139.5 -54.5q-80 0 -126 49l56 112q25 -28 62 -28q58 0 67 76l34 269h-108v130h124l10 82q13 101 65.5 155.5t140.5 54.5z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="671" 
d="M354 -14q-124 0 -210 61.5t-119 150.5l158 49q21 -52 68 -84t113 -32q49 0 79 19.5t30 46.5q0 43 -75 67l-180 56q-73 23 -117 71t-43 114q-1 87 73 148t186 61q111 0 195.5 -53.5t113.5 -130.5l-153 -45q-21 37 -62.5 60.5t-91.5 23.5q-42 0 -67.5 -16.5t-25.5 -41.5
q0 -36 47 -50l176 -56q92 -29 142 -72.5t50 -124.5q0 -95 -79.5 -158.5t-207.5 -63.5zM265 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="524" 
d="M267 -14q-94 0 -164.5 46.5t-90.5 111.5l143 42q13 -33 44 -53.5t74 -20.5q29 0 46.5 11t17.5 26q0 25 -48 39l-122 36q-129 39 -129 142q0 67 62.5 112.5t149.5 45.5q82 0 144 -35t87 -95l-140 -40q-34 48 -92 48q-22 0 -38 -9t-16 -23q0 -20 29 -30l118 -33
q35 -10 58.5 -20t47 -27t35 -43t11.5 -61q0 -74 -63.5 -122t-163.5 -48zM183 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="642" 
d="M627 700v-150h-223v-550h-166v550h-223v150h612zM239 -272l13 212h143l-33 -212h-123z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="414" 
d="M394 17q-63 -25 -125 -25q-90 0 -135 51t-45 141v186h-79v140h79v160h160v-160h137v-140h-137v-186q0 -53 53 -53q29 0 54 13zM163 -272l13 212h143l-33 -212h-123z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="368" 
d="M368 580h-140l-44 70l-44 -70h-140l109 150h150z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="368" 
d="M228 730h140l-109 -150h-150l-109 150h140l44 -70z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="350" 
d="M175 567q-86 0 -130.5 43.5t-44.5 110.5h121q0 -59 54 -59t54 59h121q0 -67 -44.5 -110.5t-130.5 -43.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="174" 
d="M87 569q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="260" 
d="M130 542q-56 0 -93 36t-37 90t37 90t93 36t93 -36t37 -90t-37 -90t-93 -36zM130 626q19 0 31 11.5t12 30.5q0 18 -12 30t-31 12t-31 -12t-12 -30q0 -19 12 -30.5t31 -11.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="228" 
d="M112 -224q-48 0 -80 28t-32 73q0 65 73 123h113q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="350" 
d="M246 570q-32 0 -73.5 23.5t-50.5 23.5q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="423" 
d="M0 588l82 142h143l-87 -142h-138zM190 588l90 142h143l-95 -142h-138z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M0 588h-135l-114 142h174z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M-249 588l75 142h174l-114 -142h-135z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M0 580h-140l-44 70l-44 -70h-140l109 150h150z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-104 570q-32 0 -73.5 23.5t-50.5 23.5q-21 0 -21 -43h-101q0 156 104 156q32 0 73.5 -23.5t50.5 -23.5q21 0 21 43h101q0 -156 -104 -156z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-300 588v110h300v-110h-300z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M-175 567q-86 0 -130.5 43.5t-44.5 110.5h121q0 -59 54 -59t54 59h121q0 -67 -44.5 -110.5t-130.5 -43.5z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-87 567q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-276 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM-78 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M-130 542q-56 0 -93 36t-37 90t37 90t93 36t93 -36t37 -90t-37 -90t-93 -36zM-130 626q19 0 31 11.5t12 30.5q0 18 -12 30t-31 12t-31 -12t-12 -30q0 -19 12 -30.5t31 -11.5z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-423 588l82 142h143l-87 -142h-138zM-233 588l90 142h143l-95 -142h-138z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-140 730h140l-109 -150h-150l-109 150h140l44 -70z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M0 804l-13 -210h-143l33 210h123z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-156 -270l13 210h143l-33 -210h-123z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-121 -224q-63 0 -108 35l17 59q27 -19 65 -18.5t38 26.5q0 22 -28 30t-54 0l29 92h111l-17 -53q29 -5 48.5 -27.5t19.5 -50.5q0 -43 -33 -68t-88 -25z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-116 -224q-48 0 -80 28t-32 73q0 65 73 123h113q-30 -22 -50 -49.5t-20 -44.5q0 -28 28 -28q35 0 58 29l26 -82q-15 -20 -46.5 -34.5t-69.5 -14.5z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="730" 
d="M474 700l256 -700h-730l256 700h218zM224 147h282l-141 392z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="802" 
d="M772 379q0 -140 -105 -234h105v-145h-299v145q58 26 96 86.5t38 123.5q0 90 -57 148t-149 58t-149 -58t-57 -148q0 -63 38.5 -123.5t96.5 -86.5v-145h-300v145h105q-105 94 -105 234q0 139 106 237t265 98t265 -98t106 -237z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="671" 
d="M633 126l33 -122q-45 -18 -86 -18q-101 0 -142 61q-53 -61 -141 -61q-39 0 -77 15v-211h-160v720h160v-265q0 -111 91 -111q46 0 72.5 34t26.5 93v249h160v-343q0 -47 34 -47q11 0 29 6z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="650" 
d="M602 125l33 -117q-41 -18 -87 -18q-158 0 -158 176v203h-132v-369h-150v369h-93v140h617v-140h-92v-203q0 -46 32 -46q15 0 30 5z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1009" 
d="M585 778h-135l-114 142h174zM834 700h175l-209 -700h-176l-119 416l-120 -416h-176l-209 700h175l126 -462l129 462h149l129 -462z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="833" 
d="M497 588h-135l-114 142h174zM668 510h165l-176 -510h-148l-92 286l-93 -286h-148l-176 510h165l93 -306l92 306h133l93 -306z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1009" 
d="M674 920l-114 -142h-135l75 142h174zM834 700h175l-209 -700h-176l-119 416l-120 -416h-176l-209 700h175l126 -462l129 462h149l129 -462z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="833" 
d="M586 730l-114 -142h-135l75 142h174zM668 510h165l-176 -510h-148l-92 286l-93 -286h-148l-176 510h165l93 -306l92 306h133l93 -306z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1009" 
d="M405 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM603 751q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM834 700h175l-209 -700h-176l-119 416l-120 -416h-176
l-209 700h175l126 -462l129 462h149l129 -462z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="833" 
d="M317 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM515 561q-32 0 -54.5 22.5t-22.5 54.5t22.5 54t54.5 22t55 -22.5t23 -53.5q0 -32 -23 -54.5t-55 -22.5zM668 510h165l-176 -510h-148l-92 286l-93 -286h-148
l-176 510h165l93 -306l92 306h133l93 -306z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="691" 
d="M426 778h-135l-114 142h174zM505 700h186l-261 -448v-252h-168v252l-262 448h187l159 -286z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="604" 
d="M386 588h-135l-114 142h174zM433 510h171l-303 -720h-163l91 222l7 1l-7 -1l-229 498h175l133 -311z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="610" 
d="M55 240v130h500v-130h-500z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="910" 
d="M55 240v130h800v-130h-800z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="289" 
d="M249 700l-44 -295h-165l85 295h124z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="289" 
d="M40 405l44 295h165l-85 -295h-124z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="289" 
d="M40 -137l44 295h165l-85 -295h-124z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="499" 
d="M249 700l-44 -295h-165l85 295h124zM459 700l-44 -295h-165l85 295h124z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="499" 
d="M40 405l44 295h165l-85 -295h-124zM250 405l44 295h165l-85 -295h-124z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="499" 
d="M40 -137l44 295h165l-85 -295h-124zM250 -137l44 295h165l-85 -295h-124z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="510" 
d="M175 0v440h-145v145h145v145h160v-145h145v-145h-145v-440h-160z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="510" 
d="M175 0v145h-145v145h145v150h-145v145h145v145h160v-145h145v-145h-145v-150h145v-145h-145v-145h-160z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="360" 
d="M181 152q-59 1 -100 41.5t-41 98.5q0 56 40.5 94t100.5 40q58 1 98.5 -38t40.5 -96q0 -59 -40.5 -100t-98.5 -40z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="757" 
d="M135 -8q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27zM379 -8q-40 0 -67.5 27t-27.5 67q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27zM623 -8q-40 0 -67.5 27t-27.5 67
q0 38 27.5 64.5t67.5 26.5q39 0 66.5 -26.5t27.5 -64.5q0 -40 -27.5 -67t-66.5 -27z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1530" 
d="M245 299q-88 0 -149 59t-61 145t60.5 145t149.5 59t149.5 -59t60.5 -145t-60.5 -145t-149.5 -59zM246 0l454 700h124l-453 -700h-125zM245 414q39 0 64.5 26t25.5 63q0 38 -25.5 63.5t-64.5 25.5t-64.5 -25.5t-25.5 -63.5q0 -37 25.5 -63t64.5 -26zM826 -8q-88 0 -149 59
t-61 145t60.5 145t149.5 59t149.5 -59t60.5 -145t-60.5 -145t-149.5 -59zM1285 -8q-88 0 -149 59t-61 145t60.5 145t149.5 59t149.5 -59t60.5 -145t-60.5 -145t-149.5 -59zM826 107q39 0 64.5 26t25.5 63q0 38 -25.5 63.5t-64.5 25.5t-64.5 -25.5t-25.5 -63.5
q0 -37 25.5 -63t64.5 -26zM1285 107q39 0 64.5 26t25.5 63q0 38 -25.5 63.5t-64.5 25.5t-64.5 -25.5t-25.5 -63.5q0 -37 25.5 -63t64.5 -26z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="355" 
d="M300 510l-100 -190l100 -190h-140l-105 190l105 190h140z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="355" 
d="M195 510l105 -190l-105 -190h-140l100 190l-100 190h140z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="177" 
d="M-180 0l432 700h105l-432 -700h-105z" />
    <glyph glyph-name="zero.sups" unicode="&#x2070;" horiz-adv-x="411" 
d="M206 439q-85 0 -135.5 57t-50.5 155t50.5 154t135.5 56q81 0 133 -57t52 -153q0 -98 -50.5 -155t-134.5 -57zM206 550q62 0 62 101q0 99 -62 99q-63 0 -63 -99q0 -101 63 -101z" />
    <glyph glyph-name="four.sups" unicode="&#x2074;" horiz-adv-x="356" 
d="M346 619v-100h-44v-69h-112v69h-185v89l128 242h123l-119 -231h53v55h112v-55h44z" />
    <glyph glyph-name="five.sups" unicode="&#x2075;" horiz-adv-x="350" 
d="M199 708q60 0 95.5 -37t35.5 -95q0 -59 -41.5 -98t-107.5 -39q-70 0 -112.5 33.5t-53.5 81.5l103 29q18 -44 58 -44q20 0 32 11t12 28q0 18 -12.5 29.5t-34.5 11.5q-31 0 -57 -15l-85 27l19 219h261v-100h-165l-3 -49q22 7 56 7z" />
    <glyph glyph-name="six.sups" unicode="&#x2076;" horiz-adv-x="342" 
d="M195 725q58 -5 97.5 -43.5t39.5 -97.5q0 -63 -46 -104t-115 -41q-70 0 -116.5 41.5t-44.5 104.5q2 41 28 85l112 180h121zM171 539q22 0 35 12.5t13 32.5q0 19 -13 32t-35 13t-35 -13t-13 -32q0 -20 13 -32.5t35 -12.5z" />
    <glyph glyph-name="seven.sups" unicode="&#x2077;" horiz-adv-x="312" 
d="M3 850h304v-81l-133 -319h-124l124 298h-171v102z" />
    <glyph glyph-name="eight.sups" unicode="&#x2078;" horiz-adv-x="361" 
d="M284 668q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM181 766q-19 0 -30 -9.5t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM181 537
q23 0 35 11.5t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="nine.sups" unicode="&#x2079;" horiz-adv-x="342" 
d="M171 861q70 0 116.5 -41.5t44.5 -104.5q-2 -41 -28 -85l-112 -180h-121l76 125q-58 5 -97.5 43.5t-39.5 97.5q0 63 46 104t115 41zM171 671q22 0 35 13t13 32q0 20 -13 32.5t-35 12.5t-35 -12.5t-13 -32.5q0 -19 13 -32t35 -13z" />
    <glyph glyph-name="zero.sinf" unicode="&#x2080;" horiz-adv-x="411" 
d="M206 -161q-85 0 -135.5 57t-50.5 155t50.5 154t135.5 56q81 0 133 -57t52 -153q0 -98 -50.5 -155t-134.5 -57zM206 -50q62 0 62 101q0 99 -62 99q-63 0 -63 -99q0 -101 63 -101z" />
    <glyph glyph-name="one.sinf" unicode="&#x2081;" horiz-adv-x="287" 
d="M135 250h102v-400h-119v268l-89 -41l-29 102z" />
    <glyph glyph-name="two.sinf" unicode="&#x2082;" horiz-adv-x="357" 
d="M198 -48h139v-102h-320v73l156 145q37 36 37 58q0 13 -9.5 22t-26.5 9q-39 0 -52 -61l-107 31q6 55 49.5 94.5t107.5 39.5q73 0 116 -34.5t43 -96.5q0 -34 -14 -59.5t-46 -53.5z" />
    <glyph glyph-name="three.sinf" unicode="&#x2083;" horiz-adv-x="363" 
d="M257 99q39 -12 62.5 -43t23.5 -76q0 -60 -44.5 -100.5t-111.5 -40.5q-78 0 -120.5 38.5t-51.5 94.5l103 30q5 -29 22.5 -46t43.5 -17q22 0 34.5 11.5t12.5 30.5t-13 32t-40 13q-18 0 -36 -3l-17 62l61 66h-156v99h299v-80z" />
    <glyph glyph-name="four.sinf" unicode="&#x2084;" horiz-adv-x="356" 
d="M346 19v-100h-44v-69h-112v69h-185v89l128 242h123l-119 -231h53v55h112v-55h44z" />
    <glyph glyph-name="five.sinf" unicode="&#x2085;" horiz-adv-x="350" 
d="M199 108q60 0 95.5 -37t35.5 -95q0 -59 -41.5 -98t-107.5 -39q-70 0 -112.5 33.5t-53.5 81.5l103 29q18 -44 58 -44q20 0 32 11t12 28q0 18 -12.5 29.5t-34.5 11.5q-31 0 -57 -15l-85 27l19 219h261v-100h-165l-3 -49q22 7 56 7z" />
    <glyph glyph-name="six.sinf" unicode="&#x2086;" horiz-adv-x="342" 
d="M195 125q58 -5 97.5 -43.5t39.5 -97.5q0 -63 -46 -104t-115 -41q-70 0 -116.5 41.5t-44.5 104.5q2 41 28 85l112 180h121zM171 -61q22 0 35 12.5t13 32.5q0 19 -13 32t-35 13t-35 -13t-13 -32q0 -20 13 -32.5t35 -12.5z" />
    <glyph glyph-name="seven.sinf" unicode="&#x2087;" horiz-adv-x="312" 
d="M3 250h304v-81l-133 -319h-124l124 298h-171v102z" />
    <glyph glyph-name="eight.sinf" unicode="&#x2088;" horiz-adv-x="361" 
d="M284 68q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM181 166q-19 0 -30 -9.5t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM181 -63
q23 0 35 11.5t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="nine.sinf" unicode="&#x2089;" horiz-adv-x="342" 
d="M171 261q70 0 116.5 -41.5t44.5 -104.5q-2 -41 -28 -85l-112 -180h-121l76 125q-58 5 -97.5 43.5t-39.5 97.5q0 63 46 104t115 41zM171 71q22 0 35 13t13 32q0 20 -13 32.5t-35 12.5t-35 -12.5t-13 -32.5q0 -19 13 -32t35 -13z" />
    <glyph glyph-name="franc" unicode="&#x20a3;" horiz-adv-x="631" 
d="M586 549h-332v-119h301v-148h-301v-91h156v-85h-156v-106h-166v106h-58v85h58v509h498v-151z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="638" 
d="M269 154h339v-154h-568v154h65v56h-53v85h53v53h-53v85h53v57q0 107 65.5 165.5t174.5 58.5q92 0 155 -56t72 -133l-149 -43q-17 84 -82 84q-34 0 -53 -23t-19 -63v-47h223v-85h-223v-53h223v-85h-223v-56z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="755" 
d="M624 194l101 -129q-98 -79 -238 -79q-128 0 -224 69.5t-130 183.5h-113v85h98q-1 9 -1 26q0 18 1 27h-98v85h113q35 113 130 182.5t221 69.5q139 0 239 -79l-101 -129q-55 52 -137 52q-114 0 -170 -96h225v-85h-252q-1 -9 -1 -26q0 -9 2 -27h251v-85h-224
q26 -44 69.5 -70.5t99.5 -26.5q85 0 139 52z" />
    <glyph glyph-name="uni20BF" unicode="&#x20bf;" horiz-adv-x="741" 
d="M696 200q0 -89 -59.5 -142t-164.5 -58v-145h-116v145h-83v-145h-116v145h-127v144h82v413h-82v143h127v145h116v-145h83v145h116v-147q91 -9 145 -60t54 -128q0 -95 -83 -144q108 -53 108 -166zM434 557h-160v-138h160q32 0 54 20.5t22 49.5t-22 49t-54 19zM452 144
q36 0 58 21t22 50q0 31 -21.5 51.5t-58.5 20.5h-178v-143h178z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1120" 
d="M916 374q-75 0 -125.5 49t-50.5 121t50.5 121t125.5 49q74 0 124 -49t50 -121q0 -71 -50.5 -120.5t-123.5 -49.5zM510 299v401h166v-700h-135l-315 401v-401h-166v700h135zM916 608q-30 0 -48 -18t-18 -46q0 -27 18.5 -45.5t47.5 -18.5q27 0 45.5 19t18.5 45
q0 27 -18.5 45.5t-45.5 18.5zM758 219v110h314v-110h-314z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="729" 
d="M124 380v237h-94v83h280v-83h-96v-237h-90zM340 380v320h90l89 -130l88 130h92v-320h-88v179l-91 -126l-94 126v-179h-86z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="831" 
d="M425 -11q-168 0 -280 104.5t-112 258.5q0 149 112 254.5t271 105.5t270.5 -105.5t111.5 -254.5v-16h-615v-211q90 -106 242 -106q140 0 229 94h79q-127 -124 -308 -124zM183 366h465v210q-90 106 -232 106q-143 0 -233 -106v-210z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="825" 
d="M118 300v268l-89 -41l-29 102l135 71h102v-400h-119zM539 700h105l-432 -700h-105zM748 218q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM645 316
q-19 0 -30 -9.5t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM645 87q23 0 35 11.5t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="901" 
d="M343 430q0 -60 -44.5 -100.5t-111.5 -40.5q-78 0 -120.5 38.5t-51.5 94.5l103 30q5 -29 22.5 -46t43.5 -17q22 0 34.5 11.5t12.5 30.5t-13 32t-40 13q-18 0 -36 -3l-17 62l61 66h-156v99h299v-80l-72 -71q39 -12 62.5 -43t23.5 -76zM615 700h105l-432 -700h-105zM824 218
q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM721 316q-19 0 -30 -9.5t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM721 87q23 0 35 11.5
t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="888" 
d="M330 426q0 -59 -41.5 -98t-107.5 -39q-70 0 -112.5 33.5t-53.5 81.5l103 29q18 -44 58 -44q20 0 32 11t12 28q0 18 -12.5 29.5t-34.5 11.5q-31 0 -57 -15l-85 27l19 219h261v-100h-165l-3 -49q22 7 56 7q60 0 95.5 -37t35.5 -95zM602 700h105l-432 -700h-105zM811 218
q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM708 316q-19 0 -30 -9.5t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM708 87q23 0 35 11.5
t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="850" 
d="M174 300h-124l124 298h-171v102h304v-81zM564 700h105l-432 -700h-105zM773 218q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM670 316q-19 0 -30 -9.5
t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM670 87q23 0 35 11.5t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="825" 
d="M770 398v-140h-450l169 -166l-99 -99l-335 335l335 335l99 -99l-169 -166h450z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="780" 
d="M725 380l-99 -99l-161 164v-445h-150v445l-161 -164l-99 99l335 335z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="825" 
d="M435 666l335 -335l-335 -335l-99 99l169 166h-450v140h450l-169 166z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="780" 
d="M626 419l99 -99l-335 -335l-335 335l99 99l161 -164v445h150v-445z" />
    <glyph glyph-name="arrowboth" unicode="&#x2194;" horiz-adv-x="1142" 
d="M752 666l335 -335l-335 -335l-99 99l169 166h-502l169 -166l-99 -99l-335 335l335 335l99 -99l-169 -166h502l-169 166z" />
    <glyph glyph-name="arrowupdn" unicode="&#x2195;" horiz-adv-x="780" 
d="M465 85l161 164l99 -99l-335 -335l-335 335l99 99l161 -164v492l-161 -164l-99 99l335 335l335 -335l-99 -99l-161 164v-492z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="667" 
d="M612 115l-103 -103l-316 316l2 -232l-140 -1v474h473l1 -140l-233 2z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="667" 
d="M138 599h474v-473l-140 -1l2 233l-316 -316l-103 103l316 316l-232 -2z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="667" 
d="M472 525l140 1v-474h-473l-1 140l233 -2l-316 316l103 103l316 -316z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="667" 
d="M296 161l232 2l1 -140h-474v473l140 1l-2 -233l316 316l103 -103z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="640" 
d="M302 -14q-108 0 -185 72.5t-77 177.5q0 104 69 173t176 69q44 0 87 -16t70 -43q-9 68 -53 114.5t-110 46.5q-95 0 -165 -68l-34 127q90 75 218 75q137 0 219.5 -103.5t82.5 -270.5q0 -163 -81.5 -258.5t-216.5 -95.5zM308 127q55 0 90.5 40.5t39.5 93.5q-14 30 -53 56
t-82 26q-47 0 -78 -31t-31 -74q0 -47 31.5 -79t82.5 -32z" />
    <glyph glyph-name="emptyset" unicode="&#x2205;" horiz-adv-x="505" 
d="M442 468q50 -62 50 -148q1 -99 -66.5 -167t-166.5 -68q-85 0 -148 52l-64 -64l-42 42l65 65q-44 61 -44 140q0 99 67 167.5t166 67.5q79 0 141 -46l59 59l41 -42zM134 320q0 -34 15 -61l171 170q-27 15 -62 15q-53 0 -88.5 -35.5t-35.5 -88.5zM258 196q53 0 89 35.5
t37 88.5q0 38 -21 69l-173 -173q29 -20 68 -20z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="656" 
d="M601 730v-940h-160v795h-226v-795h-160v940h546z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="664" 
d="M246 -55h378v-145h-584v127l269 346l-252 330v127h547v-145h-342l235 -313z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="560" 
d="M55 288v125h450v-125h-450z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="751" 
d="M601 790h160l-274 -881h-170l-143 375h-134v135h246l112 -332z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="864" 
d="M622 560q84 0 143 -62t59 -148q0 -88 -59 -149t-143 -61q-107 0 -189 102q-84 -102 -191 -102q-84 0 -143 61t-59 149q0 86 59 148t143 62q107 0 191 -104q82 104 189 104zM247 267q55 0 123 82q-68 82 -123 82q-32 0 -55 -24t-23 -58t23 -58t55 -24zM618 267q32 0 55 24
t23 58t-23 58t-55 24q-55 0 -123 -82q68 -82 123 -82z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="593" 
d="M129 -156q-83 0 -129 43l59 126q25 -28 58 -28q60 0 70 69l71 527q28 208 206 208q83 0 129 -43l-59 -126q-25 28 -58 28q-60 0 -70 -69l-71 -527q-28 -208 -206 -208z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="606" 
d="M197 357l-142 41q0 63 33 109t100 46q47 0 110.5 -28t74.5 -28q36 0 36 56l142 -41q0 -64 -32.5 -109.5t-98.5 -45.5q-47 0 -111 28t-75 28q-37 0 -37 -56zM197 150l-142 41q0 63 33 109t100 46q47 0 110.5 -28t74.5 -28q36 0 36 56l142 -41q0 -64 -32.5 -109.5
t-98.5 -45.5q-47 0 -111 28t-75 28q-37 0 -37 -56z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="560" 
d="M505 388h-153l-33 -75h186v-125h-240l-54 -123h-110l54 123h-100v125h154l33 75h-187v125h241l53 122h110l-53 -122h99v-125z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="565" 
d="M505 677v-135l-302 -107l302 -108v-135l-450 170v145zM55 42v125h450v-125h-450z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="565" 
d="M60 677l450 -170v-145l-450 -170v135l302 108l-302 107v135zM60 42v125h450v-125h-450z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="639" 
d="M406 700l193 -350l-193 -350h-172l-194 350l194 350h172zM320 144l113 206l-113 206l-114 -206z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="280" 
d="M140 567q-36 0 -61.5 25t-25.5 61q0 35 25.5 60t61.5 25t61.5 -25t25.5 -60q0 -36 -25.5 -61t-61.5 -25zM60 0v510h160v-510h-160z" />
    <glyph glyph-name="f_f_j" horiz-adv-x="995" 
d="M948 510v-510q0 -106 -46.5 -165t-141.5 -59q-50 0 -101 22l35 122q18 -10 39 -10q55 0 55 75v385h-180v-370h-160v370h-180v-370h-160v370h-93v140h93v36q0 92 51.5 145t147.5 53q71 0 120 -22l-38 -127q-26 11 -57 11q-26 0 -45 -16.5t-19 -43.5v-36h180v36
q0 92 55 145t159 53q109 0 186 -44l-38 -127q-65 33 -129 33q-30 0 -51.5 -16.5t-21.5 -43.5v-36h340z" />
    <glyph glyph-name="f_j" horiz-adv-x="655" 
d="M608 510v-510q0 -106 -46.5 -165t-141.5 -59q-50 0 -101 22l35 122q18 -10 39 -10q55 0 55 75v385h-180v-370h-160v370h-93v140h93v36q0 92 55 145t159 53q109 0 186 -44l-38 -127q-65 33 -129 33q-30 0 -51.5 -16.5t-21.5 -43.5v-36h340z" />
    <glyph glyph-name="f_t" horiz-adv-x="768" 
d="M710 144l38 -127q-63 -25 -125 -25q-90 0 -135 51t-45 141v186h-175v-370h-160v370h-93v140h93v36q0 92 51.5 145t147.5 53q56 0 95 -14l-38 -127q-15 3 -32 3q-30 0 -47 -15.5t-17 -44.5v-36h175v160h160v-160h137v-140h-137v-186q0 -53 53 -53q29 0 54 13z" />
    <glyph glyph-name="t_t" horiz-adv-x="764" 
d="M706 144l38 -127q-63 -25 -125 -25q-90 0 -135 51t-45 141v186h-190v-186q0 -53 53 -53q29 0 54 13l38 -127q-63 -25 -125 -25q-90 0 -135 51t-45 141v186h-79v140h79v160h160v-160h190v160h160v-160h137v-140h-137v-186q0 -53 53 -53q29 0 54 13z" />
    <glyph glyph-name="zero.tf" horiz-adv-x="615" 
d="M308 -14q-139 0 -218.5 101t-79.5 263t79.5 263t218.5 101t218 -101t79 -263t-79 -263t-218 -101zM308 136q66 0 98.5 56.5t32.5 157.5t-32.5 157.5t-98.5 56.5t-99 -56.5t-33 -157.5t33 -157.5t99 -56.5z" />
    <glyph glyph-name="one.tf" horiz-adv-x="615" 
d="M420 150h126v-150h-460v150h166v367l-148 -68l-40 141l214 110h142v-550z" />
    <glyph glyph-name="two.tf" horiz-adv-x="615" 
d="M311 150h264v-150h-519v111l270 251q39 35 57 61.5t18 56.5q0 40 -24 63t-66 23q-41 0 -70.5 -28.5t-39.5 -75.5l-149 43q11 86 81.5 147.5t173.5 61.5q117 0 188 -58.5t71 -165.5q0 -63 -30.5 -114t-95.5 -110z" />
    <glyph glyph-name="three.tf" horiz-adv-x="615" 
d="M415 437q75 -18 121 -72.5t46 -136.5q0 -102 -74 -172t-184 -70q-124 0 -195 64.5t-86 155.5l147 42q9 -48 43.5 -83t80.5 -35q50 0 79 29t29 69t-28 68.5t-77 28.5q-37 0 -63 -13l-27 96l113 144h-275v148h493v-109z" />
    <glyph glyph-name="four.tf" horiz-adv-x="615" 
d="M603 272v-146h-83v-126h-166v126h-325v120l231 454h178l-215 -428h131v126h166v-126h83z" />
    <glyph glyph-name="five.tf" horiz-adv-x="615" 
d="M351 461q100 0 165.5 -67.5t65.5 -165.5q0 -103 -73 -172.5t-184 -69.5q-113 0 -183.5 56t-93.5 139l149 42q14 -40 46 -66.5t71 -26.5q50 0 79 28.5t29 72.5t-28.5 72t-80.5 28q-60 0 -104 -30l-132 39l30 360h443v-148h-296l-8 -108q48 17 105 17z" />
    <glyph glyph-name="six.tf" horiz-adv-x="615" 
d="M341 484q109 -8 178.5 -76t69.5 -170q0 -108 -78 -180t-195 -72t-195 71.5t-78 180.5q0 70 49 148l199 314h187zM316 134q49 0 78.5 29t29.5 75q0 44 -29.5 73.5t-78.5 29.5q-48 0 -78 -29.5t-30 -73.5q0 -46 29.5 -75t78.5 -29z" />
    <glyph glyph-name="seven.tf" horiz-adv-x="615" 
d="M60 700h514v-113l-256 -587h-176l242 550h-324v150z" />
    <glyph glyph-name="eight.tf" horiz-adv-x="615" 
d="M475 382q116 -54 116 -177q0 -93 -77 -156t-201 -63q-125 0 -201.5 62.5t-76.5 156.5q0 124 116 177q-86 46 -86 143q0 80 68 134.5t180 54.5t180 -54.5t68 -134.5q0 -97 -86 -143zM313 585q-40 0 -64 -22t-24 -53q0 -32 24 -54t64 -22t64 22.5t24 53.5t-24 53t-64 22z
M313 124q51 0 81 27t30 68q0 42 -30 69t-81 27q-52 0 -81.5 -27t-29.5 -69q0 -41 29.5 -68t81.5 -27z" />
    <glyph glyph-name="nine.tf" horiz-adv-x="615" 
d="M319 714q117 0 195 -71.5t78 -180.5q0 -70 -49 -148l-199 -314h-187l137 216q-109 8 -178.5 76t-69.5 170q0 108 78 180t195 72zM319 359q48 0 78 29.5t30 73.5q0 46 -29.5 75t-78.5 29t-78.5 -29t-29.5 -75q0 -44 29.5 -73.5t78.5 -29.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="411" 
d="M206 -11q-85 0 -135.5 57t-50.5 155t50.5 154t135.5 56q81 0 133 -57t52 -153q0 -98 -50.5 -155t-134.5 -57zM206 100q62 0 62 101q0 99 -62 99q-63 0 -63 -99q0 -101 63 -101z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="287" 
d="M135 400h102v-400h-119v268l-89 -41l-29 102z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="357" 
d="M198 102h139v-102h-320v73l156 145q37 36 37 58q0 13 -9.5 22t-26.5 9q-39 0 -52 -61l-107 31q6 55 49.5 94.5t107.5 39.5q73 0 116 -34.5t43 -96.5q0 -34 -14 -59.5t-46 -53.5z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="363" 
d="M257 249q39 -12 62.5 -43t23.5 -76q0 -60 -44.5 -100.5t-111.5 -40.5q-78 0 -120.5 38.5t-51.5 94.5l103 30q5 -29 22.5 -46t43.5 -17q22 0 34.5 11.5t12.5 30.5t-13 32t-40 13q-18 0 -36 -3l-17 62l61 66h-156v99h299v-80z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="356" 
d="M346 169v-100h-44v-69h-112v69h-185v89l128 242h123l-119 -231h53v55h112v-55h44z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="350" 
d="M199 258q60 0 95.5 -37t35.5 -95q0 -59 -41.5 -98t-107.5 -39q-70 0 -112.5 33.5t-53.5 81.5l103 29q18 -44 58 -44q20 0 32 11t12 28q0 18 -12.5 29.5t-34.5 11.5q-31 0 -57 -15l-85 27l19 219h261v-100h-165l-3 -49q22 7 56 7z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="342" 
d="M195 275q58 -5 97.5 -43.5t39.5 -97.5q0 -63 -46 -104t-115 -41q-70 0 -116.5 41.5t-44.5 104.5q2 41 28 85l112 180h121zM171 89q22 0 35 12.5t13 32.5q0 19 -13 32t-35 13t-35 -13t-13 -32q0 -20 13 -32.5t35 -12.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="312" 
d="M3 400h304v-81l-133 -319h-124l124 298h-171v102z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="361" 
d="M284 218q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM181 316q-19 0 -30 -9.5t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM181 87
q23 0 35 11.5t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="342" 
d="M171 411q70 0 116.5 -41.5t44.5 -104.5q-2 -41 -28 -85l-112 -180h-121l76 125q-58 5 -97.5 43.5t-39.5 97.5q0 63 46 104t115 41zM171 221q22 0 35 13t13 32q0 20 -13 32.5t-35 12.5t-35 -12.5t-13 -32.5q0 -19 13 -32t35 -13z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="411" 
d="M206 289q-85 0 -135.5 57t-50.5 155t50.5 154t135.5 56q81 0 133 -57t52 -153q0 -98 -50.5 -155t-134.5 -57zM206 400q62 0 62 101q0 99 -62 99q-63 0 -63 -99q0 -101 63 -101z" />
    <glyph glyph-name="one.numr" horiz-adv-x="287" 
d="M135 700h102v-400h-119v268l-89 -41l-29 102z" />
    <glyph glyph-name="two.numr" horiz-adv-x="357" 
d="M198 402h139v-102h-320v73l156 145q37 36 37 58q0 13 -9.5 22t-26.5 9q-39 0 -52 -61l-107 31q6 55 49.5 94.5t107.5 39.5q73 0 116 -34.5t43 -96.5q0 -34 -14 -59.5t-46 -53.5z" />
    <glyph glyph-name="three.numr" horiz-adv-x="363" 
d="M257 549q39 -12 62.5 -43t23.5 -76q0 -60 -44.5 -100.5t-111.5 -40.5q-78 0 -120.5 38.5t-51.5 94.5l103 30q5 -29 22.5 -46t43.5 -17q22 0 34.5 11.5t12.5 30.5t-13 32t-40 13q-18 0 -36 -3l-17 62l61 66h-156v99h299v-80z" />
    <glyph glyph-name="four.numr" horiz-adv-x="356" 
d="M346 469v-100h-44v-69h-112v69h-185v89l128 242h123l-119 -231h53v55h112v-55h44z" />
    <glyph glyph-name="five.numr" horiz-adv-x="350" 
d="M199 558q60 0 95.5 -37t35.5 -95q0 -59 -41.5 -98t-107.5 -39q-70 0 -112.5 33.5t-53.5 81.5l103 29q18 -44 58 -44q20 0 32 11t12 28q0 18 -12.5 29.5t-34.5 11.5q-31 0 -57 -15l-85 27l19 219h261v-100h-165l-3 -49q22 7 56 7z" />
    <glyph glyph-name="six.numr" horiz-adv-x="342" 
d="M195 575q58 -5 97.5 -43.5t39.5 -97.5q0 -63 -46 -104t-115 -41q-70 0 -116.5 41.5t-44.5 104.5q2 41 28 85l112 180h121zM171 389q22 0 35 12.5t13 32.5q0 19 -13 32t-35 13t-35 -13t-13 -32q0 -20 13 -32.5t35 -12.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="312" 
d="M3 700h304v-81l-133 -319h-124l124 298h-171v102z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="361" 
d="M284 518q62 -33 62 -102q0 -55 -45 -91t-120 -36t-120.5 36t-45.5 91q0 69 62 102q-45 27 -45 81q0 47 41 79.5t108 32.5q66 0 107 -32.5t41 -79.5q0 -54 -45 -81zM181 616q-19 0 -30 -9.5t-11 -24.5t11 -24.5t30 -9.5t30 9.5t11 24.5t-11 24.5t-30 9.5zM181 387
q23 0 35 11.5t12 30.5q0 17 -12.5 29t-34.5 12t-34.5 -12t-12.5 -29q0 -19 12 -30.5t35 -11.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="342" 
d="M171 711q70 0 116.5 -41.5t44.5 -104.5q-2 -41 -28 -85l-112 -180h-121l76 125q-58 5 -97.5 43.5t-39.5 97.5q0 63 46 104t115 41zM171 521q22 0 35 13t13 32q0 20 -13 32.5t-35 12.5t-35 -12.5t-13 -32.5q0 -19 13 -32t35 -13z" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="E" u2="&#x153;" k="10" />
    <hkern u1="E" u2="&#x151;" k="10" />
    <hkern u1="E" u2="&#x14d;" k="10" />
    <hkern u1="E" u2="&#x123;" k="10" />
    <hkern u1="E" u2="&#x121;" k="10" />
    <hkern u1="E" u2="&#x11f;" k="10" />
    <hkern u1="E" u2="&#x11b;" k="10" />
    <hkern u1="E" u2="&#x119;" k="10" />
    <hkern u1="E" u2="&#x117;" k="10" />
    <hkern u1="E" u2="&#x113;" k="10" />
    <hkern u1="E" u2="&#x111;" k="10" />
    <hkern u1="E" u2="&#x10f;" k="10" />
    <hkern u1="E" u2="&#x10d;" k="10" />
    <hkern u1="E" u2="&#x10b;" k="10" />
    <hkern u1="E" u2="&#x107;" k="10" />
    <hkern u1="E" u2="&#x105;" k="10" />
    <hkern u1="E" u2="&#x103;" k="10" />
    <hkern u1="E" u2="&#x101;" k="10" />
    <hkern u1="E" u2="&#xf8;" k="10" />
    <hkern u1="E" u2="&#xf6;" k="10" />
    <hkern u1="E" u2="&#xf5;" k="10" />
    <hkern u1="E" u2="&#xf4;" k="10" />
    <hkern u1="E" u2="&#xf3;" k="10" />
    <hkern u1="E" u2="&#xf2;" k="10" />
    <hkern u1="E" u2="&#xeb;" k="10" />
    <hkern u1="E" u2="&#xea;" k="10" />
    <hkern u1="E" u2="&#xe9;" k="10" />
    <hkern u1="E" u2="&#xe8;" k="10" />
    <hkern u1="E" u2="&#xe7;" k="10" />
    <hkern u1="E" u2="&#xe6;" k="10" />
    <hkern u1="E" u2="&#xe5;" k="10" />
    <hkern u1="E" u2="&#xe4;" k="10" />
    <hkern u1="E" u2="&#xe3;" k="10" />
    <hkern u1="E" u2="&#xe2;" k="10" />
    <hkern u1="E" u2="&#xe1;" k="10" />
    <hkern u1="E" u2="&#xe0;" k="10" />
    <hkern u1="E" u2="q" k="10" />
    <hkern u1="E" u2="o" k="10" />
    <hkern u1="E" u2="g" k="10" />
    <hkern u1="E" u2="e" k="10" />
    <hkern u1="E" u2="d" k="10" />
    <hkern u1="E" u2="c" k="10" />
    <hkern u1="E" u2="a" k="10" />
    <hkern u1="F" u2="&#x153;" k="20" />
    <hkern u1="F" u2="&#x151;" k="20" />
    <hkern u1="F" u2="&#x14d;" k="20" />
    <hkern u1="F" u2="&#x123;" k="20" />
    <hkern u1="F" u2="&#x121;" k="20" />
    <hkern u1="F" u2="&#x11f;" k="20" />
    <hkern u1="F" u2="&#x11b;" k="20" />
    <hkern u1="F" u2="&#x119;" k="20" />
    <hkern u1="F" u2="&#x117;" k="20" />
    <hkern u1="F" u2="&#x113;" k="20" />
    <hkern u1="F" u2="&#x111;" k="20" />
    <hkern u1="F" u2="&#x10f;" k="20" />
    <hkern u1="F" u2="&#x10d;" k="20" />
    <hkern u1="F" u2="&#x10b;" k="20" />
    <hkern u1="F" u2="&#x107;" k="20" />
    <hkern u1="F" u2="&#x105;" k="20" />
    <hkern u1="F" u2="&#x103;" k="20" />
    <hkern u1="F" u2="&#x101;" k="20" />
    <hkern u1="F" u2="&#xf8;" k="20" />
    <hkern u1="F" u2="&#xf6;" k="20" />
    <hkern u1="F" u2="&#xf5;" k="20" />
    <hkern u1="F" u2="&#xf4;" k="20" />
    <hkern u1="F" u2="&#xf3;" k="20" />
    <hkern u1="F" u2="&#xf2;" k="20" />
    <hkern u1="F" u2="&#xeb;" k="20" />
    <hkern u1="F" u2="&#xea;" k="20" />
    <hkern u1="F" u2="&#xe9;" k="20" />
    <hkern u1="F" u2="&#xe8;" k="20" />
    <hkern u1="F" u2="&#xe7;" k="20" />
    <hkern u1="F" u2="&#xe6;" k="20" />
    <hkern u1="F" u2="&#xe5;" k="20" />
    <hkern u1="F" u2="&#xe4;" k="20" />
    <hkern u1="F" u2="&#xe3;" k="20" />
    <hkern u1="F" u2="&#xe2;" k="20" />
    <hkern u1="F" u2="&#xe1;" k="20" />
    <hkern u1="F" u2="&#xe0;" k="20" />
    <hkern u1="F" u2="q" k="20" />
    <hkern u1="F" u2="o" k="20" />
    <hkern u1="F" u2="g" k="20" />
    <hkern u1="F" u2="e" k="20" />
    <hkern u1="F" u2="d" k="20" />
    <hkern u1="F" u2="c" k="20" />
    <hkern u1="F" u2="a" k="20" />
    <hkern u1="K" u2="&#x153;" k="50" />
    <hkern u1="K" u2="&#x151;" k="50" />
    <hkern u1="K" u2="&#x14d;" k="50" />
    <hkern u1="K" u2="&#x123;" k="50" />
    <hkern u1="K" u2="&#x121;" k="50" />
    <hkern u1="K" u2="&#x11f;" k="50" />
    <hkern u1="K" u2="&#x11b;" k="50" />
    <hkern u1="K" u2="&#x119;" k="50" />
    <hkern u1="K" u2="&#x117;" k="50" />
    <hkern u1="K" u2="&#x113;" k="50" />
    <hkern u1="K" u2="&#x111;" k="50" />
    <hkern u1="K" u2="&#x10f;" k="50" />
    <hkern u1="K" u2="&#x10d;" k="50" />
    <hkern u1="K" u2="&#x10b;" k="50" />
    <hkern u1="K" u2="&#x107;" k="50" />
    <hkern u1="K" u2="&#x105;" k="50" />
    <hkern u1="K" u2="&#x103;" k="50" />
    <hkern u1="K" u2="&#x101;" k="50" />
    <hkern u1="K" u2="&#xf8;" k="50" />
    <hkern u1="K" u2="&#xf6;" k="50" />
    <hkern u1="K" u2="&#xf5;" k="50" />
    <hkern u1="K" u2="&#xf4;" k="50" />
    <hkern u1="K" u2="&#xf3;" k="50" />
    <hkern u1="K" u2="&#xf2;" k="50" />
    <hkern u1="K" u2="&#xeb;" k="50" />
    <hkern u1="K" u2="&#xea;" k="50" />
    <hkern u1="K" u2="&#xe9;" k="50" />
    <hkern u1="K" u2="&#xe8;" k="50" />
    <hkern u1="K" u2="&#xe7;" k="50" />
    <hkern u1="K" u2="&#xe6;" k="50" />
    <hkern u1="K" u2="&#xe5;" k="50" />
    <hkern u1="K" u2="&#xe4;" k="50" />
    <hkern u1="K" u2="&#xe3;" k="50" />
    <hkern u1="K" u2="&#xe2;" k="50" />
    <hkern u1="K" u2="&#xe1;" k="50" />
    <hkern u1="K" u2="&#xe0;" k="50" />
    <hkern u1="K" u2="q" k="50" />
    <hkern u1="K" u2="o" k="50" />
    <hkern u1="K" u2="g" k="50" />
    <hkern u1="K" u2="e" k="50" />
    <hkern u1="K" u2="d" k="50" />
    <hkern u1="K" u2="c" k="50" />
    <hkern u1="K" u2="a" k="50" />
    <hkern u1="L" u2="&#x153;" k="7" />
    <hkern u1="L" u2="&#x151;" k="7" />
    <hkern u1="L" u2="&#x14d;" k="7" />
    <hkern u1="L" u2="&#x123;" k="7" />
    <hkern u1="L" u2="&#x121;" k="7" />
    <hkern u1="L" u2="&#x11f;" k="7" />
    <hkern u1="L" u2="&#x11b;" k="7" />
    <hkern u1="L" u2="&#x119;" k="7" />
    <hkern u1="L" u2="&#x117;" k="7" />
    <hkern u1="L" u2="&#x113;" k="7" />
    <hkern u1="L" u2="&#x111;" k="7" />
    <hkern u1="L" u2="&#x10f;" k="7" />
    <hkern u1="L" u2="&#x10d;" k="7" />
    <hkern u1="L" u2="&#x10b;" k="7" />
    <hkern u1="L" u2="&#x107;" k="7" />
    <hkern u1="L" u2="&#x105;" k="7" />
    <hkern u1="L" u2="&#x103;" k="7" />
    <hkern u1="L" u2="&#x101;" k="7" />
    <hkern u1="L" u2="&#xf8;" k="7" />
    <hkern u1="L" u2="&#xf6;" k="7" />
    <hkern u1="L" u2="&#xf5;" k="7" />
    <hkern u1="L" u2="&#xf4;" k="7" />
    <hkern u1="L" u2="&#xf3;" k="7" />
    <hkern u1="L" u2="&#xf2;" k="7" />
    <hkern u1="L" u2="&#xeb;" k="7" />
    <hkern u1="L" u2="&#xea;" k="7" />
    <hkern u1="L" u2="&#xe9;" k="7" />
    <hkern u1="L" u2="&#xe8;" k="7" />
    <hkern u1="L" u2="&#xe7;" k="7" />
    <hkern u1="L" u2="&#xe6;" k="7" />
    <hkern u1="L" u2="&#xe5;" k="7" />
    <hkern u1="L" u2="&#xe4;" k="7" />
    <hkern u1="L" u2="&#xe3;" k="7" />
    <hkern u1="L" u2="&#xe2;" k="7" />
    <hkern u1="L" u2="&#xe1;" k="7" />
    <hkern u1="L" u2="&#xe0;" k="7" />
    <hkern u1="L" u2="q" k="7" />
    <hkern u1="L" u2="o" k="7" />
    <hkern u1="L" u2="g" k="7" />
    <hkern u1="L" u2="e" k="7" />
    <hkern u1="L" u2="d" k="7" />
    <hkern u1="L" u2="c" k="7" />
    <hkern u1="L" u2="a" k="7" />
    <hkern u1="P" u2="&#x153;" k="15" />
    <hkern u1="P" u2="&#x151;" k="15" />
    <hkern u1="P" u2="&#x14d;" k="15" />
    <hkern u1="P" u2="&#x123;" k="15" />
    <hkern u1="P" u2="&#x121;" k="15" />
    <hkern u1="P" u2="&#x11f;" k="15" />
    <hkern u1="P" u2="&#x11b;" k="15" />
    <hkern u1="P" u2="&#x119;" k="15" />
    <hkern u1="P" u2="&#x117;" k="15" />
    <hkern u1="P" u2="&#x113;" k="15" />
    <hkern u1="P" u2="&#x111;" k="15" />
    <hkern u1="P" u2="&#x10f;" k="15" />
    <hkern u1="P" u2="&#x10d;" k="15" />
    <hkern u1="P" u2="&#x10b;" k="15" />
    <hkern u1="P" u2="&#x107;" k="15" />
    <hkern u1="P" u2="&#x105;" k="15" />
    <hkern u1="P" u2="&#x103;" k="15" />
    <hkern u1="P" u2="&#x101;" k="15" />
    <hkern u1="P" u2="&#xf8;" k="15" />
    <hkern u1="P" u2="&#xf6;" k="15" />
    <hkern u1="P" u2="&#xf5;" k="15" />
    <hkern u1="P" u2="&#xf4;" k="15" />
    <hkern u1="P" u2="&#xf3;" k="15" />
    <hkern u1="P" u2="&#xf2;" k="15" />
    <hkern u1="P" u2="&#xeb;" k="15" />
    <hkern u1="P" u2="&#xea;" k="15" />
    <hkern u1="P" u2="&#xe9;" k="15" />
    <hkern u1="P" u2="&#xe8;" k="15" />
    <hkern u1="P" u2="&#xe7;" k="15" />
    <hkern u1="P" u2="&#xe6;" k="15" />
    <hkern u1="P" u2="&#xe5;" k="15" />
    <hkern u1="P" u2="&#xe4;" k="15" />
    <hkern u1="P" u2="&#xe3;" k="15" />
    <hkern u1="P" u2="&#xe2;" k="15" />
    <hkern u1="P" u2="&#xe1;" k="15" />
    <hkern u1="P" u2="&#xe0;" k="15" />
    <hkern u1="P" u2="q" k="15" />
    <hkern u1="P" u2="o" k="15" />
    <hkern u1="P" u2="g" k="15" />
    <hkern u1="P" u2="e" k="15" />
    <hkern u1="P" u2="d" k="15" />
    <hkern u1="P" u2="c" k="15" />
    <hkern u1="P" u2="a" k="15" />
    <hkern u1="Q" u2="&#x104;" k="15" />
    <hkern u1="Q" u2="&#x102;" k="15" />
    <hkern u1="Q" u2="&#x100;" k="15" />
    <hkern u1="Q" u2="&#xc6;" k="15" />
    <hkern u1="Q" u2="&#xc5;" k="15" />
    <hkern u1="Q" u2="&#xc4;" k="15" />
    <hkern u1="Q" u2="&#xc3;" k="15" />
    <hkern u1="Q" u2="&#xc2;" k="15" />
    <hkern u1="Q" u2="&#xc1;" k="15" />
    <hkern u1="Q" u2="&#xc0;" k="15" />
    <hkern u1="Q" u2="A" k="15" />
    <hkern u1="R" u2="&#x153;" k="20" />
    <hkern u1="R" u2="&#x151;" k="20" />
    <hkern u1="R" u2="&#x14d;" k="20" />
    <hkern u1="R" u2="&#x123;" k="20" />
    <hkern u1="R" u2="&#x121;" k="20" />
    <hkern u1="R" u2="&#x11f;" k="20" />
    <hkern u1="R" u2="&#x11b;" k="20" />
    <hkern u1="R" u2="&#x119;" k="20" />
    <hkern u1="R" u2="&#x117;" k="20" />
    <hkern u1="R" u2="&#x113;" k="20" />
    <hkern u1="R" u2="&#x111;" k="20" />
    <hkern u1="R" u2="&#x10f;" k="20" />
    <hkern u1="R" u2="&#x10d;" k="20" />
    <hkern u1="R" u2="&#x10b;" k="20" />
    <hkern u1="R" u2="&#x107;" k="20" />
    <hkern u1="R" u2="&#x105;" k="20" />
    <hkern u1="R" u2="&#x103;" k="20" />
    <hkern u1="R" u2="&#x101;" k="20" />
    <hkern u1="R" u2="&#xf8;" k="20" />
    <hkern u1="R" u2="&#xf6;" k="20" />
    <hkern u1="R" u2="&#xf5;" k="20" />
    <hkern u1="R" u2="&#xf4;" k="20" />
    <hkern u1="R" u2="&#xf3;" k="20" />
    <hkern u1="R" u2="&#xf2;" k="20" />
    <hkern u1="R" u2="&#xeb;" k="20" />
    <hkern u1="R" u2="&#xea;" k="20" />
    <hkern u1="R" u2="&#xe9;" k="20" />
    <hkern u1="R" u2="&#xe8;" k="20" />
    <hkern u1="R" u2="&#xe7;" k="20" />
    <hkern u1="R" u2="&#xe6;" k="20" />
    <hkern u1="R" u2="&#xe5;" k="20" />
    <hkern u1="R" u2="&#xe4;" k="20" />
    <hkern u1="R" u2="&#xe3;" k="20" />
    <hkern u1="R" u2="&#xe2;" k="20" />
    <hkern u1="R" u2="&#xe1;" k="20" />
    <hkern u1="R" u2="&#xe0;" k="20" />
    <hkern u1="R" u2="q" k="20" />
    <hkern u1="R" u2="o" k="20" />
    <hkern u1="R" u2="g" k="20" />
    <hkern u1="R" u2="e" k="20" />
    <hkern u1="R" u2="d" k="20" />
    <hkern u1="R" u2="c" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="&#x153;" k="100" />
    <hkern u1="T" u2="&#x151;" k="100" />
    <hkern u1="T" u2="&#x14d;" k="100" />
    <hkern u1="T" u2="&#x123;" k="100" />
    <hkern u1="T" u2="&#x121;" k="100" />
    <hkern u1="T" u2="&#x11f;" k="100" />
    <hkern u1="T" u2="&#x11b;" k="100" />
    <hkern u1="T" u2="&#x119;" k="100" />
    <hkern u1="T" u2="&#x117;" k="100" />
    <hkern u1="T" u2="&#x113;" k="100" />
    <hkern u1="T" u2="&#x111;" k="100" />
    <hkern u1="T" u2="&#x10f;" k="100" />
    <hkern u1="T" u2="&#x10d;" k="100" />
    <hkern u1="T" u2="&#x10b;" k="100" />
    <hkern u1="T" u2="&#x107;" k="100" />
    <hkern u1="T" u2="&#x105;" k="100" />
    <hkern u1="T" u2="&#x103;" k="100" />
    <hkern u1="T" u2="&#x101;" k="100" />
    <hkern u1="T" u2="&#xf8;" k="100" />
    <hkern u1="T" u2="&#xf6;" k="100" />
    <hkern u1="T" u2="&#xf5;" k="100" />
    <hkern u1="T" u2="&#xf4;" k="100" />
    <hkern u1="T" u2="&#xf3;" k="100" />
    <hkern u1="T" u2="&#xf2;" k="100" />
    <hkern u1="T" u2="&#xeb;" k="100" />
    <hkern u1="T" u2="&#xea;" k="100" />
    <hkern u1="T" u2="&#xe9;" k="100" />
    <hkern u1="T" u2="&#xe8;" k="100" />
    <hkern u1="T" u2="&#xe7;" k="100" />
    <hkern u1="T" u2="&#xe6;" k="100" />
    <hkern u1="T" u2="&#xe5;" k="100" />
    <hkern u1="T" u2="&#xe4;" k="100" />
    <hkern u1="T" u2="&#xe3;" k="100" />
    <hkern u1="T" u2="&#xe2;" k="100" />
    <hkern u1="T" u2="&#xe1;" k="100" />
    <hkern u1="T" u2="&#xe0;" k="100" />
    <hkern u1="T" u2="q" k="100" />
    <hkern u1="T" u2="o" k="100" />
    <hkern u1="T" u2="g" k="100" />
    <hkern u1="T" u2="e" k="100" />
    <hkern u1="T" u2="d" k="100" />
    <hkern u1="T" u2="c" k="100" />
    <hkern u1="T" u2="a" k="100" />
    <hkern u1="X" u2="&#x153;" k="35" />
    <hkern u1="X" u2="&#x151;" k="35" />
    <hkern u1="X" u2="&#x14d;" k="35" />
    <hkern u1="X" u2="&#x123;" k="35" />
    <hkern u1="X" u2="&#x121;" k="35" />
    <hkern u1="X" u2="&#x11f;" k="35" />
    <hkern u1="X" u2="&#x11b;" k="35" />
    <hkern u1="X" u2="&#x119;" k="35" />
    <hkern u1="X" u2="&#x117;" k="35" />
    <hkern u1="X" u2="&#x113;" k="35" />
    <hkern u1="X" u2="&#x111;" k="35" />
    <hkern u1="X" u2="&#x10f;" k="35" />
    <hkern u1="X" u2="&#x10d;" k="35" />
    <hkern u1="X" u2="&#x10b;" k="35" />
    <hkern u1="X" u2="&#x107;" k="35" />
    <hkern u1="X" u2="&#x105;" k="35" />
    <hkern u1="X" u2="&#x103;" k="35" />
    <hkern u1="X" u2="&#x101;" k="35" />
    <hkern u1="X" u2="&#xf8;" k="35" />
    <hkern u1="X" u2="&#xf6;" k="35" />
    <hkern u1="X" u2="&#xf5;" k="35" />
    <hkern u1="X" u2="&#xf4;" k="35" />
    <hkern u1="X" u2="&#xf3;" k="35" />
    <hkern u1="X" u2="&#xf2;" k="35" />
    <hkern u1="X" u2="&#xeb;" k="35" />
    <hkern u1="X" u2="&#xea;" k="35" />
    <hkern u1="X" u2="&#xe9;" k="35" />
    <hkern u1="X" u2="&#xe8;" k="35" />
    <hkern u1="X" u2="&#xe7;" k="35" />
    <hkern u1="X" u2="&#xe6;" k="35" />
    <hkern u1="X" u2="&#xe5;" k="35" />
    <hkern u1="X" u2="&#xe4;" k="35" />
    <hkern u1="X" u2="&#xe3;" k="35" />
    <hkern u1="X" u2="&#xe2;" k="35" />
    <hkern u1="X" u2="&#xe1;" k="35" />
    <hkern u1="X" u2="&#xe0;" k="35" />
    <hkern u1="X" u2="q" k="35" />
    <hkern u1="X" u2="o" k="35" />
    <hkern u1="X" u2="g" k="35" />
    <hkern u1="X" u2="e" k="35" />
    <hkern u1="X" u2="d" k="35" />
    <hkern u1="X" u2="c" k="35" />
    <hkern u1="X" u2="a" k="35" />
    <hkern u1="b" u2="Y" k="95" />
    <hkern u1="b" u2="X" k="35" />
    <hkern u1="e" u2="Y" k="95" />
    <hkern u1="e" u2="X" k="35" />
    <hkern u1="k" u2="&#x153;" k="30" />
    <hkern u1="k" u2="&#x151;" k="30" />
    <hkern u1="k" u2="&#x14d;" k="30" />
    <hkern u1="k" u2="&#x123;" k="30" />
    <hkern u1="k" u2="&#x121;" k="30" />
    <hkern u1="k" u2="&#x11f;" k="30" />
    <hkern u1="k" u2="&#x11b;" k="30" />
    <hkern u1="k" u2="&#x119;" k="30" />
    <hkern u1="k" u2="&#x117;" k="30" />
    <hkern u1="k" u2="&#x113;" k="30" />
    <hkern u1="k" u2="&#x111;" k="30" />
    <hkern u1="k" u2="&#x10f;" k="30" />
    <hkern u1="k" u2="&#x10d;" k="30" />
    <hkern u1="k" u2="&#x10b;" k="30" />
    <hkern u1="k" u2="&#x107;" k="30" />
    <hkern u1="k" u2="&#x105;" k="30" />
    <hkern u1="k" u2="&#x103;" k="30" />
    <hkern u1="k" u2="&#x101;" k="30" />
    <hkern u1="k" u2="&#xf8;" k="30" />
    <hkern u1="k" u2="&#xf6;" k="30" />
    <hkern u1="k" u2="&#xf5;" k="30" />
    <hkern u1="k" u2="&#xf4;" k="30" />
    <hkern u1="k" u2="&#xf3;" k="30" />
    <hkern u1="k" u2="&#xf2;" k="30" />
    <hkern u1="k" u2="&#xeb;" k="30" />
    <hkern u1="k" u2="&#xea;" k="30" />
    <hkern u1="k" u2="&#xe9;" k="30" />
    <hkern u1="k" u2="&#xe8;" k="30" />
    <hkern u1="k" u2="&#xe7;" k="30" />
    <hkern u1="k" u2="&#xe6;" k="30" />
    <hkern u1="k" u2="&#xe5;" k="30" />
    <hkern u1="k" u2="&#xe4;" k="30" />
    <hkern u1="k" u2="&#xe3;" k="30" />
    <hkern u1="k" u2="&#xe2;" k="30" />
    <hkern u1="k" u2="&#xe1;" k="30" />
    <hkern u1="k" u2="&#xe0;" k="30" />
    <hkern u1="k" u2="q" k="30" />
    <hkern u1="k" u2="o" k="30" />
    <hkern u1="k" u2="g" k="30" />
    <hkern u1="k" u2="e" k="30" />
    <hkern u1="k" u2="d" k="30" />
    <hkern u1="k" u2="c" k="30" />
    <hkern u1="k" u2="a" k="30" />
    <hkern u1="o" u2="Y" k="95" />
    <hkern u1="o" u2="X" k="35" />
    <hkern u1="p" u2="Y" k="95" />
    <hkern u1="p" u2="X" k="35" />
    <hkern u1="&#xc0;" u2="f" k="20" />
    <hkern u1="&#xc1;" u2="f" k="20" />
    <hkern u1="&#xc2;" u2="f" k="20" />
    <hkern u1="&#xc3;" u2="f" k="20" />
    <hkern u1="&#xc4;" u2="f" k="20" />
    <hkern u1="&#xc5;" u2="f" k="20" />
    <hkern u1="&#xe6;" u2="Y" k="95" />
    <hkern u1="&#xe6;" u2="X" k="35" />
    <hkern u1="&#xe8;" u2="Y" k="95" />
    <hkern u1="&#xe8;" u2="X" k="35" />
    <hkern u1="&#xe9;" u2="Y" k="95" />
    <hkern u1="&#xe9;" u2="X" k="35" />
    <hkern u1="&#xea;" u2="Y" k="95" />
    <hkern u1="&#xea;" u2="X" k="35" />
    <hkern u1="&#xeb;" u2="Y" k="95" />
    <hkern u1="&#xeb;" u2="X" k="35" />
    <hkern u1="&#xf2;" u2="Y" k="95" />
    <hkern u1="&#xf2;" u2="X" k="35" />
    <hkern u1="&#xf3;" u2="Y" k="95" />
    <hkern u1="&#xf3;" u2="X" k="35" />
    <hkern u1="&#xf4;" u2="Y" k="95" />
    <hkern u1="&#xf4;" u2="X" k="35" />
    <hkern u1="&#xf5;" u2="Y" k="95" />
    <hkern u1="&#xf5;" u2="X" k="35" />
    <hkern u1="&#xf6;" u2="Y" k="95" />
    <hkern u1="&#xf6;" u2="X" k="35" />
    <hkern u1="&#xf8;" u2="Y" k="95" />
    <hkern u1="&#xf8;" u2="X" k="35" />
    <hkern u1="&#xfe;" u2="Y" k="95" />
    <hkern u1="&#xfe;" u2="X" k="35" />
    <hkern u1="&#x100;" u2="f" k="20" />
    <hkern u1="&#x102;" u2="f" k="20" />
    <hkern u1="&#x104;" u2="f" k="20" />
    <hkern u1="&#x113;" u2="Y" k="95" />
    <hkern u1="&#x113;" u2="X" k="35" />
    <hkern u1="&#x117;" u2="Y" k="95" />
    <hkern u1="&#x117;" u2="X" k="35" />
    <hkern u1="&#x119;" u2="Y" k="95" />
    <hkern u1="&#x119;" u2="X" k="35" />
    <hkern u1="&#x11b;" u2="Y" k="95" />
    <hkern u1="&#x11b;" u2="X" k="35" />
    <hkern u1="&#x14d;" u2="Y" k="95" />
    <hkern u1="&#x14d;" u2="X" k="35" />
    <hkern u1="&#x151;" u2="Y" k="95" />
    <hkern u1="&#x151;" u2="X" k="35" />
    <hkern u1="&#x153;" u2="Y" k="95" />
    <hkern u1="&#x153;" u2="X" k="35" />
    <hkern g1="at"
	g2="four"
	k="10" />
    <hkern g1="at"
	g2="six"
	k="20" />
    <hkern g1="at"
	g2="two"
	k="10" />
    <hkern g1="at"
	g2="zero"
	k="40" />
    <hkern g1="at"
	g2="eight"
	k="20" />
    <hkern g1="uni20BF"
	g2="nine"
	k="10" />
    <hkern g1="uni20BF"
	g2="one"
	k="10" />
    <hkern g1="uni20BF"
	g2="three"
	k="10" />
    <hkern g1="cent"
	g2="five"
	k="20" />
    <hkern g1="cent"
	g2="two"
	k="20" />
    <hkern g1="copyright,registered"
	g2="five"
	k="10" />
    <hkern g1="copyright,registered"
	g2="four"
	k="20" />
    <hkern g1="copyright,registered"
	g2="six"
	k="10" />
    <hkern g1="copyright,registered"
	g2="three"
	k="10" />
    <hkern g1="copyright,registered"
	g2="eight"
	k="10" />
    <hkern g1="currency,emptyset"
	g2="five"
	k="30" />
    <hkern g1="currency,emptyset"
	g2="seven"
	k="20" />
    <hkern g1="currency,emptyset"
	g2="zero"
	k="20" />
    <hkern g1="dollar"
	g2="five"
	k="10" />
    <hkern g1="dollar"
	g2="six"
	k="10" />
    <hkern g1="dollar"
	g2="eight"
	k="20" />
    <hkern g1="Euro"
	g2="five"
	k="10" />
    <hkern g1="Euro"
	g2="seven"
	k="10" />
    <hkern g1="franc"
	g2="five"
	k="50" />
    <hkern g1="franc"
	g2="nine"
	k="20" />
    <hkern g1="franc"
	g2="one"
	k="20" />
    <hkern g1="franc"
	g2="zero"
	k="40" />
    <hkern g1="franc"
	g2="eight"
	k="-20" />
    <hkern g1="lira"
	g2="seven"
	k="20" />
    <hkern g1="lira"
	g2="six"
	k="10" />
    <hkern g1="lira"
	g2="zero"
	k="10" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="two"
	k="10" />
    <hkern g1="paragraph"
	g2="eight"
	k="20" />
    <hkern g1="percent,perthousand"
	g2="five"
	k="-3009" />
    <hkern g1="backslash"
	g2="four"
	k="20" />
    <hkern g1="backslash"
	g2="seven"
	k="80" />
    <hkern g1="backslash"
	g2="six"
	k="40" />
    <hkern g1="backslash"
	g2="eight"
	k="-25" />
    <hkern g1="braceleft"
	g2="asterisk,degree,trademark"
	k="20" />
    <hkern g1="braceleft"
	g2="eight"
	k="40" />
    <hkern g1="braceright"
	g2="six"
	k="10" />
    <hkern g1="braceright"
	g2="five"
	k="-25" />
    <hkern g1="braceright"
	g2="asterisk,degree,trademark"
	k="10" />
    <hkern g1="bracketleft"
	g2="four"
	k="10" />
    <hkern g1="bracketleft"
	g2="six"
	k="50" />
    <hkern g1="bracketleft"
	g2="five"
	k="80" />
    <hkern g1="bracketleft"
	g2="nine"
	k="20" />
    <hkern g1="bracketleft"
	g2="one"
	k="70" />
    <hkern g1="bracketleft"
	g2="zero"
	k="70" />
    <hkern g1="bracketleft"
	g2="asterisk,degree,trademark"
	k="-20" />
    <hkern g1="bracketleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="five"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="nine"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="two"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="four"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="one"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="zero"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="two"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="nine"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="one"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="three"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="six"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="zero"
	k="-10" />
    <hkern g1="numbersign"
	g2="zero"
	k="-20" />
    <hkern g1="numbersign"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="numbersign"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="numbersign"
	g2="two"
	k="-20" />
    <hkern g1="numbersign"
	g2="three"
	k="-20" />
    <hkern g1="numbersign"
	g2="eight"
	k="60" />
    <hkern g1="parenleft"
	g2="six"
	k="40" />
    <hkern g1="parenleft"
	g2="nine"
	k="30" />
    <hkern g1="parenleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="parenright"
	g2="four"
	k="20" />
    <hkern g1="parenright"
	g2="seven"
	k="10" />
    <hkern g1="parenright"
	g2="six"
	k="60" />
    <hkern g1="parenright"
	g2="five"
	k="100" />
    <hkern g1="parenright"
	g2="nine"
	k="30" />
    <hkern g1="parenright"
	g2="two"
	k="20" />
    <hkern g1="parenright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="nine"
	k="-10" />
    <hkern g1="periodcentered,bullet"
	g2="zero"
	k="80" />
    <hkern g1="periodcentered,bullet"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="100" />
    <hkern g1="periodcentered,bullet"
	g2="two"
	k="-10" />
    <hkern g1="periodcentered,bullet"
	g2="three"
	k="80" />
    <hkern g1="periodcentered,bullet"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="question"
	g2="seven"
	k="30" />
    <hkern g1="question"
	g2="nine"
	k="30" />
    <hkern g1="question"
	g2="one"
	k="10" />
    <hkern g1="question"
	g2="zero"
	k="40" />
    <hkern g1="question"
	g2="asterisk,degree,trademark"
	k="40" />
    <hkern g1="question"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="30" />
    <hkern g1="question"
	g2="two"
	k="100" />
    <hkern g1="question"
	g2="three"
	k="50" />
    <hkern g1="five"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="five"
	g2="V"
	k="10" />
    <hkern g1="five"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="five"
	g2="X"
	k="10" />
    <hkern g1="five"
	g2="Euro"
	k="15" />
    <hkern g1="five"
	g2="hbar"
	k="10" />
    <hkern g1="five"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="five"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="five"
	g2="dollar"
	k="10" />
    <hkern g1="four"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="15" />
    <hkern g1="four"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="four"
	g2="at"
	k="10" />
    <hkern g1="four"
	g2="uni20BF"
	k="10" />
    <hkern g1="four"
	g2="cent"
	k="10" />
    <hkern g1="four"
	g2="eight"
	k="-20" />
    <hkern g1="four"
	g2="Euro"
	k="30" />
    <hkern g1="four"
	g2="hbar"
	k="10" />
    <hkern g1="four"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="four"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="four"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="four"
	g2="zero"
	k="-10" />
    <hkern g1="four"
	g2="copyright,registered"
	k="10" />
    <hkern g1="four"
	g2="dollar"
	k="10" />
    <hkern g1="nine"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="15" />
    <hkern g1="nine"
	g2="florin"
	k="10" />
    <hkern g1="nine"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="nine"
	g2="radical"
	k="10" />
    <hkern g1="nine"
	g2="parenright"
	k="10" />
    <hkern g1="nine"
	g2="Hbar"
	k="-20" />
    <hkern g1="nine"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="30" />
    <hkern g1="nine"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="nine"
	g2="at"
	k="10" />
    <hkern g1="nine"
	g2="uni20BF"
	k="10" />
    <hkern g1="nine"
	g2="braceleft"
	k="10" />
    <hkern g1="nine"
	g2="braceright"
	k="-10" />
    <hkern g1="nine"
	g2="bracketright"
	k="10" />
    <hkern g1="nine"
	g2="cent"
	k="10" />
    <hkern g1="nine"
	g2="eight"
	k="10" />
    <hkern g1="nine"
	g2="Euro"
	k="30" />
    <hkern g1="nine"
	g2="hbar"
	k="10" />
    <hkern g1="nine"
	g2="lira"
	k="10" />
    <hkern g1="one"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="one"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="one"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="one"
	g2="questiondown"
	k="10" />
    <hkern g1="one"
	g2="ampersand"
	k="10" />
    <hkern g1="one"
	g2="eth"
	k="-20" />
    <hkern g1="one"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="one"
	g2="florin"
	k="10" />
    <hkern g1="one"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="one"
	g2="radical"
	k="10" />
    <hkern g1="one"
	g2="six"
	k="10" />
    <hkern g1="one"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="-10" />
    <hkern g1="one"
	g2="three"
	k="10" />
    <hkern g1="one"
	g2="parenright"
	k="10" />
    <hkern g1="one"
	g2="Hbar"
	k="10" />
    <hkern g1="one"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="30" />
    <hkern g1="one"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="one"
	g2="Tbar"
	k="10" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="seven"
	g2="nine"
	k="10" />
    <hkern g1="seven"
	g2="percent,perthousand"
	k="10" />
    <hkern g1="seven"
	g2="question"
	k="10" />
    <hkern g1="seven"
	g2="x"
	k="10" />
    <hkern g1="seven"
	g2="guillemotleft,guilsinglleft"
	k="-20" />
    <hkern g1="seven"
	g2="guillemotright,guilsinglright"
	k="30" />
    <hkern g1="seven"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="seven"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="seven"
	g2="questiondown"
	k="10" />
    <hkern g1="seven"
	g2="seven"
	k="10" />
    <hkern g1="seven"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-10" />
    <hkern g1="seven"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="seven"
	g2="ampersand"
	k="10" />
    <hkern g1="seven"
	g2="eth"
	k="10" />
    <hkern g1="seven"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="seven"
	g2="florin"
	k="10" />
    <hkern g1="seven"
	g2="four"
	k="10" />
    <hkern g1="six"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="six"
	g2="asterisk,degree,trademark"
	k="10" />
    <hkern g1="six"
	g2="five"
	k="10" />
    <hkern g1="six"
	g2="one"
	k="10" />
    <hkern g1="six"
	g2="yen"
	k="10" />
    <hkern g1="six"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="six"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="six"
	g2="nine"
	k="10" />
    <hkern g1="six"
	g2="percent,perthousand"
	k="10" />
    <hkern g1="six"
	g2="question"
	k="10" />
    <hkern g1="six"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="six"
	g2="slash"
	k="-10" />
    <hkern g1="six"
	g2="two"
	k="10" />
    <hkern g1="six"
	g2="x"
	k="10" />
    <hkern g1="six"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="six"
	g2="guillemotright,guilsinglright"
	k="30" />
    <hkern g1="six"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="six"
	g2="paragraph"
	k="10" />
    <hkern g1="three"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="three"
	g2="X"
	k="-20" />
    <hkern g1="three"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="three"
	g2="asterisk,degree,trademark"
	k="10" />
    <hkern g1="three"
	g2="five"
	k="10" />
    <hkern g1="three"
	g2="one"
	k="10" />
    <hkern g1="three"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="three"
	g2="underscore"
	k="-10" />
    <hkern g1="three"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="three"
	g2="yen"
	k="10" />
    <hkern g1="three"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="three"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="three"
	g2="nine"
	k="10" />
    <hkern g1="three"
	g2="numbersign"
	k="10" />
    <hkern g1="three"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="three"
	g2="zero"
	k="30" />
    <hkern g1="three"
	g2="copyright,registered"
	k="10" />
    <hkern g1="three"
	g2="dollar"
	k="70" />
    <hkern g1="two"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="two"
	g2="V"
	k="10" />
    <hkern g1="two"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="two"
	g2="X"
	k="10" />
    <hkern g1="two"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="two"
	g2="asterisk,degree,trademark"
	k="10" />
    <hkern g1="two"
	g2="backslash"
	k="10" />
    <hkern g1="two"
	g2="braceleft"
	k="50" />
    <hkern g1="two"
	g2="braceright"
	k="30" />
    <hkern g1="two"
	g2="bracketright"
	k="10" />
    <hkern g1="two"
	g2="cent"
	k="70" />
    <hkern g1="two"
	g2="eight"
	k="80" />
    <hkern g1="two"
	g2="Euro"
	k="80" />
    <hkern g1="two"
	g2="lira"
	k="40" />
    <hkern g1="two"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-20" />
    <hkern g1="two"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="two"
	g2="zero"
	k="10" />
    <hkern g1="two"
	g2="copyright,registered"
	k="10" />
    <hkern g1="zero"
	g2="six"
	k="50" />
    <hkern g1="zero"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="30" />
    <hkern g1="zero"
	g2="three"
	k="10" />
    <hkern g1="zero"
	g2="parenright"
	k="70" />
    <hkern g1="zero"
	g2="Hbar"
	k="80" />
    <hkern g1="zero"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="80" />
    <hkern g1="zero"
	g2="Tbar"
	k="40" />
    <hkern g1="zero"
	g2="at"
	k="-20" />
    <hkern g1="zero"
	g2="braceleft"
	k="20" />
    <hkern g1="zero"
	g2="braceright"
	k="10" />
    <hkern g1="zero"
	g2="bracketright"
	k="10" />
    <hkern g1="zero"
	g2="eight"
	k="20" />
    <hkern g1="zero"
	g2="Euro"
	k="-10" />
    <hkern g1="zero"
	g2="hbar"
	k="50" />
    <hkern g1="zero"
	g2="lira"
	k="50" />
    <hkern g1="zero"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="40" />
    <hkern g1="zero"
	g2="copyright,registered"
	k="-10" />
    <hkern g1="zero"
	g2="dollar"
	k="-10" />
    <hkern g1="B,germandbls"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="B,germandbls"
	g2="J"
	k="30" />
    <hkern g1="B,germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="35" />
    <hkern g1="B,germandbls"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="50" />
    <hkern g1="B,germandbls"
	g2="T,uni0162,Tcaron,uni021A"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="Tbar"
	k="15" />
    <hkern g1="B,germandbls"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="65" />
    <hkern g1="B,germandbls"
	g2="V"
	k="35" />
    <hkern g1="B,germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,germandbls"
	g2="X"
	k="60" />
    <hkern g1="B,germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="85" />
    <hkern g1="B,germandbls"
	g2="ampersand"
	k="-20" />
    <hkern g1="B,germandbls"
	g2="asterisk,degree,trademark"
	k="80" />
    <hkern g1="B,germandbls"
	g2="at"
	k="20" />
    <hkern g1="B,germandbls"
	g2="backslash"
	k="30" />
    <hkern g1="B,germandbls"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="B,germandbls"
	g2="copyright,registered"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eight"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eth"
	k="10" />
    <hkern g1="B,germandbls"
	g2="five"
	k="40" />
    <hkern g1="B,germandbls"
	g2="four"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="guillemotleft,guilsinglleft"
	k="27" />
    <hkern g1="B,germandbls"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="B,germandbls"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="B,germandbls"
	g2="slash"
	k="15" />
    <hkern g1="B,germandbls"
	g2="braceright"
	k="10" />
    <hkern g1="B,germandbls"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="22" />
    <hkern g1="B,germandbls"
	g2="parenright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="B,germandbls"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="30" />
    <hkern g1="B,germandbls"
	g2="Hbar"
	k="20" />
    <hkern g1="B,germandbls"
	g2="bracketright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Eth,Dcroat"
	k="27" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="numbersign"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="one"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="paragraph"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="question"
	k="22" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="questiondown"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="section"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="six"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="three"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="parenleft"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="V"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="22" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="ampersand"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="asterisk,degree,trademark"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="at"
	k="30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eight"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eth"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="section"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="seven"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="six"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="slash"
	k="20" />
    <hkern g1="F"
	g2="Tbar"
	k="10" />
    <hkern g1="F"
	g2="X"
	k="10" />
    <hkern g1="F"
	g2="at"
	k="20" />
    <hkern g1="F"
	g2="backslash"
	k="20" />
    <hkern g1="F"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="F"
	g2="numbersign"
	k="10" />
    <hkern g1="F"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="5" />
    <hkern g1="F"
	g2="one"
	k="40" />
    <hkern g1="F"
	g2="paragraph"
	k="20" />
    <hkern g1="F"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="F"
	g2="slash"
	k="20" />
    <hkern g1="F"
	g2="braceright"
	k="5" />
    <hkern g1="F"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="F"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="F"
	g2="parenright"
	k="20" />
    <hkern g1="F"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="F"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="15" />
    <hkern g1="F"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="F"
	g2="Hbar"
	k="25" />
    <hkern g1="F"
	g2="two"
	k="40" />
    <hkern g1="F"
	g2="j"
	k="40" />
    <hkern g1="F"
	g2="braceleft"
	k="20" />
    <hkern g1="Hbar"
	g2="J"
	k="15" />
    <hkern g1="Hbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Hbar"
	g2="Tbar"
	k="5" />
    <hkern g1="Hbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="40" />
    <hkern g1="Hbar"
	g2="V"
	k="20" />
    <hkern g1="Hbar"
	g2="one"
	k="10" />
    <hkern g1="Hbar"
	g2="paragraph"
	k="20" />
    <hkern g1="Hbar"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="Hbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="Hbar"
	g2="question"
	k="30" />
    <hkern g1="Hbar"
	g2="questiondown"
	k="20" />
    <hkern g1="Hbar"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="Hbar"
	g2="section"
	k="15" />
    <hkern g1="Hbar"
	g2="seven"
	k="15" />
    <hkern g1="Hbar"
	g2="six"
	k="25" />
    <hkern g1="Hbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="Hbar"
	g2="x"
	k="40" />
    <hkern g1="Hbar"
	g2="zero"
	k="20" />
    <hkern g1="Hbar"
	g2="parenright"
	k="8" />
    <hkern g1="Hbar"
	g2="bracketright"
	k="40" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="K,uni0136"
	g2="V"
	k="20" />
    <hkern g1="K,uni0136"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="K,uni0136"
	g2="X"
	k="10" />
    <hkern g1="K,uni0136"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="K,uni0136"
	g2="ampersand"
	k="20" />
    <hkern g1="K,uni0136"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="K,uni0136"
	g2="at"
	k="15" />
    <hkern g1="K,uni0136"
	g2="backslash"
	k="15" />
    <hkern g1="K,uni0136"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="K,uni0136"
	g2="copyright,registered"
	k="40" />
    <hkern g1="K,uni0136"
	g2="four"
	k="40" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="K,uni0136"
	g2="questiondown"
	k="8" />
    <hkern g1="K,uni0136"
	g2="three"
	k="40" />
    <hkern g1="K,uni0136"
	g2="slash"
	k="25" />
    <hkern g1="K,uni0136"
	g2="braceright"
	k="10" />
    <hkern g1="K,uni0136"
	g2="exclam,exclamdown"
	k="30" />
    <hkern g1="K,uni0136"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="15" />
    <hkern g1="K,uni0136"
	g2="parenright"
	k="5" />
    <hkern g1="K,uni0136"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="K,uni0136"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="ampersand"
	k="8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="eight"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="paragraph"
	k="25" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="questiondown"
	k="5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="section"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="parenright"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="V"
	k="25" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="X"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="ampersand"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="asterisk,degree,trademark"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="at"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="questiondown"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="underscore"
	k="-10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="braceright"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="parenright"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="hbar"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="P"
	g2="ampersand"
	k="15" />
    <hkern g1="P"
	g2="asterisk,degree,trademark"
	k="20" />
    <hkern g1="P"
	g2="nine"
	k="-10" />
    <hkern g1="P"
	g2="one"
	k="5" />
    <hkern g1="P"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="P"
	g2="questiondown"
	k="10" />
    <hkern g1="P"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="P"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="P"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="P"
	g2="bracketright"
	k="-10" />
    <hkern g1="P"
	g2="hbar"
	k="-10" />
    <hkern g1="P"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="P"
	g2="j"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="ampersand"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="eth"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="five"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="three"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="x"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Hbar"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="two"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="asterisk,degree,trademark"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="eight"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="eth"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="five"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="four"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="section"
	k="40" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="seven"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="six"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="colon,semicolon"
	k="100" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="braceright"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="parenright"
	k="20" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="two"
	k="20" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="at"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="backslash"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="copyright,registered"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="numbersign"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="one"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="questiondown"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="parenright"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Hbar"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="two"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="hbar"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="braceleft"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="parenleft"
	k="15" />
    <hkern g1="Tbar"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="Tbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Tbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="100" />
    <hkern g1="Tbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Tbar"
	g2="ampersand"
	k="20" />
    <hkern g1="Tbar"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Tbar"
	g2="five"
	k="-10" />
    <hkern g1="Tbar"
	g2="hyphen,endash,emdash"
	k="60" />
    <hkern g1="Tbar"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-20" />
    <hkern g1="Tbar"
	g2="one"
	k="10" />
    <hkern g1="Tbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="15" />
    <hkern g1="Tbar"
	g2="questiondown"
	k="10" />
    <hkern g1="Tbar"
	g2="section"
	k="50" />
    <hkern g1="Tbar"
	g2="six"
	k="80" />
    <hkern g1="Tbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="15" />
    <hkern g1="Tbar"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="Tbar"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="90" />
    <hkern g1="Tbar"
	g2="zero"
	k="-10" />
    <hkern g1="Tbar"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="Tbar"
	g2="underscore"
	k="5" />
    <hkern g1="Tbar"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="Tbar"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="15" />
    <hkern g1="Tbar"
	g2="two"
	k="5" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="60" />
    <hkern g1="Thorn"
	g2="Tbar"
	k="-20" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Thorn"
	g2="X"
	k="15" />
    <hkern g1="Thorn"
	g2="ampersand"
	k="10" />
    <hkern g1="Thorn"
	g2="at"
	k="50" />
    <hkern g1="Thorn"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="80" />
    <hkern g1="Thorn"
	g2="copyright,registered"
	k="15" />
    <hkern g1="Thorn"
	g2="eth"
	k="30" />
    <hkern g1="Thorn"
	g2="five"
	k="90" />
    <hkern g1="Thorn"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="Thorn"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="Thorn"
	g2="nine"
	k="5" />
    <hkern g1="Thorn"
	g2="numbersign"
	k="10" />
    <hkern g1="Thorn"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="15" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="Thorn"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="J"
	k="15" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Tbar"
	k="15" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="copyright,registered"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="seven"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="braceright"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="hbar"
	k="10" />
    <hkern g1="V"
	g2="backslash"
	k="10" />
    <hkern g1="V"
	g2="periodcentered,bullet"
	k="-10" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="V"
	g2="hbar"
	k="20" />
    <hkern g1="V"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="35" />
    <hkern g1="V"
	g2="j"
	k="67" />
    <hkern g1="V"
	g2="braceleft"
	k="45" />
    <hkern g1="V"
	g2="parenleft"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="eth"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="67" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="zero"
	k="45" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="underscore"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="-30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Hbar"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="two"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bracketright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hbar"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceleft"
	k="40" />
    <hkern g1="X"
	g2="eth"
	k="20" />
    <hkern g1="X"
	g2="five"
	k="35" />
    <hkern g1="X"
	g2="four"
	k="67" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="45" />
    <hkern g1="X"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="X"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="X"
	g2="nine"
	k="40" />
    <hkern g1="X"
	g2="numbersign"
	k="30" />
    <hkern g1="X"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="X"
	g2="one"
	k="10" />
    <hkern g1="X"
	g2="paragraph"
	k="30" />
    <hkern g1="X"
	g2="periodcentered,bullet"
	k="40" />
    <hkern g1="X"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="X"
	g2="question"
	k="30" />
    <hkern g1="X"
	g2="questiondown"
	k="10" />
    <hkern g1="X"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-30" />
    <hkern g1="X"
	g2="section"
	k="80" />
    <hkern g1="X"
	g2="seven"
	k="20" />
    <hkern g1="X"
	g2="six"
	k="40" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="X"
	g2="three"
	k="40" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="60" />
    <hkern g1="X"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="X"
	g2="x"
	k="60" />
    <hkern g1="X"
	g2="zero"
	k="40" />
    <hkern g1="X"
	g2="underscore"
	k="20" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="X"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="40" />
    <hkern g1="X"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="X"
	g2="slash"
	k="40" />
    <hkern g1="X"
	g2="exclam,exclamdown"
	k="30" />
    <hkern g1="X"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="X"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="X"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="40" />
    <hkern g1="X"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="X"
	g2="Hbar"
	k="40" />
    <hkern g1="X"
	g2="two"
	k="65" />
    <hkern g1="X"
	g2="hbar"
	k="50" />
    <hkern g1="X"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="X"
	g2="braceleft"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Eth,Dcroat"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Tbar"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="X"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ampersand"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk,degree,trademark"
	k="-30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eight"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eth"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="five"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="four"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="nine"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="one"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="paragraph"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="question"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="section"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="seven"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="six"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="zero"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Eth,Dcroat"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Tbar"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="V"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="X"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="asterisk,degree,trademark"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="at"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="backslash"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="copyright,registered"
	k="65" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="eth"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="five"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="paragraph"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="questiondown"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="underscore"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="90" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="80" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="slash"
	k="90" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceright"
	k="80" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="100" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="parenright"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="160" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="150" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Hbar"
	k="-40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="two"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceleft"
	k="70" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="three"
	k="70" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="zero"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Eth,Dcroat"
	k="65" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Hbar"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="braceright"
	k="90" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="bracketright"
	k="25" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="exclam,exclamdown"
	k="80" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="numbersign"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="parenright"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="periodcentered,bullet"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="yen"
	k="20" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="j"
	k="10" />
    <hkern g1="dcroat"
	g2="two"
	k="70" />
    <hkern g1="dcroat"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="dcroat"
	g2="ampersand"
	k="65" />
    <hkern g1="dcroat"
	g2="at"
	k="30" />
    <hkern g1="dcroat"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="90" />
    <hkern g1="dcroat"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="dcroat"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="80" />
    <hkern g1="dcroat"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="dcroat"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="30" />
    <hkern g1="dcroat"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="dcroat"
	g2="copyright,registered"
	k="20" />
    <hkern g1="dcroat"
	g2="eight"
	k="20" />
    <hkern g1="dcroat"
	g2="eth"
	k="10" />
    <hkern g1="dcroat"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="dcroat"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="5" />
    <hkern g1="dcroat"
	g2="section"
	k="20" />
    <hkern g1="dcroat"
	g2="three"
	k="-10" />
    <hkern g1="dcroat"
	g2="zero"
	k="20" />
    <hkern g1="dcroat"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="dcroat"
	g2="Hbar"
	k="10" />
    <hkern g1="dcroat"
	g2="braceright"
	k="5" />
    <hkern g1="eth"
	g2="X"
	k="70" />
    <hkern g1="eth"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="eth"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="30" />
    <hkern g1="eth"
	g2="questiondown"
	k="90" />
    <hkern g1="eth"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="eth"
	g2="slash"
	k="80" />
    <hkern g1="eth"
	g2="x"
	k="30" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="eth"
	g2="four"
	k="30" />
    <hkern g1="eth"
	g2="six"
	k="20" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="20" />
    <hkern g1="eth"
	g2="J"
	k="10" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="eth"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="eth"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="eth"
	g2="two"
	k="-10" />
    <hkern g1="eth"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="eth"
	g2="ampersand"
	k="30" />
    <hkern g1="eth"
	g2="at"
	k="10" />
    <hkern g1="eth"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="f,f_f"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="f,f_f"
	g2="T,uni0162,Tcaron,uni021A"
	k="25" />
    <hkern g1="f,f_f"
	g2="Tbar"
	k="80" />
    <hkern g1="f,f_f"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="f,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="f,f_f"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="f,f_f"
	g2="backslash"
	k="20" />
    <hkern g1="f,f_f"
	g2="five"
	k="20" />
    <hkern g1="f,f_f"
	g2="one"
	k="10" />
    <hkern g1="f,f_f"
	g2="question"
	k="10" />
    <hkern g1="f,f_f"
	g2="seven"
	k="5" />
    <hkern g1="f,f_f"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="f,f_f"
	g2="X"
	k="-10" />
    <hkern g1="f,f_f"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="f,f_f"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="f,f_f"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="f,f_f"
	g2="questiondown"
	k="5" />
    <hkern g1="f,f_f"
	g2="yen"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="5" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="eight"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="bracketright"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="k,uni0137"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="three"
	k="10" />
    <hkern g1="k,uni0137"
	g2="zero"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="five"
	k="-10" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="two"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="T,uni0162,Tcaron,uni021A"
	k="-20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="X"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="hbar"
	k="40" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="numbersign"
	k="30" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="parenright"
	k="35" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="quotedbl,quotesingle"
	k="55" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="yen"
	k="20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="nine"
	k="40" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="35" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="copyright,registered"
	k="55" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="eight"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="eth"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="three"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="zero"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="braceright"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="exclam,exclamdown"
	k="25" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="hbar"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="numbersign"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="parenright"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="periodcentered,bullet"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="quotedbl,quotesingle"
	k="25" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="j"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="underscore"
	k="40" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="x"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="35" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="four"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="six"
	k="55" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="J"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="two"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="ampersand"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="nine"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="copyright,registered"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="15" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="section"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="three"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="zero"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Hbar"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="V"
	k="40" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="backslash"
	k="55" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="five"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="one"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="X"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="questiondown"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="slash"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="underscore"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="x"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="four"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="six"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="15" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="two"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="ampersand"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="at"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Tbar"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="V"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="backslash"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="question"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="seven"
	k="15" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="X"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="hbar"
	k="-20" />
    <hkern g1="tcaron"
	g2="nine"
	k="-20" />
    <hkern g1="tcaron"
	g2="section"
	k="70" />
    <hkern g1="tcaron"
	g2="zero"
	k="20" />
    <hkern g1="tcaron"
	g2="Eth,Dcroat"
	k="5" />
    <hkern g1="tcaron"
	g2="Hbar"
	k="20" />
    <hkern g1="tcaron"
	g2="braceright"
	k="40" />
    <hkern g1="tcaron"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="tcaron"
	g2="hbar"
	k="30" />
    <hkern g1="tcaron"
	g2="numbersign"
	k="20" />
    <hkern g1="tcaron"
	g2="parenright"
	k="50" />
    <hkern g1="tcaron"
	g2="periodcentered,bullet"
	k="20" />
    <hkern g1="tcaron"
	g2="yen"
	k="-21" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="underscore"
	k="-20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="70" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="ampersand"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="at"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="40" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="nine"
	k="30" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="50" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="eight"
	k="-21" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="section"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="three"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="zero"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Hbar"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="braceright"
	k="-10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="bracketright"
	k="25" />
    <hkern g1="x"
	g2="V"
	k="-20" />
    <hkern g1="x"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="70" />
    <hkern g1="x"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="x"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="x"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="x"
	g2="questiondown"
	k="40" />
    <hkern g1="x"
	g2="slash"
	k="10" />
    <hkern g1="x"
	g2="underscore"
	k="30" />
    <hkern g1="x"
	g2="x"
	k="20" />
    <hkern g1="x"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="x"
	g2="four"
	k="20" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-21" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="x"
	g2="two"
	k="10" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="5" />
    <hkern g1="x"
	g2="ampersand"
	k="10" />
    <hkern g1="x"
	g2="at"
	k="10" />
    <hkern g1="x"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="x"
	g2="j"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="40" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Tbar"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="asterisk,degree,trademark"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="five"
	k="-21" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="X"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="questiondown"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="section"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="three"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="zero"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="bracketright"
	k="10" />
    <hkern g1="at"
	g2="J"
	k="40" />
    <hkern g1="at"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="at"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="at"
	g2="Tbar"
	k="10" />
    <hkern g1="at"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="35" />
    <hkern g1="at"
	g2="V"
	k="10" />
    <hkern g1="at"
	g2="eth"
	k="10" />
    <hkern g1="at"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="at"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="70" />
    <hkern g1="at"
	g2="j"
	k="30" />
    <hkern g1="copyright,registered"
	g2="nine"
	k="10" />
    <hkern g1="copyright,registered"
	g2="seven"
	k="10" />
    <hkern g1="copyright,registered"
	g2="six"
	k="70" />
    <hkern g1="copyright,registered"
	g2="two"
	k="30" />
    <hkern g1="copyright,registered"
	g2="Eth,Dcroat"
	k="60" />
    <hkern g1="copyright,registered"
	g2="eth"
	k="30" />
    <hkern g1="copyright,registered"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="30" />
    <hkern g1="copyright,registered"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="80" />
    <hkern g1="copyright,registered"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="copyright,registered"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="florin"
	g2="x"
	k="10" />
    <hkern g1="florin"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="florin"
	g2="z,zacute,zdotaccent,zcaron"
	k="70" />
    <hkern g1="florin"
	g2="eight"
	k="30" />
    <hkern g1="florin"
	g2="four"
	k="60" />
    <hkern g1="florin"
	g2="nine"
	k="30" />
    <hkern g1="florin"
	g2="one"
	k="30" />
    <hkern g1="florin"
	g2="seven"
	k="80" />
    <hkern g1="florin"
	g2="six"
	k="10" />
    <hkern g1="florin"
	g2="zero"
	k="20" />
    <hkern g1="florin"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="florin"
	g2="eth"
	k="15" />
    <hkern g1="florin"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="florin"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="paragraph"
	g2="V"
	k="10" />
    <hkern g1="paragraph"
	g2="X"
	k="10" />
    <hkern g1="paragraph"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="paragraph"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="paragraph"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="60" />
    <hkern g1="paragraph"
	g2="x"
	k="30" />
    <hkern g1="paragraph"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="paragraph"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="80" />
    <hkern g1="paragraph"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="paragraph"
	g2="five"
	k="20" />
    <hkern g1="paragraph"
	g2="four"
	k="20" />
    <hkern g1="paragraph"
	g2="nine"
	k="15" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="seven"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="70" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="V"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="X"
	k="80" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="x"
	k="15" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="section"
	g2="J"
	k="30" />
    <hkern g1="section"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="80" />
    <hkern g1="section"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="section"
	g2="Tbar"
	k="20" />
    <hkern g1="section"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="section"
	g2="V"
	k="15" />
    <hkern g1="section"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="section"
	g2="X"
	k="10" />
    <hkern g1="section"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="section"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="section"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="section"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="backslash"
	g2="Tbar"
	k="60" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="backslash"
	g2="V"
	k="20" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="backslash"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="backslash"
	g2="j"
	k="20" />
    <hkern g1="backslash"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="100" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="100" />
    <hkern g1="backslash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="60" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="120" />
    <hkern g1="backslash"
	g2="hbar"
	k="80" />
    <hkern g1="backslash"
	g2="X"
	k="10" />
    <hkern g1="backslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-70" />
    <hkern g1="backslash"
	g2="x"
	k="10" />
    <hkern g1="backslash"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="30" />
    <hkern g1="backslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="backslash"
	g2="Hbar"
	k="70" />
    <hkern g1="braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="80" />
    <hkern g1="braceleft"
	g2="J"
	k="10" />
    <hkern g1="braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-70" />
    <hkern g1="braceleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="braceleft"
	g2="eth"
	k="30" />
    <hkern g1="braceleft"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="braceleft"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="70" />
    <hkern g1="braceleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="braceleft"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-10" />
    <hkern g1="braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="braceright"
	g2="eth"
	k="-10" />
    <hkern g1="braceright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-10" />
    <hkern g1="braceright"
	g2="Tbar"
	k="-70" />
    <hkern g1="braceright"
	g2="j"
	k="-10" />
    <hkern g1="braceright"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="-10" />
    <hkern g1="bracketleft"
	g2="j"
	k="20" />
    <hkern g1="bracketleft"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="bracketleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="40" />
    <hkern g1="bracketright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="40" />
    <hkern g1="bracketright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="bracketright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="bracketright"
	g2="hbar"
	k="-10" />
    <hkern g1="bracketright"
	g2="z,zacute,zdotaccent,zcaron"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Tbar"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="exclam,exclamdown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="j"
	k="110" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="80" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="90" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="40" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="hbar"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="60" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="130" />
    <hkern g1="guillemotright,guilsinglright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="eth"
	k="60" />
    <hkern g1="guillemotright,guilsinglright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="130" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Eth,Dcroat"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="j"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="X"
	k="30" />
    <hkern g1="numbersign"
	g2="J"
	k="30" />
    <hkern g1="numbersign"
	g2="Tbar"
	k="40" />
    <hkern g1="numbersign"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="numbersign"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="numbersign"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="numbersign"
	g2="hbar"
	k="50" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="x"
	k="10" />
    <hkern g1="parenleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="50" />
    <hkern g1="parenleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="30" />
    <hkern g1="parenleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="parenleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="parenleft"
	g2="V"
	k="10" />
    <hkern g1="parenleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="40" />
    <hkern g1="parenleft"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="30" />
    <hkern g1="parenleft"
	g2="hbar"
	k="30" />
    <hkern g1="parenleft"
	g2="X"
	k="20" />
    <hkern g1="parenright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="parenright"
	g2="J"
	k="20" />
    <hkern g1="parenright"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="50" />
    <hkern g1="parenright"
	g2="Tbar"
	k="30" />
    <hkern g1="parenright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="parenright"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="35" />
    <hkern g1="parenright"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="parenright"
	g2="z,zacute,zdotaccent,zcaron"
	k="60" />
    <hkern g1="parenright"
	g2="Hbar"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="50" />
    <hkern g1="periodcentered,bullet"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="35" />
    <hkern g1="periodcentered,bullet"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="question"
	g2="eth"
	k="35" />
    <hkern g1="question"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="question"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="question"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="question"
	g2="hbar"
	k="-10" />
    <hkern g1="question"
	g2="x"
	k="30" />
    <hkern g1="question"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="questiondown"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="questiondown"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="30" />
    <hkern g1="questiondown"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="questiondown"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="questiondown"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="questiondown"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="12" />
    <hkern g1="questiondown"
	g2="Hbar"
	k="-70" />
    <hkern g1="quotedbl,quotesingle"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-70" />
    <hkern g1="quotedbl,quotesingle"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="V"
	k="-10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="slash"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="slash"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="slash"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="60" />
    <hkern g1="slash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="slash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="slash"
	g2="Hbar"
	k="40" />
    <hkern g1="underscore"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="underscore"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="20" />
    <hkern g1="underscore"
	g2="j"
	k="80" />
    <hkern g1="underscore"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="underscore"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="underscore"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="underscore"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="underscore"
	g2="hbar"
	k="30" />
    <hkern g1="underscore"
	g2="Hbar"
	k="20" />
  </font>
</defs></svg>
