<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Sat Dec 15 10:02:57 2018
 By deploy user
Copyright &#194;&#169; 2018 by Piotr &#197;&#129;apa. All rights reserved.
</metadata>
<defs>
<font id="Gilmer-Bold" horiz-adv-x="619" >
  <font-face 
    font-family="Gilmer Bold"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 8 0 0 0 0 0 0 0"
    ascent="790"
    descent="-210"
    x-height="506"
    cap-height="700"
    bbox="-388 -265 1457 979"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-U+FB02"
  />
<missing-glyph horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="630" 
d="M578 506v-506h-133v390h-197v-390h-132v390h-97v116h97v46q0 90 51.5 141t150.5 51q104 0 177 -47l-32 -106q-65 38 -131 38q-37 0 -60.5 -21t-23.5 -56v-46h330z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="643" 
d="M320 744q150 0 258 -68v-676h-133v606q-50 23 -112 23q-38 0 -61.5 -20.5t-23.5 -56.5v-46h130v-116h-130v-390h-132v390h-97v116h97v46q0 90 52 141t152 51z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="741" 
d="M578 552v-46h153v-116h-153v-390h-133v390h-197v-390h-132v390h-97v116h97v46q0 90 45.5 141t132.5 51q67 0 114 -22l-32 -106q-30 13 -63 13q-31 0 -48 -20.5t-17 -56.5v-46h197v46q0 90 46 141t133 51q66 0 113 -22l-31 -106q-30 13 -63 13q-31 0 -48 -20.5t-17 -56.5z
" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="960" 
d="M907 506v-506h-132v390h-197v-390h-133v390h-197v-390h-132v390h-97v116h97v46q0 90 48 141t140 51q67 0 114 -22l-32 -106q-30 13 -63 13t-54 -21t-21 -56v-46h197v46q0 90 52 141t151 51q102 0 177 -47l-32 -106q-65 38 -131 38q-38 0 -61 -20.5t-23 -56.5v-46h329z
" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="972" 
d="M650 744q149 0 257 -68v-676h-132v606q-50 23 -112 23q-39 0 -62 -20.5t-23 -56.5v-46h130v-116h-130v-390h-133v390h-197v-390h-132v390h-97v116h97v46q0 90 47.5 141t138.5 51q66 0 113 -22l-32 -106q-29 13 -62 13q-35 0 -54 -20.5t-19 -56.5v-46h197v46
q0 90 52.5 141t152.5 51z" />
    <glyph glyph-name=".notdef" horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="246" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="276" 
d="M85 228l-18 472h144l-18 -472h-108zM138 -8q-34 0 -58 24t-24 58q0 32 24 55t58 23t58 -23t24 -55q0 -34 -24 -58t-58 -24z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="386" 
d="M61 467l-16 233h126l-15 -233h-95zM231 467l-16 233h126l-15 -233h-95z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="724" 
d="M682 399h-134l-20 -127h134l-16 -101h-134l-27 -171h-110l27 171h-131l-27 -171h-110l27 171h-135l16 101h135l20 127h-135l16 100h134l28 176h110l-28 -176h131l28 176h110l-28 -176h135zM418 272l20 127h-131l-20 -127h131z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="663" 
d="M625 203q0 -84 -67 -144.5t-176 -70.5v-133h-114v138q-92 19 -156 75.5t-89 129.5l130 40q23 -58 75 -94.5t125 -36.5q59 0 96.5 26t37.5 62q0 28 -21.5 48t-71.5 36l-178 56q-73 23 -116 68t-43 110q0 75 59 131.5t152 67.5v133h114v-138q82 -16 142.5 -63.5
t84.5 -109.5l-127 -37q-22 43 -68.5 69.5t-104.5 26.5q-50 0 -81.5 -22t-31.5 -54q0 -46 68 -66l174 -56q91 -28 139 -70.5t48 -121.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1047" 
d="M244 310q-85 0 -144.5 58t-59.5 141q0 84 59 141t145 57t145 -57t59 -141q0 -83 -59 -141t-145 -58zM243 0l454 700h106l-453 -700h-107zM244 408q44 0 73 29.5t29 71.5t-29 71t-73 29t-73 -28.5t-29 -71.5q0 -42 29 -71.5t73 -29.5zM803 -8q-85 0 -144 57.5t-59 140.5
q0 84 58.5 141.5t144.5 57.5q87 0 145.5 -57.5t58.5 -141.5q0 -83 -58.5 -140.5t-145.5 -57.5zM803 90q44 0 73 29t29 71q0 43 -28.5 72t-73.5 29q-44 0 -72.5 -29t-28.5 -72q0 -42 28.5 -71t72.5 -29z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="717" 
d="M712 0h-161l-62 66q-92 -80 -209 -80q-94 0 -159.5 57t-65.5 148q0 59 32.5 112t96.5 90q-54 64 -54 127q0 91 60.5 142.5t159.5 51.5q84 0 141.5 -48t68.5 -116l-118 -34q-20 81 -95 81q-38 0 -62.5 -22t-24.5 -57q0 -36 47 -85l175 -187q29 51 41 107l116 -33
q-17 -84 -71 -166zM294 106q58 0 114 46l-148 157q-73 -45 -73 -108q0 -39 29.5 -67t77.5 -28z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="216" 
d="M61 467l-16 233h126l-15 -233h-95z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="310" 
d="M294 -136h-129q-115 190 -115 440q0 251 115 440h129q-115 -191 -115 -440q0 -247 115 -440z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="310" 
d="M16 -136q115 193 115 440q0 249 -115 440h129q117 -189 115 -440q0 -252 -115 -440h-129z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="423" 
d="M378 596l-96 -50l96 -50l-41 -69l-90 57l4 -107h-80l4 107l-90 -57l-40 69l95 50l-95 50l40 68l90 -57l-4 107h80l-4 -107l90 57z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="558" 
d="M498 403v-105h-166v-166h-105v166h-167v105h167v167h105v-167h166z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="273" 
d="M45 -137l44 277h139l-78 -277h-105z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="485" 
d="M60 252v109h365v-109h-365z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="253" 
d="M127 -8q-35 0 -58.5 24t-23.5 58q0 32 24 55t58 23t57.5 -23t23.5 -55q0 -34 -23.5 -58t-57.5 -24z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="561" 
d="M30 -91l396 881h105l-396 -881h-105z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="649" 
d="M325 -14q-136 0 -213 101.5t-77 262.5q0 162 77 263t213 101t212.5 -101t76.5 -263q0 -161 -76.5 -262.5t-212.5 -101.5zM325 111q75 0 113.5 64t38.5 175t-38.5 175t-113.5 64t-114 -64t-39 -175t39 -175t114 -64z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="426" 
d="M230 700h117v-700h-139v547l-157 -73l-33 117z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="586" 
d="M246 124h297v-124h-506v94l273 257q44 41 65 71t21 65q0 48 -29 76t-81 28q-48 0 -82.5 -31.5t-45.5 -83.5l-123 35q11 83 79.5 143t168.5 60q112 0 180.5 -58t68.5 -162q0 -62 -31 -112.5t-97 -111.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="601" 
d="M376 434q83 -13 133.5 -68.5t50.5 -142.5q0 -100 -72.5 -168.5t-181.5 -68.5q-119 0 -189 62t-86 150l123 35q9 -53 49 -90t96 -37q59 0 93.5 34.5t34.5 82.5q0 49 -32 81.5t-88 32.5q-42 0 -72 -17l-23 81l142 176h-301v123h481v-92z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="613" 
d="M592 257v-121h-87v-136h-137v136h-338v100l241 464h148l-228 -443h177v143h137v-143h87z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="594" 
d="M323 456q101 0 167 -66t66 -165q0 -101 -72 -170t-181 -69q-110 0 -178 55.5t-91 137.5l123 34q16 -47 52.5 -77t85.5 -30q59 0 94 34.5t35 86.5t-34.5 86t-95.5 34q-71 0 -124 -40l-108 33l30 360h431v-123h-309l-11 -145q53 24 120 24z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="586" 
d="M313 477q109 -6 177.5 -74t68.5 -169q0 -106 -75.5 -177t-190.5 -71q-114 0 -189.5 71t-75.5 177q0 70 51 152l203 314h155l-145 -224q7 1 21 1zM293 109q58 0 94 35.5t36 89.5q0 53 -36 88.5t-94 35.5q-57 0 -93 -35.5t-36 -88.5q0 -54 35.5 -89.5t93.5 -35.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="541" 
d="M16 700h503v-95l-265 -605h-146l254 576h-346v124z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="612" 
d="M456 381q121 -54 121 -179q0 -92 -75 -154t-196 -62t-196 61.5t-75 154.5q0 126 121 179q-91 46 -91 146q0 79 66 133t175 54q108 0 174.5 -54t66.5 -133q0 -100 -91 -146zM306 605q-50 0 -79 -26t-29 -64t29 -64t79 -26q49 0 78.5 26t29.5 64t-29.5 64t-78.5 26z
M306 102q61 0 97 31.5t36 79.5t-36 79.5t-97 31.5q-62 0 -97.5 -31t-35.5 -80q0 -48 36 -79.5t97 -31.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="586" 
d="M293 714q115 0 190.5 -71t75.5 -177q0 -70 -52 -152l-203 -314h-154l144 224h-20q-109 5 -177.5 73t-68.5 169q0 106 75 177t190 71zM293 343q58 0 94 35.5t36 87.5q0 54 -36 89.5t-94 35.5t-93.5 -35.5t-35.5 -89.5q0 -53 35.5 -88t93.5 -35z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="253" 
d="M127 349q-35 0 -58.5 23.5t-23.5 57.5q0 32 24 55.5t58 23.5t57.5 -23.5t23.5 -55.5q0 -34 -23.5 -57.5t-57.5 -23.5zM127 -8q-35 0 -58.5 24t-23.5 58q0 32 24 55t58 23t57.5 -23t23.5 -55q0 -34 -23.5 -58t-57.5 -24z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="277" 
d="M151 350q-35 0 -58.5 23.5t-23.5 57.5q0 32 24 55t58 23t57.5 -23t23.5 -55q0 -34 -23.5 -57.5t-57.5 -23.5zM45 -137l44 277h139l-78 -277h-105z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="553" 
d="M488 579v-114l-313 -114l313 -115v-114l-438 168v121z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="558" 
d="M60 396v105h438v-105h-438zM61 200v105h437v-105h-437z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="553" 
d="M65 579l438 -168v-121l-438 -168v114l313 115l-313 114v114z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="570" 
d="M210 228q0 42 18 77.5t43 59.5t50.5 45.5t43 46t17.5 50.5q0 38 -27 61.5t-73 23.5q-49 0 -82 -32.5t-45 -86.5l-120 36q13 86 78 145.5t170 59.5q103 0 169.5 -56.5t66.5 -143.5q0 -43 -18.5 -79.5t-45 -61t-53 -46.5t-45.5 -47t-19 -52h-128zM274 -8q-34 0 -58 24
t-24 58q0 32 24 55t58 23t57.5 -23t23.5 -55q0 -34 -23.5 -58t-57.5 -24z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="982" 
d="M483 -177q-192 0 -317.5 125t-125.5 308q0 123 61.5 225t166.5 160.5t230 58.5q194 0 319 -124.5t125 -314.5q0 -114 -58 -181.5t-146 -67.5q-103 0 -135 87q-54 -68 -140 -68q-90 0 -150 63.5t-60 160.5q0 98 60 161.5t151 63.5q82 0 134 -64v52h105v-312q0 -65 50 -65
q40 0 68 45.5t28 123.5q0 154 -96 253.5t-254 99.5q-154 0 -260.5 -103t-106.5 -254q0 -148 100.5 -247t252.5 -99q127 0 200 59l25 -85q-87 -61 -227 -61zM482 130q53 0 87 35t34 91q0 55 -34 89.5t-87 34.5q-54 0 -87 -34.5t-33 -89.5t33.5 -90.5t86.5 -35.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="722" 
d="M576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="692" 
d="M543 367q53 -24 83 -67.5t30 -101.5q0 -91 -63.5 -144.5t-174.5 -53.5h-353v700h334q103 0 166.5 -52t64.5 -135q0 -99 -87 -146zM398 582h-199v-173h202q42 0 68.5 25.5t25.5 62.5q0 37 -27 61.5t-70 23.5zM417 119q47 0 75 26t28 66q0 39 -27.5 64.5t-74.5 25.5h-219
v-182h218z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="775" 
d="M404 -14q-158 0 -263 104.5t-105 260.5q0 155 104.5 259t260.5 104q133 0 222.5 -67.5t116.5 -159.5l-134 -33q-18 55 -73 93t-131 38q-101 0 -163.5 -68t-62.5 -165q0 -98 63 -167.5t163 -69.5q76 0 131 37.5t73 92.5l133 -34q-26 -92 -115.5 -158.5t-219.5 -66.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="740" 
d="M369 700q152 0 243.5 -97t91.5 -253q0 -154 -91.5 -252t-243.5 -98h-304v700h304zM366 124q90 0 145 63.5t55 165.5q0 100 -55 162t-145 62h-165v-453h165z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="626" 
d="M581 576h-382v-160h331v-120h-331v-172h381v-124h-515v700h516v-124z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="590" 
d="M574 575h-372v-174h321v-123h-321v-278h-137v700h509v-125z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="799" 
d="M763 389v-70q0 -148 -98 -240.5t-256 -92.5q-161 0 -267 104.5t-106 260.5q0 154 105.5 258.5t261.5 104.5q124 0 212.5 -60.5t123.5 -153.5l-127 -36q-59 121 -208 121q-100 0 -164.5 -68t-64.5 -165q0 -100 65.5 -169t178.5 -69q85 0 139 44.5t63 112.5h-215v118h357z
" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="725" 
d="M522 700h138v-700h-138v294h-320v-294h-137v700h137v-282h320v282z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="269" 
d="M65 0v700h139v-700h-139z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="620" 
d="M561 700v-464q0 -114 -72 -182t-192 -68q-103 0 -172.5 51t-99.5 139l128 37q16 -49 52 -78t90 -29q60 0 95 38t35 96v340h-330v120h466z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="703" 
d="M526 0l-221 293l-103 -114v-179h-137v700h137v-339l295 339h174l-276 -306l298 -394h-167z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="574" 
d="M202 126h349v-126h-486v700h137v-574z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="850" 
d="M654 700h131v-700h-137v461l-223 -309l-223 309v-461h-137v700h131l229 -331z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="732" 
d="M530 700h137v-700h-113l-352 455v-455h-137v700h113l352 -454v454z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="807" 
d="M404 -14q-158 0 -263 104.5t-105 260.5q0 155 105 259t263 104t262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5zM404 113q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68t-165.5 -68t-63.5 -168q0 -102 63.5 -170t165.5 -68z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="656" 
d="M393 700q109 0 176 -74.5t67 -168.5q0 -96 -67.5 -169.5t-181.5 -73.5h-186v-214h-136v700h328zM383 336q53 0 86.5 36t33.5 84t-32.5 84t-81.5 36h-188v-240h182z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="803" 
d="M764 351q0 -140 -92 -243l89 -108h-137l-34 39q-87 -53 -190 -53q-152 0 -258 105.5t-106 259.5q0 153 106 258t258 105t258 -105t106 -258zM400 112q58 0 106 23l-160 183h154l89 -109q41 61 41 142q0 103 -64 170t-166 67t-166 -67t-64 -170q0 -104 64 -171.5
t166 -67.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="695" 
d="M509 0l-159 245h-149v-245h-136v700h341q108 0 174 -69.5t66 -158.5q0 -71 -40.5 -129.5t-112.5 -83.5l174 -259h-158zM201 577v-212h196q52 0 84 31t32 76q0 43 -31.5 74t-79.5 31h-201z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="668" 
d="M351 -14q-121 0 -206 61.5t-117 150.5l131 40q22 -58 74.5 -94.5t125.5 -36.5q59 0 96 26t37 62q0 28 -21 48t-71 36l-179 56q-73 23 -116 68t-42 110q-1 83 71 142t180 59q109 0 190 -51.5t111 -128.5l-127 -37q-22 43 -68.5 69.5t-104.5 26.5q-50 0 -81.5 -22
t-31.5 -54q0 -46 67 -66l174 -56q92 -28 140 -70.5t48 -121.5q0 -92 -78 -154.5t-202 -62.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="637" 
d="M622 700v-124h-235v-576h-137v576h-235v124h607z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="724" 
d="M527 700h138v-413q0 -136 -80 -218.5t-223 -82.5q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="712" 
d="M556 700h151l-276 -700h-150l-276 700h151l200 -526z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1001" 
d="M852 700h144l-212 -700h-145l-138 470l-138 -470h-145l-213 700h144l144 -507l146 507h123l146 -507z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="707" 
d="M697 0h-165l-178 249l-178 -249h-166l252 352l-246 348h166l172 -244l172 244h165l-245 -348z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="678" 
d="M520 700h154l-265 -443v-257h-139v257l-266 443h155l181 -311z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="634" 
d="M224 122h377v-122h-567v92l368 486h-361v122h550v-93z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="344" 
d="M319 628h-125v-647h125v-117h-254v880h254v-116z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="561" 
d="M531 -91h-105l-396 881h105z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="344" 
d="M279 744v-880h-254v117h125v647h-125v116h254z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="580" 
d="M547 393h-133l-123 220l-124 -220h-133l191 337h131z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="610" 
d="M38 -150v100h535v-100h-535z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="222" 
d="M222 585h-114l-108 140h146z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="645" 
d="M284 -14q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="645" 
d="M361 520q106 0 177.5 -76t71.5 -191q0 -114 -72 -190.5t-177 -76.5q-103 0 -164 76v-62h-132v730h132v-285q60 75 164 75zM338 104q62 0 102 42t40 108q0 65 -40 106.5t-102 41.5q-61 0 -102.5 -42t-41.5 -106q0 -65 41.5 -107.5t102.5 -42.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="580" 
d="M303 -14q-116 0 -192.5 77t-76.5 191t76.5 190t191.5 76q97 0 163.5 -51t83.5 -115l-123 -35q-9 34 -43 59t-81 25q-61 0 -99.5 -43.5t-38.5 -105.5q0 -61 38.5 -105.5t99.5 -44.5q47 0 81 24t43 57l123 -34q-17 -65 -83 -115t-163 -50z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="645" 
d="M447 730h133v-730h-133v62q-59 -76 -163 -76q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v285zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="595" 
d="M309 -14q-122 0 -198.5 73.5t-76.5 193.5q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5t-155.5 -43.5zM164 296h267q-5 48 -40.5 80t-92.5 32q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="411" 
d="M248 552v-46h153v-116h-153v-390h-132v390h-97v116h97v46q0 90 45.5 141t132.5 51q67 0 114 -22l-32 -106q-30 13 -63 13q-31 0 -48 -20.5t-17 -56.5z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="641" 
d="M444 506h132v-488q0 -107 -72 -174.5t-189 -67.5q-171 0 -244 133l115 41q41 -65 132 -65q60 0 92.5 37t33.5 96v61q-59 -73 -160 -73q-108 0 -179 74t-71 183q0 113 70.5 185t179.5 72q101 0 160 -73v59zM307 125q61 0 100.5 38.5t39.5 99.5t-39.5 99.5t-100.5 38.5
q-63 0 -102.5 -38.5t-39.5 -99.5t39.5 -99.5t102.5 -38.5z" />
    <glyph glyph-name="h" unicode="h" 
d="M360 520q97 0 148 -60t51 -164v-296h-132v271q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v730h132v-288q61 78 163 78z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="264" 
d="M132 577q-33 0 -56 22.5t-23 55.5q0 32 23 54t56 22t56 -22t23 -54q0 -33 -23 -55.5t-56 -22.5zM64 0v506h133v-506h-133z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="264" 
d="M132 577q-33 0 -56 22.5t-23 55.5q0 32 23 54t56 22t56 -22t23 -54q0 -33 -23 -55.5t-56 -22.5zM65 -27v533h132v-522q0 -98 -43.5 -153t-131.5 -55q-46 0 -95 22l30 102q22 -12 45 -12q63 0 63 85z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="558" 
d="M403 0l-147 195l-59 -66v-129h-132v730h132v-433l182 209h154l-194 -218l214 -288h-150z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="262" 
d="M65 0v730h132v-730h-132z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="930" 
d="M670 520q92 0 146 -57.5t54 -161.5v-301h-132v273q0 124 -97 124q-52 0 -79.5 -36.5t-27.5 -102.5v-258h-133v273q0 124 -95 124q-53 0 -81 -37.5t-28 -105.5v-254h-132v506h132v-60q57 74 150 74q103 0 152 -76q60 76 171 76z" />
    <glyph glyph-name="n" unicode="n" 
d="M360 520q97 0 148 -60t51 -164v-296h-132v271q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v506h132v-64q61 78 163 78z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="610" 
d="M305 -14q-117 0 -194 76.5t-77 190.5t77 190.5t194 76.5t193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5zM305 106q63 0 102.5 41.5t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5t-39.5 -105.5t39.5 -105.5t102.5 -41.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="645" 
d="M361 520q106 0 177.5 -76t71.5 -191q0 -114 -72 -190.5t-177 -76.5q-103 0 -164 76v-272h-132v716h132v-61q60 75 164 75zM338 104q62 0 102 42t40 108q0 65 -40 106.5t-102 41.5q-61 0 -102.5 -42t-41.5 -106q0 -65 41.5 -107.5t102.5 -42.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="645" 
d="M447 506h133v-716h-133v272q-59 -76 -163 -76q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="385" 
d="M197 431q50 98 167 82v-124q-83 13 -125 -28t-42 -141v-220h-132v506h132v-75z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="519" 
d="M264 -14q-90 0 -158 46t-88 110l119 35q13 -37 48 -60.5t84 -23.5q37 0 60.5 15t23.5 37q0 33 -56 50l-129 38q-125 36 -125 135q0 63 59.5 107.5t143.5 44.5q80 0 140 -35t84 -93l-117 -34q-33 59 -108 59q-30 0 -51 -14t-21 -32q0 -28 42 -40l126 -36q68 -20 105 -51
t37 -93q0 -72 -61.5 -118.5t-157.5 -46.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="412" 
d="M354 123l32 -107q-60 -24 -118 -24q-84 0 -126.5 48t-42.5 133v217h-85v116h85v164h133v-164h148v-116h-148v-217q0 -64 62 -64q32 0 60 14z" />
    <glyph glyph-name="u" unicode="u" 
d="M422 506h132v-506h-132v64q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="575" 
d="M427 506h144l-209 -506h-149l-209 506h145l139 -360z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="812" 
d="M671 506h137l-175 -506h-123l-104 319l-104 -319h-124l-174 506h137l105 -339l104 339h111l105 -339z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="546" 
d="M543 0h-151l-119 161l-119 -161h-150l189 256l-181 250h150l111 -153l112 153h149l-180 -250z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="585" 
d="M440 506h141l-304 -716h-136l92 221l-229 495h145l150 -342z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="530" 
d="M216 110h276v-110h-452v84l268 313h-262v109h437v-84z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="362" 
d="M256 425q0 -94 -74 -121q74 -26 74 -121v-138q0 -37 12.5 -53.5t45.5 -16.5h23v-116h-56q-153 0 -153 163v161q0 28 -18 45.5t-45 17.5h-20v117h20q27 0 45 17.5t18 44.5v162q0 162 153 162h56v-116h-23q-33 0 -45.5 -16.5t-12.5 -52.5v-139z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="237" 
d="M60 -125v930h117v-930h-117z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="362" 
d="M298 363h19v-117h-19q-28 0 -45.5 -17.5t-17.5 -45.5v-161q0 -163 -153 -163h-57v116h23q33 0 46 16.5t13 53.5v138q0 95 73 121q-73 27 -73 121v139q0 36 -13 52.5t-46 16.5h-23v116h57q153 0 153 -162v-162q0 -27 17.5 -44.5t45.5 -17.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="587" 
d="M168 263l-118 34q0 59 32 101t97 42q48 0 113.5 -32t82.5 -32q44 0 44 62l118 -34q0 -60 -31.5 -102t-95.5 -42q-48 0 -114 32t-83 32q-45 0 -45 -61z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="253" 
d="M126 514q35 0 58.5 -23.5t23.5 -57.5q0 -33 -24 -56t-58 -23t-57.5 23t-23.5 56q0 34 23.5 57.5t57.5 23.5zM180 279l18 -473h-144l18 473h108z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="576" 
d="M303 200q46 0 80 24t44 58l122 -35q-13 -54 -64 -100t-127 -59v-104h-113v105q-94 19 -152 90.5t-58 170.5t58 170.5t152 90.5v104h113v-104q76 -14 127 -60.5t64 -99.5l-122 -35q-10 33 -44 58t-80 25q-62 0 -100.5 -43.5t-38.5 -105.5q0 -61 39 -105.5t100 -44.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="641" 
d="M253 127h355v-127h-565v127h75v151h-61v110h61v111q0 103 62 159t165 56q90 0 151 -53.5t71 -128.5l-122 -35q-24 95 -103 95q-42 0 -65.5 -27t-23.5 -74v-103h234v-110h-234v-151z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="624" 
d="M545 320q0 -70 -34 -124l83 -83l-75 -75l-81 83q-56 -36 -126 -36q-69 0 -125 35l-82 -82l-75 75l83 82q-34 56 -34 125t34 125l-83 82l75 75l82 -82q55 35 125 35q71 0 126 -35l81 82l75 -75l-83 -82q34 -56 34 -125zM311 199q52 0 87.5 34.5t36.5 86.5q-1 52 -36.5 87
t-87.5 35q-51 0 -85.5 -35t-34.5 -87t34.5 -86.5t85.5 -34.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="691" 
d="M681 700l-220 -367h144v-74h-189l-1 -2v-56h190v-74h-190v-127h-139v127h-183v74h183v56l-1 2h-182v74h137l-220 367h155l181 -311l181 311h154z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="237" 
d="M60 445v360h117v-360h-117zM60 -125v360h117v-360h-117z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="560" 
d="M449 217q47 -39 47 -103q0 -72 -61 -119.5t-156 -47.5q-87 0 -152.5 44.5t-81.5 105.5l116 35q30 -82 125 -79q36 1 58.5 18t22.5 37q0 18 -13 28t-43 20l-131 41q-59 18 -94 53.5t-35 88.5q0 35 21 66t55 51q-48 39 -48 103q0 66 58 110.5t141 44.5q76 0 132.5 -34
t79.5 -91l-117 -34q-28 54 -96 55q-31 1 -51.5 -14t-20.5 -34q0 -28 43 -43l126 -40q67 -20 104 -52.5t37 -94.5q0 -75 -66 -115zM237 294l110 -31q40 24 40 61q0 19 -14 31t-43 22l-99 32q-22 -10 -37 -25.5t-15 -33.5q0 -41 58 -56z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="328" 
d="M67 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM261 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="743" 
d="M372 15q-143 0 -240.5 97t-97.5 240q0 141 97.5 237t240.5 96q144 0 240.5 -96t96.5 -237q0 -143 -96.5 -240t-240.5 -97zM372 83q114 0 191 77t77 192q0 114 -77 189.5t-191 75.5t-191.5 -75.5t-77.5 -189.5q0 -115 77.5 -192t191.5 -77zM376 177q-75 0 -124.5 49.5
t-49.5 123.5t49 123t123 49q64 0 107 -32.5t55 -76.5l-70 -17q-7 24 -32 41.5t-59 17.5q-45 0 -73 -30t-28 -75q0 -44 28 -74.5t73 -30.5q35 0 59.5 17t31.5 42l70 -19q-12 -43 -54.5 -75.5t-105.5 -32.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="534" 
d="M283 505l-100 -185l100 -185h-119l-104 185l104 185h119zM474 505l-100 -185l100 -185h-119l-104 185l104 185h119z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="583" 
d="M523 456v-250h-113v145h-350v105h463z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="743" 
d="M372 685q143 0 240 -96t97 -239q0 -142 -97 -238.5t-240 -96.5t-240.5 96.5t-97.5 238.5q0 143 97 239t241 96zM372 82q114 0 190.5 77t76.5 191q0 115 -76.5 191.5t-190.5 76.5t-191 -76.5t-77 -191.5q0 -114 77 -191t191 -77zM520 406q0 -32 -18.5 -60t-51.5 -41
l76 -120h-82l-68 112h-65v-112h-70v332h164q51 0 83 -34t32 -77zM311 452v-92h89q22 0 36.5 13.5t14.5 32.5t-14 32.5t-35 13.5h-91z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="288" 
d="M0 589v94h288v-94h-288z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="407" 
d="M204 414q-72 0 -121 48t-49 117q0 70 49 117.5t121 47.5q71 0 120 -47.5t49 -117.5q0 -69 -49 -117t-120 -48zM204 501q34 0 56.5 22.5t22.5 55.5t-22.5 55.5t-56.5 22.5q-35 0 -57.5 -22.5t-22.5 -55.5t22.5 -55.5t57.5 -22.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="558" 
d="M332 484h166v-105h-166v-143h-105v143h-167v105h167v144h105v-144zM60 72v105h438v-105h-438z" />
    <glyph glyph-name="two.sups" unicode="&#xb2;" horiz-adv-x="351" 
d="M172 537h157v-87h-310v63l157 148q44 42 44 68q0 19 -13 31t-36 12q-49 0 -62 -65l-91 26q6 52 48 90t103 38q69 0 111 -34t42 -95q0 -34 -15 -60t-49 -56z" />
    <glyph glyph-name="three.sups" unicode="&#xb3;" horiz-adv-x="359" 
d="M242 699q43 -11 68.5 -43t25.5 -78q0 -58 -43 -98t-109 -40q-75 0 -116 37t-50 91l87 26q6 -31 26 -50t51 -19q28 0 43.5 15.5t15.5 38.5q0 24 -16 39.5t-48 15.5q-11 0 -33 -2l-15 54l73 80h-170v84h290v-69z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="222" 
d="M0 585l77 140h145l-108 -140h-114z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="716" 
d="M663 700v-126h-76v-784h-121v784h-96v-784h-125v480q-92 0 -153.5 62t-61.5 152q0 91 64 153.5t166 62.5h403z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="253" 
d="M127 210q-35 0 -58.5 23.5t-23.5 57.5q0 32 24 55.5t58 23.5t57.5 -23.5t23.5 -55.5q0 -34 -23.5 -57.5t-57.5 -23.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="222" 
d="M106 -223q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l29 94h95l-17 -55q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24z" />
    <glyph glyph-name="one.sups" unicode="&#xb9;" horiz-adv-x="272" 
d="M134 850h86v-400h-100v288l-93 -43l-24 86z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="534" 
d="M179 505l104 -185l-104 -185h-119l100 185l-100 185h119zM370 505l104 -185l-104 -185h-119l100 185l-100 185h119z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="805" 
d="M120 300v288l-93 -43l-24 86l131 69h86v-400h-100zM533 700h92l-434 -700h-91zM793 159v-85h-47v-74h-94v74h-191v76l133 250h104l-126 -241h80v67h94v-67h47z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="804" 
d="M120 300v288l-93 -43l-24 86l131 69h86v-400h-100zM533 700h92l-434 -700h-91zM625 87h157v-87h-310v63l157 148q44 42 44 68q0 19 -13 31t-36 12q-49 0 -62 -65l-91 26q6 52 48 90t103 38q69 0 111 -34t42 -95q0 -34 -15 -60t-49 -56z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="892" 
d="M336 428q0 -58 -43 -98t-109 -40q-75 0 -116 37t-50 91l87 26q6 -31 26 -50t51 -19q28 0 43.5 15.5t15.5 38.5q0 24 -16 39.5t-48 15.5q-11 0 -33 -2l-15 54l73 80h-170v84h290v-69l-80 -82q43 -11 68.5 -43t25.5 -78zM620 700h92l-434 -700h-91zM880 159v-85h-47v-74
h-94v74h-191v76l133 250h104l-126 -241h80v67h94v-67h47z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="550" 
d="M280 514q35 0 58.5 -23.5t23.5 -57.5q0 -33 -24 -56t-58 -23t-57.5 23t-23.5 56q0 34 23.5 57.5t57.5 23.5zM344 279q0 -42 -18 -77.5t-43 -59.5t-50.5 -45.5t-43 -46.5t-17.5 -51q0 -38 27.5 -61.5t72.5 -23.5q49 0 82.5 32.5t45.5 87.5l119 -37q-13 -86 -78 -145
t-170 -59q-103 0 -169.5 56.5t-66.5 143.5q0 43 18.5 79t45 61t53 47t45.5 47t19 52h128z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="722" 
d="M429 779h-114l-108 140h146zM576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="722" 
d="M515 919l-108 -140h-114l77 140h145zM576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="722" 
d="M361 851l-52 -78h-120l109 144h127l109 -144h-120zM576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="722" 
d="M304 819q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h87q0 -143 -97 -143q-31 0 -73.5 25.5t-53.5 25.5zM576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="722" 
d="M264 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM458 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM576 0l-57 152h-315l-58 -152h-142
l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="722" 
d="M361 739q-53 0 -88.5 34.5t-35.5 85.5q0 52 35.5 86t88.5 34t88.5 -34t35.5 -86q0 -51 -35.5 -85.5t-88.5 -34.5zM361 906q-21 0 -34.5 -13t-13.5 -34q0 -20 13 -33t35 -13t35 13t13 33q0 21 -13.5 34t-34.5 13zM576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700
h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="992" 
d="M947 576h-336v-160h285v-120h-285v-172h335v-124h-467v154h-236l-90 -154h-149l414 700h529v-124zM315 276h164v279z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="775" 
d="M402 115q76 0 131 37.5t73 92.5l133 -34q-23 -82 -98 -145.5t-185 -76.5l-14 -44q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l25 82q-141 15 -232.5 117t-91.5 246q0 155 104.5 259
t260.5 104q133 0 222.5 -67.5t116.5 -159.5l-134 -33q-18 55 -73 93t-131 38q-101 0 -163.5 -68t-62.5 -165q0 -98 63 -167.5t163 -69.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="626" 
d="M388 779h-114l-108 140h146zM581 576h-382v-160h331v-120h-331v-172h381v-124h-515v700h516v-124z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="626" 
d="M474 919l-108 -140h-114l77 140h145zM581 576h-382v-160h331v-120h-331v-172h381v-124h-515v700h516v-124z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="626" 
d="M320 851l-52 -78h-120l109 144h127l109 -144h-120zM581 576h-382v-160h331v-120h-331v-172h381v-124h-515v700h516v-124z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="626" 
d="M223 760q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM417 760q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM581 576h-382v-160h331v-120h-331v-172h381
v-124h-515v700h516v-124z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="269" 
d="M202 779h-114l-108 140h146zM65 0v700h139v-700h-139z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="269" 
d="M67 779l77 140h145l-108 -140h-114zM65 0v700h139v-700h-139z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="269" 
d="M134 851l-52 -78h-120l109 144h127l109 -144h-120zM65 0v700h139v-700h-139z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="269" 
d="M37 760q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM231 760q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM65 0v700h139v-700h-139z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="771" 
d="M399 700q152 0 244 -97t92 -253q0 -154 -92 -252t-244 -98h-304v288h-70v117h70v295h304zM396 124q90 0 145 63.5t55 165.5q0 100 -55 162t-145 62h-165v-172h170v-117h-170v-164h165z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="732" 
d="M309 819q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h87q0 -143 -97 -143q-31 0 -73.5 25.5t-53.5 25.5zM530 700h137v-700h-113l-352 455v-455h-137v700h113l352 -454v454z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="807" 
d="M472 779h-114l-108 140h146zM404 -14q-158 0 -263 104.5t-105 260.5q0 155 105 259t263 104t262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5zM404 113q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68t-165.5 -68t-63.5 -168q0 -102 63.5 -170t165.5 -68
z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="807" 
d="M336 779l77 140h145l-108 -140h-114zM404 -14q-158 0 -263 104.5t-105 260.5q0 155 105 259t263 104t262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5zM404 113q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68t-165.5 -68t-63.5 -168q0 -102 63.5 -170
t165.5 -68z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="807" 
d="M404 851l-52 -78h-120l109 144h127l109 -144h-120zM404 714q158 0 262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5t-263 104.5t-105 260.5q0 155 105 259t263 104zM404 113q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68t-165.5 -68t-63.5 -168
q0 -102 63.5 -170t165.5 -68z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="807" 
d="M474 768q-31 0 -73.5 25.5t-53.5 25.5q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h87q0 -143 -97 -143zM404 -14q-158 0 -263 104.5t-105 260.5q0 155 105 259t263 104t262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5zM404 113
q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68t-165.5 -68t-63.5 -168q0 -102 63.5 -170t165.5 -68z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="807" 
d="M306 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM500 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM404 -14q-158 0 -263 104.5
t-105 260.5q0 155 105 259t263 104t262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5zM404 113q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68t-165.5 -68t-63.5 -168q0 -102 63.5 -170t165.5 -68z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="522" 
d="M462 478l-127 -127l126 -126l-74 -75l-126 126l-126 -126l-74 75l126 126l-127 126l75 74l126 -126l126 126z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="807" 
d="M647 628q59 -50 91.5 -122t32.5 -155q0 -156 -104.5 -260.5t-262.5 -104.5q-97 0 -178 42l-30 -42h-92l59 84q-60 50 -93.5 123t-33.5 158q0 155 105 259t263 104q98 0 180 -44l31 44h92zM175 351q0 -105 65 -172l269 384q-45 24 -105 24q-102 0 -165.5 -68t-63.5 -168z
M404 113q102 0 165.5 68t63.5 170q0 100 -63 168l-269 -384q47 -22 103 -22z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="724" 
d="M430 779h-114l-108 140h146zM527 700h138v-413q0 -136 -80 -218.5t-223 -82.5q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="724" 
d="M516 919l-108 -140h-114l77 140h145zM527 700h138v-413q0 -136 -80 -218.5t-223 -82.5q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="724" 
d="M362 851l-52 -78h-120l109 144h127l109 -144h-120zM527 700h138v-413q0 -136 -80 -218.5t-223 -82.5q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="724" 
d="M265 760q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM459 760q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM527 700h138v-413q0 -136 -80 -218.5
t-223 -82.5q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="678" 
d="M494 919l-108 -140h-114l77 140h145zM520 700h154l-265 -443v-257h-139v257l-266 443h155l181 -311z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="656" 
d="M393 603q109 0 176 -74.5t67 -169.5t-67.5 -169t-181.5 -74h-186v-116h-136v700h136v-97h192zM383 239q53 0 86.5 35.5t33.5 84.5q0 48 -32.5 84t-81.5 36h-188v-240h182z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="609" 
d="M457 395q116 -55 116 -180q0 -102 -66.5 -158.5t-183.5 -56.5h-56v117h52q57 0 89.5 27t32.5 76q0 50 -32 77t-91 27h-40v106h17q49 0 78.5 27.5t29.5 74.5q0 42 -26.5 67.5t-71.5 25.5q-53 0 -83 -37t-30 -100v-488h-132v486q0 115 66 185.5t176 70.5q101 0 167 -57
t66 -146q0 -95 -78 -144z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="645" 
d="M380 585h-114l-108 140h146zM284 -14q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="645" 
d="M244 585l77 140h145l-108 -140h-114zM284 -14q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108
t102 -42z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="645" 
d="M312 657l-52 -78h-120l109 144h127l109 -144h-120zM447 506h133v-506h-133v62q-59 -76 -163 -76q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5
q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="645" 
d="M382 574q-31 0 -73.5 25.5t-53.5 25.5q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h87q0 -143 -97 -143zM284 -14q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5
t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="645" 
d="M215 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM409 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM284 -14q-106 0 -178 76.5t-72 190.5
q0 115 72 191t178 76q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="645" 
d="M312 545q-53 0 -88.5 34.5t-35.5 85.5q0 52 35.5 86t88.5 34t88.5 -34t35.5 -86q0 -51 -35.5 -85.5t-88.5 -34.5zM312 619q22 0 35 13t13 33q0 21 -13 34t-35 13t-35 -13t-13 -34q0 -20 13 -33t35 -13zM284 -14q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76
q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1012" 
d="M978 259q0 -19 -4 -53h-392q9 -51 47.5 -80.5t95.5 -29.5q78 0 110 55l119 -36q-29 -58 -86 -93.5t-133 -35.5q-110 0 -170 75v-61h-118v62q-59 -76 -163 -76q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h118v-64q61 78 166 78q110 0 178.5 -73
t68.5 -188zM579 294l271 1q-5 50 -39.5 82t-91.5 32t-95 -33.5t-45 -81.5zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="580" 
d="M302 104q47 0 81 24t43 57l123 -34q-14 -55 -65.5 -101t-127.5 -59l-14 -46q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l26 83q-100 14 -163.5 88t-63.5 177q0 114 76.5 190t191.5 76
q97 0 163.5 -51t83.5 -115l-123 -35q-9 34 -43 59t-81 25q-61 0 -99.5 -43.5t-38.5 -105.5q0 -61 38.5 -105.5t99.5 -44.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="595" 
d="M368 585h-114l-108 140h146zM309 -14q-122 0 -198.5 73.5t-76.5 193.5q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5t-155.5 -43.5zM164 296h267q-5 48 -40.5 80t-92.5 32
q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="595" 
d="M232 585l77 140h145l-108 -140h-114zM309 -14q-122 0 -198.5 73.5t-76.5 193.5q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5t-155.5 -43.5zM164 296h267q-5 48 -40.5 80
t-92.5 32q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="595" 
d="M300 657l-52 -78h-120l109 144h127l109 -144h-120zM560 260q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5t-155.5 -43.5q-122 0 -198.5 73.5t-76.5 193.5q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186zM164 296h267
q-5 48 -40.5 80t-92.5 32q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="595" 
d="M203 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM397 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM309 -14q-122 0 -198.5 73.5t-76.5 193.5
q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5t-155.5 -43.5zM164 296h267q-5 48 -40.5 80t-92.5 32q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="263" 
d="M199 585h-114l-108 140h146zM65 0v506h133v-506h-133z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="263" 
d="M63 585l77 140h145l-108 -140h-114zM65 0v506h133v-506h-133z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="263" 
d="M131 657l-52 -78h-120l109 144h127l109 -144h-120zM65 0v506h133v-506h-133z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="263" 
d="M43 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM219 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM65 0v506h133v-506h-133z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="606" 
d="M428 582q143 -156 143 -330q0 -118 -75.5 -192t-191.5 -74q-115 0 -192 72t-77 179q0 106 74 173t185 67q50 0 85 -18q-34 51 -73 92l-179 -45l-18 66l139 35q-50 45 -120 93h171q32 -25 73 -62l136 34l18 -66zM304 107q60 0 96.5 36.5t36.5 92.5q0 54 -36.5 90.5
t-96.5 36.5t-96.5 -36t-36.5 -90q0 -57 36.5 -93.5t96.5 -36.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M258 625q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h87q0 -143 -97 -143q-31 0 -73.5 25.5t-53.5 25.5zM360 520q97 0 148 -60t51 -164v-296h-132v271q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v506h132v-64q61 78 163 78z
" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="610" 
d="M373 585h-114l-108 140h146zM305 -14q-117 0 -194 76.5t-77 190.5t77 190.5t194 76.5t193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5zM305 106q63 0 102.5 41.5t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5t-39.5 -105.5t39.5 -105.5t102.5 -41.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="610" 
d="M237 585l77 140h145l-108 -140h-114zM305 -14q-117 0 -194 76.5t-77 190.5t77 190.5t194 76.5t193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5zM305 106q63 0 102.5 41.5t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5t-39.5 -105.5t39.5 -105.5t102.5 -41.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="610" 
d="M305 657l-52 -78h-120l109 144h127l109 -144h-120zM305 520q117 0 193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5q-117 0 -194 76.5t-77 190.5t77 190.5t194 76.5zM305 106q63 0 102.5 41.5t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5t-39.5 -105.5t39.5 -105.5
t102.5 -41.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="610" 
d="M375 574q-31 0 -73.5 25.5t-53.5 25.5q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h87q0 -143 -97 -143zM305 -14q-117 0 -194 76.5t-77 190.5t77 190.5t194 76.5t193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5zM305 106q63 0 102.5 41.5
t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5t-39.5 -105.5t39.5 -105.5t102.5 -41.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="610" 
d="M208 566q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM402 566q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM305 -14q-117 0 -194 76.5t-77 190.5
t77 190.5t194 76.5t193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5zM305 106q63 0 102.5 41.5t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5t-39.5 -105.5t39.5 -105.5t102.5 -41.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="580" 
d="M290 466q-28 0 -47.5 19.5t-19.5 47.5q0 26 19.5 45t47.5 19t47.5 -19t19.5 -45q0 -28 -19.5 -47.5t-47.5 -19.5zM60 298v105h460v-105h-460zM290 104q-28 0 -47.5 19.5t-19.5 47.5q0 26 19.5 45t47.5 19t47.5 -19t19.5 -45q0 -28 -19.5 -47.5t-47.5 -19.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="610" 
d="M487 455q88 -78 88 -202q0 -114 -77 -190.5t-193 -76.5q-70 0 -126 29l-20 -29h-81l46 65q-90 79 -90 202q0 114 77 190.5t194 76.5q71 0 127 -29l21 30h80zM163 253q0 -59 33 -98l165 235q-28 10 -56 10q-63 0 -102.5 -41.5t-39.5 -105.5zM305 106q63 0 102.5 41.5
t39.5 105.5q0 57 -33 98l-164 -235q27 -10 55 -10z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M377 585h-114l-108 140h146zM422 506h132v-506h-132v64q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M463 725l-108 -140h-114l77 140h145zM422 506h132v-506h-132v64q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M309 657l-52 -78h-120l109 144h127l109 -144h-120zM422 506h132v-506h-132v64q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M212 566q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM406 566q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM422 506h132v-506h-132v64
q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="585" 
d="M451 725l-108 -140h-114l77 140h145zM440 506h141l-304 -716h-136l92 221l-229 495h145l150 -342z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="645" 
d="M361 520q106 0 177.5 -76t71.5 -191q0 -114 -72 -190.5t-177 -76.5q-103 0 -164 76v-272h-132v940h132v-285q60 75 164 75zM338 104q62 0 102 42t40 108q0 65 -40 106.5t-102 41.5q-61 0 -102.5 -42t-41.5 -106q0 -65 41.5 -107.5t102.5 -42.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="585" 
d="M199 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM393 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM440 506h141l-304 -716h-136l92 221l-229 495
h145l150 -342z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="722" 
d="M505 877v-94h-288v94h288zM576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="645" 
d="M168 589v94h288v-94h-288zM284 -14q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="722" 
d="M361 760q-81 0 -123.5 42t-42.5 106h102q2 -66 64 -66q33 0 48.5 18.5t15.5 47.5h102q0 -64 -42.5 -106t-123.5 -42zM576 0l-57 152h-315l-58 -152h-142l268 700h178l269 -700h-143zM249 273h224l-112 296z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="645" 
d="M312 566q-81 0 -123.5 42t-42.5 106h102q2 -66 64 -66q33 0 48.5 18.5t15.5 47.5h102q0 -64 -42.5 -106t-123.5 -42zM284 -14q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h133v-506h-133v62q-59 -76 -163 -76zM307 104q61 0 102.5 42.5t41.5 107.5
q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="722" 
d="M737 -106l23 -73q-37 -45 -106 -45q-47 0 -77.5 27t-30.5 71q0 65 76 126h-46l-57 152h-315l-58 -152h-142l268 700h178l269 -700q-32 -26 -53 -55t-21 -48q0 -33 33 -33q36 0 59 30zM249 273h224l-112 296z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="645" 
d="M598 -106l23 -73q-37 -45 -106 -45q-47 0 -77.5 27t-30.5 71q0 65 76 126h-36v62q-59 -76 -163 -76q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v61h133v-506q-32 -26 -53 -55t-21 -48q0 -33 33 -33q36 0 59 30zM307 104q61 0 102.5 42.5t41.5 107.5
q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="775" 
d="M334 779l77 140h145l-108 -140h-114zM404 -14q-158 0 -263 104.5t-105 260.5q0 155 104.5 259t260.5 104q133 0 222.5 -67.5t116.5 -159.5l-134 -33q-18 55 -73 93t-131 38q-101 0 -163.5 -68t-62.5 -165q0 -98 63 -167.5t163 -69.5q76 0 131 37.5t73 92.5l133 -34
q-26 -92 -115.5 -158.5t-219.5 -66.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="580" 
d="M234 585l77 140h145l-108 -140h-114zM303 -14q-116 0 -192.5 77t-76.5 191t76.5 190t191.5 76q97 0 163.5 -51t83.5 -115l-123 -35q-9 34 -43 59t-81 25q-61 0 -99.5 -43.5t-38.5 -105.5q0 -61 38.5 -105.5t99.5 -44.5q47 0 81 24t43 57l123 -34q-17 -65 -83 -115
t-163 -50z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="775" 
d="M402 764q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM404 -14q-158 0 -263 104.5t-105 260.5q0 155 104.5 259t260.5 104q133 0 222.5 -67.5t116.5 -159.5l-134 -33q-18 55 -73 93t-131 38q-101 0 -163.5 -68t-62.5 -165q0 -98 63 -167.5
t163 -69.5q76 0 131 37.5t73 92.5l133 -34q-26 -92 -115.5 -158.5t-219.5 -66.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="580" 
d="M302 570q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM303 -14q-116 0 -192.5 77t-76.5 191t76.5 190t191.5 76q97 0 163.5 -51t83.5 -115l-123 -35q-9 34 -43 59t-81 25q-61 0 -99.5 -43.5t-38.5 -105.5q0 -61 38.5 -105.5t99.5 -44.5
q47 0 81 24t43 57l123 -34q-17 -65 -83 -115t-163 -50z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="775" 
d="M339 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM402 115q76 0 131 37.5t73 92.5l133 -34q-26 -92 -115.5 -158.5t-219.5 -66.5q-158 0 -263 104.5t-105 260.5q0 155 104.5 259t260.5 104q133 0 222.5 -67.5t116.5 -159.5l-134 -33q-18 55 -73 93t-131 38
q-101 0 -163.5 -68t-62.5 -165q0 -98 63 -167.5t163 -69.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="580" 
d="M238 579l-109 144h120l52 -78l53 78h120l-109 -144h-127zM302 104q47 0 81 24t43 57l123 -34q-17 -65 -83 -115t-163 -50q-116 0 -192.5 77t-76.5 191t76.5 190t191.5 76q97 0 163.5 -51t83.5 -115l-123 -35q-9 34 -43 59t-81 25q-61 0 -99.5 -43.5t-38.5 -105.5
q0 -61 38.5 -105.5t99.5 -44.5z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="740" 
d="M284 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM369 700q152 0 243.5 -97t91.5 -253q0 -154 -91.5 -252t-243.5 -98h-304v700h304zM366 124q90 0 145 63.5t55 165.5q0 100 -55 162t-145 62h-165v-453h165z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="762" 
d="M447 730h133v-730h-133v62q-59 -76 -163 -76q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v285zM650 730h120l-31 -202h-102zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="771" 
d="M399 700q152 0 244 -97t92 -253q0 -154 -92 -252t-244 -98h-304v288h-70v117h70v295h304zM396 124q90 0 145 63.5t55 165.5q0 100 -55 162t-145 62h-165v-172h170v-117h-170v-164h165z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="650" 
d="M647 652v-78h-67v-574h-133v62q-59 -76 -163 -76q-106 0 -178 76.5t-72 190.5q0 115 72 191t178 76q104 0 163 -75v129h-160v78h160v78h133v-78h67zM307 104q61 0 102.5 42.5t41.5 107.5q0 64 -41.5 106t-102.5 42q-62 0 -102 -41.5t-40 -106.5q0 -66 40 -108t102 -42z
" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="626" 
d="M464 877v-94h-288v94h288zM581 576h-382v-160h331v-120h-331v-172h381v-124h-515v700h516v-124z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="595" 
d="M157 589v94h288v-94h-288zM309 -14q-122 0 -198.5 73.5t-76.5 193.5q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5t-155.5 -43.5zM164 296h267q-5 48 -40.5 80t-92.5 32
q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="626" 
d="M320 764q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM581 576h-382v-160h331v-120h-331v-172h381v-124h-515v700h516v-124z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="595" 
d="M300 570q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM309 -14q-122 0 -198.5 73.5t-76.5 193.5q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5
t-155.5 -43.5zM164 296h267q-5 48 -40.5 80t-92.5 32q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="626" 
d="M598 -106l23 -73q-37 -45 -106 -45q-47 0 -77.5 27t-30.5 71q0 65 76 126h-418v700h516v-124h-382v-160h331v-120h-331v-172h381v-124q-32 -26 -53 -55t-21 -48q0 -33 33 -33q36 0 59 30z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="595" 
d="M560 260q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-26 -60 -88 -100q-43 -35 -68.5 -70.5t-25.5 -61.5q0 -34 33 -34q36 0 59 30l23 -73q-37 -45 -106 -45q-47 0 -77.5 27t-30.5 71q0 59 59 113q-7 -1 -22 -1q-122 0 -198.5 73.5t-76.5 193.5
q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186zM164 296h267q-5 48 -40.5 80t-92.5 32q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="626" 
d="M257 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM581 576h-382v-160h331v-120h-331v-172h381v-124h-515v700h516v-124z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="595" 
d="M237 579l-109 144h120l52 -78l53 78h120l-109 -144h-127zM560 260q0 -31 -3 -47h-393q7 -52 44 -83.5t103 -31.5q88 0 123 67l119 -35q-25 -57 -88.5 -100.5t-155.5 -43.5q-122 0 -198.5 73.5t-76.5 193.5q0 115 73.5 191t191.5 76q121 0 191 -74t70 -186zM164 296h267
q-5 48 -40.5 80t-92.5 32q-55 0 -91.5 -33t-42.5 -79z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="799" 
d="M403 760q-81 0 -123.5 42t-42.5 106h102q2 -66 64 -66q33 0 48.5 18.5t15.5 47.5h102q0 -64 -42.5 -106t-123.5 -42zM763 389v-70q0 -148 -98 -240.5t-256 -92.5q-161 0 -267 104.5t-106 260.5q0 154 105.5 258.5t261.5 104.5q124 0 212.5 -60.5t123.5 -153.5l-127 -36
q-59 121 -208 121q-100 0 -164.5 -68t-64.5 -165q0 -100 65.5 -169t178.5 -69q85 0 139 44.5t63 112.5h-215v118h357z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="641" 
d="M313 566q-81 0 -123.5 42t-42.5 106h102q2 -66 64 -66q33 0 48.5 18.5t15.5 47.5h102q0 -64 -42.5 -106t-123.5 -42zM444 506h132v-488q0 -107 -72 -174.5t-189 -67.5q-171 0 -244 133l115 41q41 -65 132 -65q60 0 92.5 37t33.5 96v61q-59 -73 -160 -73q-108 0 -179 74
t-71 183q0 113 70.5 185t179.5 72q101 0 160 -73v59zM307 125q61 0 100.5 38.5t39.5 99.5t-39.5 99.5t-100.5 38.5q-63 0 -102.5 -38.5t-39.5 -99.5t39.5 -99.5t102.5 -38.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="799" 
d="M403 764q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM763 389v-70q0 -148 -98 -240.5t-256 -92.5q-161 0 -267 104.5t-106 260.5q0 154 105.5 258.5t261.5 104.5q124 0 212.5 -60.5t123.5 -153.5l-127 -36q-59 121 -208 121
q-100 0 -164.5 -68t-64.5 -165q0 -100 65.5 -169t178.5 -69q85 0 139 44.5t63 112.5h-215v118h357z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="641" 
d="M313 570q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM444 506h132v-488q0 -107 -72 -174.5t-189 -67.5q-171 0 -244 133l115 41q41 -65 132 -65q60 0 92.5 37t33.5 96v61q-59 -73 -160 -73q-108 0 -179 74t-71 183q0 113 70.5 185t179.5 72
q101 0 160 -73v59zM307 125q61 0 100.5 38.5t39.5 99.5t-39.5 99.5t-100.5 38.5q-63 0 -102.5 -38.5t-39.5 -99.5t39.5 -99.5t102.5 -38.5z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="799" 
d="M763 389v-70q0 -148 -98 -240.5t-256 -92.5q-161 0 -267 104.5t-106 260.5q0 154 105.5 258.5t261.5 104.5q124 0 212.5 -60.5t123.5 -153.5l-127 -36q-59 121 -208 121q-100 0 -164.5 -68t-64.5 -165q0 -100 65.5 -169t178.5 -69q85 0 139 44.5t63 112.5h-215v118h357z
M337 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="641" 
d="M384 789l-13 -199h-120l31 199h102zM444 506h132v-488q0 -107 -72 -174.5t-189 -67.5q-171 0 -244 133l115 41q41 -65 132 -65q60 0 92.5 37t33.5 96v61q-59 -73 -160 -73q-108 0 -179 74t-71 183q0 113 70.5 185t179.5 72q101 0 160 -73v59zM307 125q61 0 100.5 38.5
t39.5 99.5t-39.5 99.5t-100.5 38.5q-63 0 -102.5 -38.5t-39.5 -99.5t39.5 -99.5t102.5 -38.5z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="755" 
d="M743 602v-86h-67v-516h-138v294h-320v-294h-137v516h-68v86h68v98h137v-98h320v98h138v-98h67zM538 418v98h-320v-98h320z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="624" 
d="M365 520q97 0 148 -60t51 -164v-296h-132v271q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v574h-66v78h66v78h132v-78h162v-78h-162v-132q61 78 163 78z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="269" 
d="M-9 783v94h288v-94h-288zM65 0v700h139v-700h-139z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="263" 
d="M-13 589v94h288v-94h-288zM65 0v506h133v-506h-133z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="269" 
d="M226 -106l23 -73q-15 -19 -43.5 -32t-62.5 -13q-47 0 -77.5 27t-30.5 71q0 63 71 126h-41v700h139v-700q-69 -59 -69 -103q0 -33 32 -33q36 0 59 30z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="263" 
d="M131 570q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM220 -106l23 -73q-15 -19 -43.5 -32t-62.5 -13q-47 0 -77.5 27t-30.5 71q0 63 71 126h-35v506h133v-506q-69 -59 -69 -103q0 -33 32 -33q36 0 59 30z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="269" 
d="M135 764q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM65 0v700h139v-700h-139z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="263" 
d="M65 0v506h133v-506h-133z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="703" 
d="M395 394l298 -394h-167l-221 293l-103 -114v-179h-137v700h137v-339l295 339h174zM275 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="558" 
d="M339 288l214 -288h-150l-147 195l-59 -66v-129h-132v730h132v-433l182 209h154zM226 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="574" 
d="M300 919l-108 -140h-114l77 140h145zM202 126h349v-126h-486v700h137v-574z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="262" 
d="M63 809l77 140h145l-108 -140h-114zM65 0v730h132v-730h-132z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="574" 
d="M202 126h349v-126h-486v700h137v-574zM235 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="262" 
d="M65 0v730h132v-730h-132zM58 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="576" 
d="M202 126h349v-126h-486v700h137v-574zM549 700l-31 -179h-104l11 179h124z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="384" 
d="M65 0v730h132v-730h-132zM256 528l12 202h121l-31 -202h-102z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="584" 
d="M213 126h349v-126h-486v248l-56 -24v117l56 24v335h137v-276l195 84v-117l-195 -84v-181z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="316" 
d="M306 525v-116l-82 -39v-366h-132v305l-82 -39v117l82 38v309h132v-247z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="732" 
d="M520 919l-108 -140h-114l77 140h145zM530 700h137v-700h-113l-352 455v-455h-137v700h113l352 -454v454z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M469 725l-108 -140h-114l77 140h145zM360 520q97 0 148 -60t51 -164v-296h-132v271q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v506h132v-64q61 78 163 78z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="732" 
d="M530 700h137v-700h-113l-352 455v-455h-137v700h113l352 -454v454zM299 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" 
d="M360 520q97 0 148 -60t51 -164v-296h-132v271q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v506h132v-64q61 78 163 78zM244 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="732" 
d="M303 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM530 700h137v-700h-113l-352 455v-455h-137v700h113l352 -454v454z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M252 579l-109 144h120l52 -78l53 78h120l-109 -144h-127zM360 520q97 0 148 -60t51 -164v-296h-132v271q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v506h132v-64q61 78 163 78z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="732" 
d="M530 700h137v-713q0 -94 -43.5 -146.5t-131.5 -52.5q-47 0 -95 22l30 102q20 -12 49 -12q54 0 54 77v54l-328 424v-455h-137v700h113l352 -454v454z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M360 520q97 0 148 -60t51 -164v-312q0 -99 -43.5 -153.5t-131.5 -54.5q-50 0 -94 22l29 102q22 -12 45 -12q63 0 63 85v298q0 59 -27.5 92.5t-80.5 33.5q-55 0 -88.5 -40t-33.5 -107v-250h-132v506h132v-64q61 78 163 78z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="807" 
d="M260 783v94h288v-94h-288zM404 -14q-158 0 -263 104.5t-105 260.5q0 155 105 259t263 104t262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5zM404 113q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68t-165.5 -68t-63.5 -168q0 -102 63.5 -170t165.5 -68z
" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="610" 
d="M161 589v94h288v-94h-288zM305 -14q-117 0 -194 76.5t-77 190.5t77 190.5t194 76.5t193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5zM305 106q63 0 102.5 41.5t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5t-39.5 -105.5t39.5 -105.5t102.5 -41.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="807" 
d="M249 779l80 140h122l-86 -140h-116zM427 779l88 140h122l-94 -140h-116zM404 -14q-158 0 -263 104.5t-105 260.5q0 155 105 259t263 104t262.5 -104t104.5 -259q0 -156 -104.5 -260.5t-262.5 -104.5zM404 113q102 0 165.5 68t63.5 170q0 100 -63.5 168t-165.5 68
t-165.5 -68t-63.5 -168q0 -102 63.5 -170t165.5 -68z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="610" 
d="M150 585l80 140h122l-86 -140h-116zM328 585l88 140h122l-94 -140h-116zM305 -14q-117 0 -194 76.5t-77 190.5t77 190.5t194 76.5t193.5 -76.5t76.5 -190.5t-77 -190.5t-193 -76.5zM305 106q63 0 102.5 41.5t39.5 105.5t-39.5 105.5t-102.5 41.5t-102.5 -41.5
t-39.5 -105.5t39.5 -105.5t102.5 -41.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1006" 
d="M961 576h-336v-160h285v-120h-285v-172h335v-124h-583q-153 0 -247 98.5t-94 251.5q0 155 93.5 252.5t247.5 97.5h584v-124zM377 124h112v452h-112q-90 0 -146 -62t-56 -161q0 -102 56 -165.5t146 -63.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1007" 
d="M973 259q0 -19 -4 -51h-391q10 -50 48 -81t95 -31q76 0 111 55l119 -36q-25 -53 -86.5 -91t-142.5 -38q-140 0 -212 101q-76 -100 -205 -100q-117 0 -194 76.5t-77 190.5t77 190t194 76q131 0 205 -100q71 100 203 100q121 0 190.5 -74t69.5 -187zM576 295h269
q-5 50 -39.5 82t-91.5 32t-94.5 -33.5t-43.5 -80.5zM305 106q62 0 102 42.5t40 104.5t-40 105t-102 43q-63 0 -102.5 -42.5t-39.5 -105.5q0 -62 39.5 -104.5t102.5 -42.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="695" 
d="M483 919l-108 -140h-114l77 140h145zM509 0l-159 245h-149v-245h-136v700h341q108 0 174 -69.5t66 -158.5q0 -71 -40.5 -129.5t-112.5 -83.5l174 -259h-158zM201 577v-212h196q52 0 84 31t32 76q0 43 -31.5 74t-79.5 31h-201z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="385" 
d="M360 725l-108 -140h-114l77 140h145zM197 431q50 98 167 82v-124q-83 13 -125 -28t-42 -141v-220h-132v506h132v-75z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="695" 
d="M493 259l174 -259h-158l-159 245h-149v-245h-136v700h341q108 0 174 -69.5t66 -158.5q0 -71 -40.5 -129.5t-112.5 -83.5zM201 577v-212h196q52 0 84 31t32 76q0 43 -31.5 74t-79.5 31h-201zM274 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="385" 
d="M197 431q50 98 167 82v-124q-83 13 -125 -28t-42 -141v-220h-132v506h132v-75zM59 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="695" 
d="M265 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM509 0l-159 245h-149v-245h-136v700h341q108 0 174 -69.5t66 -158.5q0 -71 -40.5 -129.5t-112.5 -83.5l174 -259h-158zM201 577v-212h196q52 0 84 31t32 76q0 43 -31.5 74t-79.5 31h-201z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="385" 
d="M142 579l-109 144h120l52 -78l53 78h120l-109 -144h-127zM197 431q50 98 167 82v-124q-83 13 -125 -28t-42 -141v-220h-132v506h132v-75z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="668" 
d="M261 779l77 140h145l-108 -140h-114zM351 -14q-121 0 -206 61.5t-117 150.5l131 40q22 -58 74.5 -94.5t125.5 -36.5q59 0 96 26t37 62q0 28 -21 48t-71 36l-179 56q-73 23 -116 68t-42 110q-1 83 71 142t180 59q109 0 190 -51.5t111 -128.5l-127 -37q-22 43 -68.5 69.5
t-104.5 26.5q-50 0 -81.5 -22t-31.5 -54q0 -46 67 -66l174 -56q92 -28 140 -70.5t48 -121.5q0 -92 -78 -154.5t-202 -62.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="519" 
d="M182 585l77 140h145l-108 -140h-114zM264 -14q-90 0 -158 46t-88 110l119 35q13 -37 48 -60.5t84 -23.5q37 0 60.5 15t23.5 37q0 33 -56 50l-129 38q-125 36 -125 135q0 63 59.5 107.5t143.5 44.5q80 0 140 -35t84 -93l-117 -34q-33 59 -108 59q-30 0 -51 -14t-21 -32
q0 -28 42 -40l126 -36q68 -20 105 -51t37 -93q0 -72 -61.5 -118.5t-157.5 -46.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="668" 
d="M631 203q0 -83 -65 -143.5t-172 -71.5l-13 -43q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l26 83q-102 13 -173 71.5t-99 137.5l131 40q22 -58 74.5 -94.5t125.5 -36.5q59 0 96 26
t37 62q0 28 -21 48t-71 36l-179 56q-73 23 -116 68t-42 110q-1 83 71 142t180 59q109 0 190 -51.5t111 -128.5l-127 -37q-22 43 -68.5 69.5t-104.5 26.5q-50 0 -81.5 -22t-31.5 -54q0 -46 67 -66l174 -56q92 -28 140 -70.5t48 -121.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="519" 
d="M483 151q0 -63 -48 -107t-127 -55l-14 -44q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l26 84q-73 12 -126 55t-69 97l119 35q13 -37 48 -60.5t84 -23.5q37 0 60.5 15t23.5 37
q0 33 -56 50l-129 38q-125 36 -125 135q0 63 59.5 107.5t143.5 44.5q80 0 140 -35t84 -93l-117 -34q-33 59 -108 59q-30 0 -51 -14t-21 -32q0 -28 42 -40l126 -36q68 -20 105 -51t37 -93z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="668" 
d="M266 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM443 395q92 -28 140 -70.5t48 -121.5q0 -92 -78 -154.5t-202 -62.5q-121 0 -206 61.5t-117 150.5l131 40q22 -58 74.5 -94.5t125.5 -36.5q59 0 96 26t37 62q0 28 -21 48t-71 36l-179 56q-73 23 -116 68t-42 110
q-1 83 71 142t180 59q109 0 190 -51.5t111 -128.5l-127 -37q-22 43 -68.5 69.5t-104.5 26.5q-50 0 -81.5 -22t-31.5 -54q0 -46 67 -66z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="519" 
d="M187 579l-109 144h120l52 -78l53 78h120l-109 -144h-127zM341 295q68 -20 105 -51t37 -93q0 -72 -61.5 -118.5t-157.5 -46.5q-90 0 -158 46t-88 110l119 35q13 -37 48 -60.5t84 -23.5q37 0 60.5 15t23.5 37q0 33 -56 50l-129 38q-125 36 -125 135q0 63 59.5 107.5
t143.5 44.5q80 0 140 -35t84 -93l-117 -34q-33 59 -108 59q-30 0 -51 -14t-21 -32q0 -28 42 -40z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="637" 
d="M387 576v-576l-17 -55q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l29 94h-42v576h-235v124h607v-124h-235z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="412" 
d="M291 -55q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l29 93q-114 28 -114 174v217h-85v116h85v164h133v-164h148v-116h-148v-217q0 -64 62 -64q32 0 60 14l32 -107q-39 -16 -80 -22z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="637" 
d="M255 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM622 700v-124h-235v-576h-137v576h-235v124h607z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="434" 
d="M297 726h120l-30 -175h-100zM294 109q32 0 60 14l32 -107q-60 -24 -118 -24q-84 0 -126.5 48t-42.5 133v217h-85v116h85v164h133v-164h148v-116h-148v-217q0 -64 62 -64z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="637" 
d="M622 576h-235v-186h146v-108h-146v-282h-137v282h-152v108h152v186h-235v124h607v-124z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="415" 
d="M356 123l32 -107q-60 -24 -120 -24q-84 0 -126.5 48t-42.5 133v73h-78v86h78v80h-85v94h85v164h133v-164h150v-94h-150v-80h129v-86h-129v-73q0 -64 66 -64q31 0 58 14z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="724" 
d="M506 877v-94h-288v94h288zM527 700h138v-413q0 -136 -80 -218.5t-223 -82.5q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M454 683v-94h-288v94h288zM422 506h132v-506h-132v64q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="724" 
d="M362 739q-53 0 -88.5 34.5t-35.5 85.5q0 52 35.5 86t88.5 34t88.5 -34t35.5 -86q0 -51 -35.5 -85.5t-88.5 -34.5zM362 906q-22 0 -35 -13t-13 -34q0 -20 13 -33t35 -13t35 13t13 33q0 21 -13 34t-35 13zM527 700h138v-413q0 -136 -80 -218.5t-223 -82.5q-144 0 -223.5 82
t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M310 545q-53 0 -88.5 34.5t-35.5 85.5q0 52 35.5 86t88.5 34t88.5 -34t35.5 -86q0 -51 -35.5 -85.5t-88.5 -34.5zM310 712q-22 0 -35 -13t-13 -34q0 -20 13 -33t35 -13t35 13t13 33q0 21 -13 34t-35 13zM422 506h132v-506h-132v64q-59 -78 -162 -78q-98 0 -149 60
t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="724" 
d="M409 919l-86 -140h-116l80 140h122zM501 779h-116l88 140h122zM527 700h138v-413q0 -136 -80 -218.5t-223 -82.5q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M357 725l-86 -140h-116l80 140h122zM449 585h-116l88 140h122zM422 506h132v-506h-132v64q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="724" 
d="M527 700h138v-413q0 -172 -120 -252q-42 -33 -67.5 -69t-25.5 -62q0 -33 32 -33q35 0 59 30l23 -72q-14 -19 -43 -32t-63 -13q-47 0 -77.5 27t-30.5 71q0 53 53 106q-28 -2 -43 -2q-144 0 -223.5 82t-79.5 219v413h139v-411q0 -80 42.5 -128.5t121.5 -48.5
q80 0 122.5 48.5t42.5 128.5v411z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M572 -106l23 -73q-15 -19 -43.5 -32t-62.5 -13q-47 0 -77.5 27t-30.5 71q0 65 76 126h-35v64q-59 -78 -162 -78q-98 0 -149 60t-51 165v295h132v-270q0 -59 27.5 -93t80.5 -34q56 0 89 40t33 108v249h132v-506q-32 -25 -52.5 -54t-20.5 -49q0 -33 32 -33q36 0 59 30z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1001" 
d="M501 851l-52 -78h-120l109 144h127l109 -144h-120zM852 700h144l-212 -700h-145l-138 470l-138 -470h-145l-213 700h144l144 -507l146 507h123l146 -507z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="812" 
d="M406 657l-52 -78h-120l109 144h127l109 -144h-120zM671 506h137l-175 -506h-123l-104 319l-104 -319h-124l-174 506h137l105 -339l104 339h111l105 -339z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="678" 
d="M339 851l-52 -78h-120l109 144h127l109 -144h-120zM520 700h154l-265 -443v-257h-139v257l-266 443h155l181 -311z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="585" 
d="M297 657l-52 -78h-120l109 144h127l109 -144h-120zM440 506h141l-304 -716h-136l92 221l-229 495h145l150 -342z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="678" 
d="M242 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM436 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM520 700h154l-265 -443v-257h-139
v257l-266 443h155l181 -311z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="634" 
d="M472 919l-108 -140h-114l77 140h145zM224 122h377v-122h-567v92l368 486h-361v122h550v-93z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="530" 
d="M419 725l-108 -140h-114l77 140h145zM216 110h276v-110h-452v84l268 313h-262v109h437v-84z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="634" 
d="M318 764q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM224 122h377v-122h-567v92l368 486h-361v122h550v-93z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="530" 
d="M265 570q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM216 110h276v-110h-452v84l268 313h-262v109h437v-84z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="634" 
d="M255 773l-109 144h120l52 -78l53 78h120l-109 -144h-127zM224 122h377v-122h-567v92l368 486h-361v122h550v-93z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="530" 
d="M202 579l-109 144h120l52 -78l53 78h120l-109 -144h-127zM216 110h276v-110h-452v84l268 313h-262v109h437v-84z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="531" 
d="M397 744q77 0 121 -40l-49 -100q-26 28 -67 28q-61 0 -72 -90l-13 -99h132v-108h-146l-37 -288q-13 -98 -61.5 -150t-127.5 -52q-75 0 -117 45l47 95q26 -29 64 -29q61 0 73 91l37 288h-115v108h128l13 99q25 202 190 202z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="668" 
d="M351 -14q-121 0 -206 61.5t-117 150.5l131 40q22 -58 74.5 -94.5t125.5 -36.5q59 0 96 26t37 62q0 28 -21 48t-71 36l-179 56q-73 23 -116 68t-42 110q-1 83 71 142t180 59q109 0 190 -51.5t111 -128.5l-127 -37q-22 43 -68.5 69.5t-104.5 26.5q-50 0 -81.5 -22
t-31.5 -54q0 -46 67 -66l174 -56q92 -28 140 -70.5t48 -121.5q0 -92 -78 -154.5t-202 -62.5zM273 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="519" 
d="M264 -14q-90 0 -158 46t-88 110l119 35q13 -37 48 -60.5t84 -23.5q37 0 60.5 15t23.5 37q0 33 -56 50l-129 38q-125 36 -125 135q0 63 59.5 107.5t143.5 44.5q80 0 140 -35t84 -93l-117 -34q-33 59 -108 59q-30 0 -51 -14t-21 -32q0 -28 42 -40l126 -36q68 -20 105 -51
t37 -93q0 -72 -61.5 -118.5t-157.5 -46.5zM187 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="637" 
d="M622 700v-124h-235v-576h-137v576h-235v124h607zM249 -265l13 201h120l-31 -201h-102z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="412" 
d="M386 16q-60 -24 -118 -24q-84 0 -126.5 48t-42.5 133v217h-85v116h85v164h133v-164h148v-116h-148v-217q0 -64 62 -64q32 0 60 14zM176 -265l13 201h120l-31 -201h-102z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="345" 
d="M345 579h-120l-53 78l-52 -78h-120l109 144h127z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="345" 
d="M225 723h120l-109 -144h-127l-109 144h120l52 -78z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="332" 
d="M166 566q-81 0 -123.5 42t-42.5 106h102q2 -66 64 -66q33 0 48.5 18.5t15.5 47.5h102q0 -64 -42.5 -106t-123.5 -42z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="152" 
d="M76 576q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="248" 
d="M124 545q-53 0 -88.5 34.5t-35.5 85.5q0 52 35.5 86t88.5 34t88.5 -34t35.5 -86q0 -51 -35.5 -85.5t-88.5 -34.5zM124 619q22 0 35 13t13 33q0 21 -13 34t-35 13t-35 -13t-13 -34q0 -20 13 -33t35 -13z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="214" 
d="M108 -224q-47 0 -77.5 27t-30.5 71q0 65 76 126h97q-32 -26 -53 -55t-21 -48q0 -33 33 -33q36 0 59 30l23 -73q-37 -45 -106 -45z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="334" 
d="M237 574q-31 0 -73.5 25.5t-53.5 25.5q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h87q0 -143 -97 -143z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="388" 
d="M0 585l80 140h122l-86 -140h-116zM178 585l88 140h122l-94 -140h-116z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M0 585h-114l-108 140h146z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M-222 585l77 140h145l-108 -140h-114z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M0 579h-120l-53 78l-52 -78h-120l109 144h127z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-97 574q-31 0 -73.5 25.5t-53.5 25.5q-23 0 -23 -48h-87q0 144 96 144q31 0 74 -26t54 -26q23 0 23 48h88q0 -143 -98 -143z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-288 589v94h289v-94h-289z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M-166 566q-81 0 -123.5 42t-42.5 106h102q2 -66 64 -66q33 0 48.5 18.5t15.5 47.5h102q0 -64 -42.5 -106t-123.5 -42z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-76 570q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-261 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM-67 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M-124 545q-53 0 -88.5 34.5t-35.5 85.5q0 52 35.5 86t88.5 34t89 -34.5t36 -85.5t-36 -85.5t-89 -34.5zM-124 619q22 0 35 13t13 33q0 21 -13 34t-35 13t-35 -13t-13 -34q0 -20 13 -33t35 -13z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-388 585l80 140h122l-86 -140h-116zM-210 585l88 140h123l-95 -140h-116z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-120 723h120l-109 -144h-127l-109 144h120l52 -78z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M0 789l-13 -199h-120l31 199h102z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-133 -263l13 199h120l-31 -199h-102z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-116 -223q-61 0 -106 34l16 54q33 -24 73.5 -21.5t40.5 30.5q0 23 -29.5 31.5t-59.5 0.5l29 94h95l-17 -55q32 -4 53 -26.5t21 -51.5q0 -42 -32 -66t-84 -24z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-106 -224q-47 0 -77.5 27t-30.5 71q0 65 76 126h97q-32 -26 -53 -55t-21 -48q0 -33 33 -33q36 0 59 30l23 -73q-37 -45 -106 -45z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="718" 
d="M448 700l266 -700h-710l266 700h178zM192 122h334l-167 447z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="809" 
d="M774 374q0 -154 -122 -253h120v-121h-294v121q69 28 114.5 94t45.5 141q0 99 -65 165t-168 66t-168.5 -66t-65.5 -165q0 -75 45.5 -141t115.5 -94v-121h-294v121h120q-123 98 -123 253q0 141 105.5 240.5t264.5 99.5q158 0 263.5 -99.5t105.5 -240.5z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="663" 
d="M629 105l27 -103q-39 -16 -77 -16q-100 0 -133 68q-56 -68 -153 -68q-52 0 -96 25v-221h-132v716h132v-270q0 -59 28 -93t80 -34q56 0 89 40t33 108v249h132v-354q0 -53 38 -53q11 0 32 6z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="650" 
d="M627 392h-97v-240q0 -52 36 -52q18 0 32 6l28 -99q-39 -17 -78 -17q-74 0 -108.5 40t-34.5 122v240h-163v-392h-125v392h-97v117h607v-117z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1001" 
d="M569 779h-114l-108 140h146zM852 700h144l-212 -700h-145l-138 470l-138 -470h-145l-213 700h144l144 -507l146 507h123l146 -507z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="812" 
d="M474 585h-114l-108 140h146zM671 506h137l-175 -506h-123l-104 319l-104 -319h-124l-174 506h137l105 -339l104 339h111l105 -339z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1001" 
d="M655 919l-108 -140h-114l77 140h145zM852 700h144l-212 -700h-145l-138 470l-138 -470h-145l-213 700h144l144 -507l146 507h123l146 -507z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="812" 
d="M560 725l-108 -140h-114l77 140h145zM671 506h137l-175 -506h-123l-104 319l-104 -319h-124l-174 506h137l105 -339l104 339h111l105 -339z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1001" 
d="M404 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM598 760q-28 0 -47.5 19t-19.5 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM852 700h144l-212 -700h-145
l-138 470l-138 -470h-145l-213 700h144l144 -507l146 507h123l146 -507z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="812" 
d="M309 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t48 -19.5t20 -46.5q0 -28 -19.5 -47.5t-48.5 -19.5zM503 566q-29 0 -48 19t-19 48q0 27 19.5 46.5t47.5 19.5t47.5 -19.5t19.5 -46.5q0 -28 -19.5 -47.5t-47.5 -19.5zM671 506h137l-175 -506h-123l-104 319
l-104 -319h-124l-174 506h137l105 -339l104 339h111l105 -339z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="678" 
d="M408 779h-114l-108 140h146zM520 700h154l-265 -443v-257h-139v257l-266 443h155l181 -311z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="585" 
d="M365 585h-114l-108 140h146zM440 506h141l-304 -716h-136l92 221l-229 495h145l150 -342z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="615" 
d="M60 252v109h495v-109h-495z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="915" 
d="M60 252v109h795v-109h-795z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="273" 
d="M228 700l-44 -277h-139l79 277h104z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="273" 
d="M45 423l44 277h139l-78 -277h-105z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="273" 
d="M45 -137l44 277h139l-78 -277h-105z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="464" 
d="M228 700l-44 -277h-139l79 277h104zM419 700l-44 -277h-139l78 277h105z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="464" 
d="M45 423l44 277h139l-78 -277h-105zM236 423l44 277h139l-79 -277h-104z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="464" 
d="M45 -137l44 277h139l-78 -277h-105zM236 -137l44 277h139l-79 -277h-104z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="508" 
d="M188 0v452h-153v121h153v157h132v-157h153v-121h-153v-452h-132z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="508" 
d="M188 0v157h-153v121h153v174h-153v121h153v157h132v-157h153v-121h-153v-174h153v-121h-153v-157h-132z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="341" 
d="M171 167q-53 1 -89.5 37t-36.5 88q0 50 36.5 84t89.5 36q52 1 88.5 -34t36.5 -86q0 -53 -36.5 -89.5t-88.5 -35.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="712" 
d="M127 -8q-35 0 -58.5 24t-23.5 58q0 32 24 55t58 23t57.5 -23t23.5 -55q0 -34 -23.5 -58t-57.5 -24zM357 -8q-35 0 -58.5 24t-23.5 58q0 32 24 55t58 23t57.5 -23t23.5 -55q0 -34 -23.5 -58t-57.5 -24zM586 -8q-34 0 -58 24t-24 58q0 32 24 55t58 23t57.5 -23t23.5 -55
q0 -34 -23.5 -58t-57.5 -24z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1497" 
d="M244 310q-85 0 -144.5 58t-59.5 141q0 84 59 141t145 57t145 -57t59 -141q0 -83 -59 -141t-145 -58zM243 0l454 700h106l-453 -700h-107zM244 408q44 0 73 29.5t29 71.5t-29 71t-73 29t-73 -28.5t-29 -71.5q0 -42 29 -71.5t73 -29.5zM803 -8q-85 0 -144 57.5t-59 140.5
q0 84 58.5 141.5t144.5 57.5q87 0 145.5 -57.5t58.5 -141.5q0 -83 -58.5 -140.5t-145.5 -57.5zM1253 -8q-85 0 -144 57.5t-59 140.5q0 84 58.5 141.5t144.5 57.5q87 0 145.5 -57.5t58.5 -141.5q0 -83 -58.5 -140.5t-145.5 -57.5zM803 90q44 0 73 29t29 71q0 43 -28.5 72
t-73.5 29q-44 0 -72.5 -29t-28.5 -72q0 -42 28.5 -71t72.5 -29zM1253 90q44 0 73 29t29 71q0 43 -28.5 72t-73.5 29q-44 0 -72.5 -29t-28.5 -72q0 -42 28.5 -71t72.5 -29z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="343" 
d="M283 505l-100 -185l100 -185h-119l-104 185l104 185h119z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="343" 
d="M179 505l104 -185l-104 -185h-119l100 185l-100 185h119z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="181" 
d="M-173 0l434 700h92l-434 -700h-92z" />
    <glyph glyph-name="zero.sups" unicode="&#x2070;" horiz-adv-x="403" 
d="M202 440q-83 0 -131 57t-48 154t48 153.5t131 56.5q79 0 128.5 -57t49.5 -153q0 -97 -48 -154t-130 -57zM202 534q74 0 74 117q0 54 -18.5 85t-55.5 31q-75 0 -75 -116q0 -117 75 -117z" />
    <glyph glyph-name="four.sups" unicode="&#x2074;" horiz-adv-x="353" 
d="M340 609v-85h-47v-74h-94v74h-191v76l133 250h104l-126 -241h80v67h94v-67h47z" />
    <glyph glyph-name="five.sups" unicode="&#x2075;" horiz-adv-x="348" 
d="M194 706q59 0 95 -36.5t36 -94.5t-40.5 -96.5t-105.5 -38.5q-67 0 -108.5 32.5t-52.5 79.5l87 25q19 -52 70 -52q26 0 41.5 14.5t15.5 36.5q0 24 -15.5 39t-43.5 15q-36 0 -68 -19l-72 23l19 216h255v-85h-173l-6 -69q26 10 66 10z" />
    <glyph glyph-name="six.sups" unicode="&#x2076;" horiz-adv-x="338" 
d="M189 722q59 -4 98 -42.5t39 -97.5q0 -62 -44.5 -102t-112.5 -40t-113 40.5t-43 102.5q0 41 29 87l114 180h103l-80 -128h10zM169 525q28 0 44.5 16t16.5 41t-16.5 41.5t-44.5 16.5q-27 0 -44 -16.5t-17 -41.5t17 -41t44 -16z" />
    <glyph glyph-name="seven.sups" unicode="&#x2077;" horiz-adv-x="310" 
d="M6 850h296v-69l-140 -331h-105l133 314h-184v86z" />
    <glyph glyph-name="eight.sups" unicode="&#x2078;" horiz-adv-x="357" 
d="M274 668q66 -32 66 -103q0 -54 -44 -89.5t-117 -35.5t-117 35.5t-44 89.5q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM179 780q-24 0 -38 -12t-14 -31t14 -31t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM179 523
q29 0 45 14.5t16 37.5q0 22 -16.5 36.5t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="nine.sups" unicode="&#x2079;" horiz-adv-x="364" 
d="M182 861q68 0 113 -41t44 -103q-2 -42 -30 -87l-114 -180h-103l81 128h-10q-59 4 -98.5 42.5t-39.5 97.5q0 61 44.5 102t112.5 41zM182 660q27 0 44 16.5t17 41.5t-17 41.5t-44 16.5q-28 0 -44.5 -16.5t-16.5 -41.5t16.5 -41.5t44.5 -16.5z" />
    <glyph glyph-name="zero.sinf" unicode="&#x2080;" horiz-adv-x="403" 
d="M202 -161q-83 0 -131 57.5t-48 154.5t48 153.5t131 56.5q79 0 128.5 -57t49.5 -153q0 -97 -48 -154.5t-130 -57.5zM202 -67q74 0 74 118q0 54 -18.5 85t-55.5 31q-75 0 -75 -116q0 -118 75 -118z" />
    <glyph glyph-name="one.sinf" unicode="&#x2081;" horiz-adv-x="272" 
d="M134 250h86v-400h-100v288l-93 -43l-24 86z" />
    <glyph glyph-name="two.sinf" unicode="&#x2082;" horiz-adv-x="351" 
d="M172 -63h157v-87h-310v63l157 148q44 42 44 68q0 19 -13 31t-36 12q-49 0 -62 -65l-91 26q6 52 48 90t103 38q69 0 111 -34t42 -95q0 -34 -15 -60t-49 -56z" />
    <glyph glyph-name="three.sinf" unicode="&#x2083;" horiz-adv-x="359" 
d="M242 99q43 -11 68.5 -43t25.5 -78q0 -59 -43 -99t-109 -40q-75 0 -116 37t-50 92l87 26q6 -32 26 -51t51 -19q28 0 43.5 16t15.5 39q0 24 -16 39.5t-48 15.5q-11 0 -33 -2l-15 54l73 80h-170v84h290v-69z" />
    <glyph glyph-name="four.sinf" unicode="&#x2084;" horiz-adv-x="353" 
d="M340 9v-85h-47v-74h-94v74h-191v75l133 251h104l-126 -241h80v67h94v-67h47z" />
    <glyph glyph-name="five.sinf" unicode="&#x2085;" horiz-adv-x="348" 
d="M194 106q59 0 95 -36.5t36 -94.5t-41 -97t-105 -39q-67 0 -108.5 32.5t-52.5 80.5l87 25q19 -53 70 -53q26 0 41.5 14.5t15.5 37.5q0 24 -15.5 39t-43.5 15q-36 0 -68 -19l-72 23l19 216h255v-85h-173l-6 -69q26 10 66 10z" />
    <glyph glyph-name="six.sinf" unicode="&#x2086;" horiz-adv-x="338" 
d="M189 122q59 -4 98 -42.5t39 -97.5q0 -62 -44.5 -102.5t-112.5 -40.5t-113 41t-43 103q0 41 29 87l114 180h103l-80 -128h10zM169 -76q28 0 44.5 16.5t16.5 41.5t-16.5 41.5t-44.5 16.5q-27 0 -44 -16.5t-17 -41.5t17 -41.5t44 -16.5z" />
    <glyph glyph-name="seven.sinf" unicode="&#x2087;" horiz-adv-x="310" 
d="M6 250h296v-69l-140 -331h-105l133 314h-184v86z" />
    <glyph glyph-name="eight.sinf" unicode="&#x2088;" horiz-adv-x="357" 
d="M274 68q66 -32 66 -103q0 -54 -44 -90t-117 -36t-117 35.5t-44 90.5q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM179 180q-24 0 -38 -12t-14 -31t14 -31t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM179 -77q29 0 45 14.5
t16 37.5q0 22 -16.5 36.5t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="nine.sinf" unicode="&#x2089;" horiz-adv-x="338" 
d="M169 261q69 0 113.5 -41t43.5 -103q-2 -42 -29 -87l-115 -180h-103l80 128h-9q-59 4 -98 42.5t-39 97.5q0 62 44.5 102.5t111.5 40.5zM169 60q28 0 44.5 16.5t16.5 41.5t-16.5 41.5t-44.5 16.5q-27 0 -44 -16.5t-17 -41.5t17 -41.5t44 -16.5z" />
    <glyph glyph-name="franc" unicode="&#x20a3;" horiz-adv-x="625" 
d="M576 575h-347v-157h317v-122h-317v-103h167v-74h-167v-119h-137v119h-58v74h58v507h484v-125z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="641" 
d="M253 127h355v-127h-565v127h75v92h-61v74h61v58h-61v74h61v74q0 103 62 159t165 56q90 0 151 -53.5t71 -128.5l-122 -35q-24 95 -103 95q-42 0 -65.5 -27t-23.5 -74v-66h234v-74h-234v-58h234v-74h-234v-92z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="755" 
d="M637 170l83 -106q-97 -78 -234 -78q-129 0 -225 72t-129 188h-108v74h95q-1 10 -1 31q0 20 1 30h-95v75h108q33 115 128.5 186.5t222.5 71.5q136 0 236 -78l-84 -106q-62 55 -151 55q-71 0 -124.5 -35t-79.5 -94h258v-75h-278q-2 -20 -2 -29q0 -10 2 -32h278v-74h-258
q27 -59 80.5 -95t123.5 -36q93 0 153 55z" />
    <glyph glyph-name="uni20BF" unicode="&#x20bf;" horiz-adv-x="717" 
d="M558 367q53 -24 83 -67.5t30 -101.5q0 -91 -63.5 -144.5t-173.5 -53.5v-140h-99v140h-81v-140h-98v140h-128v120h81v461h-81v119h128v140h98v-140h81v140h99v-141q95 -5 152.5 -56t57.5 -130q1 -98 -86 -146zM413 581h-170v-171h173q41 0 68 25.5t27 60.5
q0 37 -27.5 61.5t-70.5 23.5zM433 120q46 0 74 26t28 64t-27.5 64t-73.5 26h-191v-180h190z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1104" 
d="M905 383q-72 0 -121 48t-49 117q0 70 49 118t121 48q71 0 120 -48t49 -118q0 -69 -49 -117t-120 -48zM530 246v454h137v-700h-113l-352 455v-455h-137v700h113zM905 623q-33 0 -54.5 -21.5t-21.5 -53.5q0 -31 21.5 -52.5t54.5 -21.5q32 0 53.5 22t21.5 52
q0 32 -21.5 53.5t-53.5 21.5zM753 237v94h304v-94h-304z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="724" 
d="M130 380v248h-100v72h278v-72h-101v-248h-77zM341 380v320h77l97 -139l95 139h79v-320h-76v198l-97 -136l-101 136v-198h-74z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="837" 
d="M428 -11q-168 0 -280 104.5t-112 258.5q0 149 112 254.5t271 105.5t270.5 -105.5t111.5 -254.5v-16h-615v-211q90 -106 242 -106q140 0 229 94h79q-127 -124 -308 -124zM186 366h465v210q-90 106 -232 106q-143 0 -233 -106v-210z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="810" 
d="M120 300v288l-93 -43l-24 86l131 69h86v-400h-100zM533 700h92l-434 -700h-91zM727 218q66 -32 66 -103q0 -54 -44 -90t-117 -36t-117 36t-44 90q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM632 330q-24 0 -38 -12t-14 -31
t14 -31t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM632 73q29 0 45 14.5t16 37.5q0 22 -16.5 36.5t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="897" 
d="M336 428q0 -58 -43 -98t-109 -40q-75 0 -116 37t-50 91l87 26q6 -31 26 -50t51 -19q28 0 43.5 15.5t15.5 38.5q0 24 -16 39.5t-48 15.5q-11 0 -33 -2l-15 54l73 80h-170v84h290v-69l-80 -82q43 -11 68.5 -43t25.5 -78zM620 700h92l-434 -700h-91zM814 218q66 -32 66 -103
q0 -54 -44 -90t-117 -36t-117 36t-44 90q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM719 330q-24 0 -38 -12t-14 -31t14 -31t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM719 73q29 0 45 14.5t16 37.5q0 22 -16.5 36.5
t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="886" 
d="M325 425q0 -58 -40.5 -96.5t-105.5 -38.5q-67 0 -108.5 32.5t-52.5 79.5l87 25q19 -52 70 -52q26 0 41.5 14.5t15.5 36.5q0 24 -15.5 39t-43.5 15q-36 0 -68 -19l-72 23l19 216h255v-85h-173l-6 -69q26 10 66 10q59 0 95 -36.5t36 -94.5zM609 700h92l-434 -700h-91z
M803 218q66 -32 66 -103q0 -54 -44 -90t-117 -36t-117 36t-44 90q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM708 330q-24 0 -38 -12t-14 -31t14 -31t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM708 73q29 0 45 14.5t16 37.5
q0 22 -16.5 36.5t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="848" 
d="M162 300h-105l133 314h-184v86h296v-69zM571 700h92l-434 -700h-91zM765 218q66 -32 66 -103q0 -54 -44 -90t-117 -36t-117 36t-44 90q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM670 330q-24 0 -38 -12t-14 -31t14 -31
t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM670 73q29 0 45 14.5t16 37.5q0 22 -16.5 36.5t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="834" 
d="M774 391v-116h-493l191 -189l-82 -83l-330 330l330 330l82 -83l-192 -189h494z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="780" 
d="M720 385l-83 -83l-185 187v-489h-124v490l-185 -188l-83 83l330 329z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="834" 
d="M445 665l329 -330l-329 -329l-83 82l191 189h-493v116l494 1l-192 189z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="780" 
d="M637 398l83 -83l-330 -329l-330 329l83 83l185 -188v490h124v-489z" />
    <glyph glyph-name="arrowboth" unicode="&#x2194;" horiz-adv-x="1137" 
d="M747 665l330 -330l-330 -329l-82 82l191 189h-575l191 -189l-82 -82l-330 329l330 330l82 -82l-192 -189h577l-192 189z" />
    <glyph glyph-name="arrowupdn" unicode="&#x2195;" horiz-adv-x="780" 
d="M452 50l185 187l83 -82l-330 -330l-330 330l83 82l185 -188v564l-185 -188l-83 82l330 330l330 -330l-83 -82l-185 187v-562z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="668" 
d="M608 120l-86 -86l-347 348l2 -265l-117 -1v466h466l1 -116l-267 2z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="668" 
d="M142 604h466v-466l-117 -1l2 266l-347 -347l-86 86l347 347l-265 -2v117z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="668" 
d="M491 530l117 1v-466h-466l-1 116l267 -2l-348 348l86 86l347 -348z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="668" 
d="M260 156l266 2l1 -116h-467v465l117 1l-2 -266l347 347l86 -85z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="640" 
d="M304 -14q-108 0 -183.5 72.5t-75.5 177.5q0 103 69.5 171.5t176.5 68.5q51 0 97.5 -18.5t75.5 -48.5q-9 81 -58 137t-123 56q-96 0 -168 -69l-29 108q88 73 211 73q136 0 217 -103t81 -271q0 -162 -79.5 -258t-211.5 -96zM308 103q66 0 108 51.5t45 121.5
q-20 34 -63.5 60.5t-93.5 26.5q-56 0 -93.5 -37t-37.5 -89q0 -56 38 -95t97 -39z" />
    <glyph glyph-name="emptyset" unicode="&#x2205;" horiz-adv-x="513" 
d="M444 468q50 -62 50 -148q1 -99 -66.5 -167t-166.5 -68q-85 0 -148 52l-63 -63l-40 40l64 64q-46 62 -46 142q0 99 67 167.5t166 67.5q82 0 143 -47l60 59l39 -40zM121 320q0 -42 21 -75l194 194q-36 21 -76 21q-59 0 -99 -40t-40 -100zM260 181q60 0 100.5 40t40.5 99
q0 44 -24 81l-196 -196q35 -24 79 -24z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="651" 
d="M591 730v-940h-132v820h-267v-820h-132v940h531z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="660" 
d="M215 -80h400v-120h-570v105l284 369l-267 351v105h534v-120h-364l251 -338z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="558" 
d="M60 298v105h438v-105h-438z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="727" 
d="M600 790h132l-271 -881h-141l-147 388h-128v113h220l122 -351z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="873" 
d="M631 553q82 0 139.5 -60t57.5 -143q0 -85 -57.5 -143.5t-139.5 -58.5q-111 0 -194 110q-83 -110 -195 -110q-82 0 -139.5 58.5t-57.5 143.5q0 83 57.5 143t139.5 60q109 0 195 -113q86 113 194 113zM247 254q64 0 137 95q-73 95 -137 95q-38 0 -65.5 -28t-27.5 -67
t27.5 -67t65.5 -28zM627 254q38 0 65.5 28t27.5 67t-27.5 67t-65.5 28q-64 0 -137 -95q73 -95 137 -95z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="578" 
d="M123 -156q-79 0 -123 41l49 105q29 -29 65 -29q64 0 76 86l74 542q28 200 192 200q80 0 122 -41l-49 -105q-26 29 -65 29q-64 0 -77 -85l-74 -542q-26 -201 -190 -201z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="607" 
d="M178 362l-118 34q0 60 32 102t97 42q48 0 113.5 -32.5t82.5 -32.5q44 0 44 62l118 -34q0 -60 -31.5 -102t-95.5 -42q-48 0 -114 32t-83 32q-45 0 -45 -61zM178 166l-118 34q0 60 32 102t97 42q48 0 113.5 -32t82.5 -32q44 0 44 62l118 -34q0 -60 -31.5 -102t-95.5 -42
q-48 0 -114 32t-83 32q-45 0 -45 -62z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="558" 
d="M498 396h-152l-40 -91h192v-105h-238l-57 -130h-93l57 130h-106v105h152l40 91h-193v105h239l56 129h93l-56 -129h106v-105z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="563" 
d="M498 659v-114l-313 -114l313 -115v-114l-438 168v121zM60 60v105h438v-105h-438z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="563" 
d="M65 659l438 -168v-121l-438 -168v114l313 115l-313 114v114zM65 60v105h438v-105h-438z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="621" 
d="M382 700l194 -350l-194 -350h-142l-195 350l195 350h142zM311 119l127 231l-127 231l-128 -231z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="263" 
d="M131 570q-32 0 -54 22t-22 53t22 52.5t54 21.5t54 -21.5t22 -52.5t-22 -53t-54 -22zM65 0v506h133v-506h-133z" />
    <glyph glyph-name="f_f_j" horiz-adv-x="960" 
d="M907 506v-522q0 -98 -43.5 -153t-131.5 -55q-46 0 -94 22l29 102q24 -12 45 -12q63 0 63 85v417h-197v-390h-133v390h-197v-390h-132v390h-97v116h97v46q0 90 48 141t140 51q67 0 114 -22l-32 -106q-30 13 -63 13t-54 -21t-21 -56v-46h197v46q0 90 52 141t151 51
q102 0 177 -47l-32 -106q-65 38 -131 38q-38 0 -61 -20.5t-23 -56.5v-46h329z" />
    <glyph glyph-name="f_j" horiz-adv-x="630" 
d="M578 506v-522q0 -98 -43.5 -153t-131.5 -55q-47 0 -95 22l30 102q22 -12 45 -12q62 0 62 85v417h-197v-390h-132v390h-97v116h97v46q0 90 51.5 141t150.5 51q104 0 177 -47l-32 -106q-65 38 -131 38q-37 0 -60.5 -21t-23.5 -56v-46h330z" />
    <glyph glyph-name="f_t" horiz-adv-x="753" 
d="M695 123l32 -107q-60 -24 -118 -24q-84 0 -126.5 48t-42.5 133v217h-192v-390h-132v390h-97v116h97v46q0 90 47.5 141t138.5 51q53 0 91 -14l-32 -106q-22 5 -40 5q-73 0 -73 -77v-46h192v164h133v-164h148v-116h-148v-217q0 -64 62 -64q32 0 60 14z" />
    <glyph glyph-name="t_t" horiz-adv-x="751" 
d="M692 123l33 -107q-61 -24 -118 -24q-84 0 -126.5 48t-42.5 133v217h-206v-217q0 -64 62 -64q32 0 60 14l32 -107q-60 -24 -118 -24q-84 0 -126.5 48t-42.5 133v217h-85v116h85v164h133v-164h206v164h132v-164h149v-116h-149v-217q0 -64 63 -64q31 0 59 14z" />
    <glyph glyph-name="zero.tf" horiz-adv-x="604" 
d="M304 -14q-136 0 -213 101.5t-77 262.5q0 162 77 263t213 101t212.5 -101t76.5 -263q0 -161 -76.5 -262.5t-212.5 -101.5zM304 111q75 0 113 63.5t38 175.5t-38 175.5t-113 63.5t-114 -64t-39 -175t39 -175t114 -64z" />
    <glyph glyph-name="one.tf" horiz-adv-x="604" 
d="M397 124h140v-124h-455v124h176v423l-157 -73l-34 117l213 109h117v-576z" />
    <glyph glyph-name="two.tf" horiz-adv-x="604" 
d="M265 124h297v-124h-506v94l273 257q45 41 66 71t21 65q0 48 -29.5 76t-81.5 28q-48 0 -82 -31t-45 -84l-124 35q12 83 80 143t168 60q113 0 181.5 -58t68.5 -162q0 -62 -31 -112.5t-97 -111.5z" />
    <glyph glyph-name="three.tf" horiz-adv-x="604" 
d="M386 434q82 -13 132.5 -69t50.5 -142q0 -100 -72.5 -168.5t-181.5 -68.5q-119 0 -188.5 62t-85.5 150l122 35q10 -53 49.5 -90t95.5 -37q60 0 94 34t34 83t-32 81.5t-88 32.5q-41 0 -72 -17l-23 81l142 176h-301v123h482v-92z" />
    <glyph glyph-name="four.tf" horiz-adv-x="604" 
d="M588 257v-121h-88v-136h-137v136h-337v100l241 464h147l-228 -443h177v143h137v-143h88z" />
    <glyph glyph-name="five.tf" horiz-adv-x="604" 
d="M337 456q102 0 168 -66t66 -165q0 -101 -72 -170t-181 -69q-110 0 -178.5 55.5t-91.5 137.5l124 34q16 -47 52 -77t85 -30q60 0 94.5 34.5t34.5 86.5t-34 86t-95 34q-72 0 -124 -40l-108 33l30 360h430v-123h-309l-11 -145q53 24 120 24z" />
    <glyph glyph-name="six.tf" horiz-adv-x="604" 
d="M329 477q109 -6 177.5 -74t68.5 -169q0 -106 -75.5 -177t-190.5 -71q-114 0 -189.5 71t-75.5 177q0 70 51 152l203 314h155l-145 -224q7 1 21 1zM309 109q58 0 94 35.5t36 89.5q0 53 -36 88.5t-94 35.5q-57 0 -93 -35.5t-36 -88.5q0 -54 35.5 -89.5t93.5 -35.5z" />
    <glyph glyph-name="seven.tf" horiz-adv-x="604" 
d="M57 700h503v-95l-265 -605h-146l254 576h-346v124z" />
    <glyph glyph-name="eight.tf" horiz-adv-x="604" 
d="M458 381q121 -53 121 -179q0 -93 -74.5 -154.5t-195.5 -61.5t-196 61.5t-75 154.5q0 126 121 179q-91 46 -91 146q0 79 66 133t175 54q108 0 174 -54t66 -133q0 -98 -91 -146zM309 605q-50 0 -79.5 -26t-29.5 -64t29.5 -64t79.5 -26q49 0 78.5 26t29.5 64t-29.5 64
t-78.5 26zM309 102q60 0 96.5 31.5t36.5 79.5t-36.5 79.5t-96.5 31.5q-62 0 -97.5 -31t-35.5 -80q0 -48 36 -79.5t97 -31.5z" />
    <glyph glyph-name="nine.tf" horiz-adv-x="604" 
d="M312 714q115 0 190.5 -71t75.5 -177q0 -70 -52 -152l-203 -314h-154l144 224h-20q-109 5 -177.5 73t-68.5 169q0 106 75 177t190 71zM312 343q58 0 94 35.5t36 87.5q0 54 -36 89.5t-94 35.5t-93.5 -35.5t-35.5 -89.5q0 -53 35.5 -88t93.5 -35z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="403" 
d="M202 -11q-83 0 -131 57.5t-48 154.5t48 153.5t131 56.5q79 0 128.5 -57t49.5 -153q0 -97 -48 -154.5t-130 -57.5zM202 84q74 0 74 117q0 54 -18.5 85t-55.5 31q-75 0 -75 -116q0 -117 75 -117z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="272" 
d="M134 400h86v-400h-100v288l-93 -43l-24 86z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="351" 
d="M172 87h157v-87h-310v63l157 148q44 42 44 68q0 19 -13 31t-36 12q-49 0 -62 -65l-91 26q6 52 48 90t103 38q69 0 111 -34t42 -95q0 -34 -15 -60t-49 -56z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="359" 
d="M242 249q43 -11 68.5 -43t25.5 -78q0 -59 -43 -99t-109 -40q-75 0 -116 37.5t-50 91.5l87 26q6 -31 26 -50t51 -19q28 0 43.5 15.5t15.5 38.5q0 24 -16 39.5t-48 15.5q-11 0 -33 -2l-15 54l73 80h-170v84h290v-69z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="353" 
d="M340 159v-85h-47v-74h-94v74h-191v76l133 250h104l-126 -241h80v67h94v-67h47z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="348" 
d="M194 256q59 0 95 -36.5t36 -94.5t-41 -97t-105 -39q-67 0 -108.5 33t-52.5 80l87 25q19 -52 70 -52q26 0 41.5 14.5t15.5 36.5q0 24 -15.5 39t-43.5 15q-36 0 -68 -19l-72 23l19 216h255v-85h-173l-6 -69q26 10 66 10z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="338" 
d="M189 272q59 -4 98 -42.5t39 -97.5q0 -62 -44.5 -102.5t-112.5 -40.5t-113 41t-43 103q0 41 29 87l114 180h103l-80 -128h10zM169 75q28 0 44.5 16t16.5 41t-16.5 41.5t-44.5 16.5q-27 0 -44 -16.5t-17 -41.5t17 -41t44 -16z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="310" 
d="M6 400h296v-69l-140 -331h-105l133 314h-184v86z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="357" 
d="M274 218q66 -32 66 -103q0 -54 -44 -90t-117 -36t-117 36t-44 90q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM179 330q-24 0 -38 -12t-14 -31t14 -31t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM179 73q29 0 45 14.5t16 37.5
q0 22 -16.5 36.5t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="364" 
d="M182 411q68 0 113 -41t44 -103q-2 -42 -30 -87l-114 -180h-103l81 128h-10q-59 4 -98.5 42.5t-39.5 97.5q0 61 44.5 102t112.5 41zM182 210q27 0 44 16.5t17 41.5t-17 41.5t-44 16.5q-28 0 -44.5 -16.5t-16.5 -41.5t16.5 -41.5t44.5 -16.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="403" 
d="M202 290q-83 0 -131 57t-48 154t48 153.5t131 56.5q79 0 128.5 -57t49.5 -153q0 -97 -48 -154t-130 -57zM202 384q74 0 74 117q0 54 -18.5 85t-55.5 31q-75 0 -75 -116q0 -117 75 -117z" />
    <glyph glyph-name="one.numr" horiz-adv-x="272" 
d="M134 700h86v-400h-100v288l-93 -43l-24 86z" />
    <glyph glyph-name="two.numr" horiz-adv-x="351" 
d="M172 387h157v-87h-310v63l157 148q44 42 44 68q0 19 -13 31t-36 12q-49 0 -62 -65l-91 26q6 52 48 90t103 38q69 0 111 -34t42 -95q0 -34 -15 -60t-49 -56z" />
    <glyph glyph-name="three.numr" horiz-adv-x="359" 
d="M242 549q43 -11 68.5 -43t25.5 -78q0 -58 -43 -98t-109 -40q-75 0 -116 37t-50 91l87 26q6 -31 26 -50t51 -19q28 0 43.5 15.5t15.5 38.5q0 24 -16 39.5t-48 15.5q-11 0 -33 -2l-15 54l73 80h-170v84h290v-69z" />
    <glyph glyph-name="four.numr" horiz-adv-x="353" 
d="M340 459v-85h-47v-74h-94v74h-191v76l133 250h104l-126 -241h80v67h94v-67h47z" />
    <glyph glyph-name="five.numr" horiz-adv-x="348" 
d="M194 556q59 0 95 -36.5t36 -94.5t-40.5 -96.5t-105.5 -38.5q-67 0 -108.5 32.5t-52.5 79.5l87 25q19 -52 70 -52q26 0 41.5 14.5t15.5 36.5q0 24 -15.5 39t-43.5 15q-36 0 -68 -19l-72 23l19 216h255v-85h-173l-6 -69q26 10 66 10z" />
    <glyph glyph-name="six.numr" horiz-adv-x="338" 
d="M189 572q59 -4 98 -42.5t39 -97.5q0 -62 -44.5 -102t-112.5 -40t-113 40.5t-43 102.5q0 41 29 87l114 180h103l-80 -128h10zM169 375q28 0 44.5 16t16.5 41t-16.5 41.5t-44.5 16.5q-27 0 -44 -16.5t-17 -41.5t17 -41t44 -16z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="310" 
d="M6 700h296v-69l-140 -331h-105l133 314h-184v86z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="357" 
d="M274 518q66 -32 66 -103q0 -54 -44 -89.5t-117 -35.5t-117 35.5t-44 89.5q0 71 66 103q-49 27 -49 83q0 46 39.5 78t104.5 32q64 0 104 -32t40 -78q0 -56 -49 -83zM179 630q-24 0 -38 -12t-14 -31t14 -31t38 -12t38.5 12t14.5 31t-14.5 31t-38.5 12zM179 373
q29 0 45 14.5t16 37.5q0 22 -16.5 36.5t-44.5 14.5t-44.5 -14.5t-16.5 -36.5q0 -23 16 -37.5t45 -14.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="364" 
d="M182 711q68 0 113 -41t44 -103q-2 -42 -30 -87l-114 -180h-103l81 128h-10q-59 4 -98.5 42.5t-39.5 97.5q0 61 44.5 102t112.5 41zM182 510q27 0 44 16.5t17 41.5t-17 41.5t-44 16.5q-28 0 -44.5 -16.5t-16.5 -41.5t16.5 -41.5t44.5 -16.5z" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="E" u2="&#x153;" k="13" />
    <hkern u1="E" u2="&#x151;" k="13" />
    <hkern u1="E" u2="&#x14d;" k="13" />
    <hkern u1="E" u2="&#x123;" k="13" />
    <hkern u1="E" u2="&#x121;" k="13" />
    <hkern u1="E" u2="&#x11f;" k="13" />
    <hkern u1="E" u2="&#x11b;" k="13" />
    <hkern u1="E" u2="&#x119;" k="13" />
    <hkern u1="E" u2="&#x117;" k="13" />
    <hkern u1="E" u2="&#x113;" k="13" />
    <hkern u1="E" u2="&#x111;" k="13" />
    <hkern u1="E" u2="&#x10f;" k="13" />
    <hkern u1="E" u2="&#x10d;" k="13" />
    <hkern u1="E" u2="&#x10b;" k="13" />
    <hkern u1="E" u2="&#x107;" k="13" />
    <hkern u1="E" u2="&#x105;" k="13" />
    <hkern u1="E" u2="&#x103;" k="13" />
    <hkern u1="E" u2="&#x101;" k="13" />
    <hkern u1="E" u2="&#xf8;" k="13" />
    <hkern u1="E" u2="&#xf6;" k="13" />
    <hkern u1="E" u2="&#xf5;" k="13" />
    <hkern u1="E" u2="&#xf4;" k="13" />
    <hkern u1="E" u2="&#xf3;" k="13" />
    <hkern u1="E" u2="&#xf2;" k="13" />
    <hkern u1="E" u2="&#xeb;" k="13" />
    <hkern u1="E" u2="&#xea;" k="13" />
    <hkern u1="E" u2="&#xe9;" k="13" />
    <hkern u1="E" u2="&#xe8;" k="13" />
    <hkern u1="E" u2="&#xe7;" k="13" />
    <hkern u1="E" u2="&#xe6;" k="13" />
    <hkern u1="E" u2="&#xe5;" k="13" />
    <hkern u1="E" u2="&#xe4;" k="13" />
    <hkern u1="E" u2="&#xe3;" k="13" />
    <hkern u1="E" u2="&#xe2;" k="13" />
    <hkern u1="E" u2="&#xe1;" k="13" />
    <hkern u1="E" u2="&#xe0;" k="13" />
    <hkern u1="E" u2="q" k="13" />
    <hkern u1="E" u2="o" k="13" />
    <hkern u1="E" u2="g" k="13" />
    <hkern u1="E" u2="e" k="13" />
    <hkern u1="E" u2="d" k="13" />
    <hkern u1="E" u2="c" k="13" />
    <hkern u1="E" u2="a" k="13" />
    <hkern u1="F" u2="&#x153;" k="23" />
    <hkern u1="F" u2="&#x151;" k="23" />
    <hkern u1="F" u2="&#x14d;" k="23" />
    <hkern u1="F" u2="&#x123;" k="23" />
    <hkern u1="F" u2="&#x121;" k="23" />
    <hkern u1="F" u2="&#x11f;" k="23" />
    <hkern u1="F" u2="&#x11b;" k="23" />
    <hkern u1="F" u2="&#x119;" k="23" />
    <hkern u1="F" u2="&#x117;" k="23" />
    <hkern u1="F" u2="&#x113;" k="23" />
    <hkern u1="F" u2="&#x111;" k="23" />
    <hkern u1="F" u2="&#x10f;" k="23" />
    <hkern u1="F" u2="&#x10d;" k="23" />
    <hkern u1="F" u2="&#x10b;" k="23" />
    <hkern u1="F" u2="&#x107;" k="23" />
    <hkern u1="F" u2="&#x105;" k="23" />
    <hkern u1="F" u2="&#x103;" k="23" />
    <hkern u1="F" u2="&#x101;" k="23" />
    <hkern u1="F" u2="&#xf8;" k="23" />
    <hkern u1="F" u2="&#xf6;" k="23" />
    <hkern u1="F" u2="&#xf5;" k="23" />
    <hkern u1="F" u2="&#xf4;" k="23" />
    <hkern u1="F" u2="&#xf3;" k="23" />
    <hkern u1="F" u2="&#xf2;" k="23" />
    <hkern u1="F" u2="&#xeb;" k="23" />
    <hkern u1="F" u2="&#xea;" k="23" />
    <hkern u1="F" u2="&#xe9;" k="23" />
    <hkern u1="F" u2="&#xe8;" k="23" />
    <hkern u1="F" u2="&#xe7;" k="23" />
    <hkern u1="F" u2="&#xe6;" k="23" />
    <hkern u1="F" u2="&#xe5;" k="23" />
    <hkern u1="F" u2="&#xe4;" k="23" />
    <hkern u1="F" u2="&#xe3;" k="23" />
    <hkern u1="F" u2="&#xe2;" k="23" />
    <hkern u1="F" u2="&#xe1;" k="23" />
    <hkern u1="F" u2="&#xe0;" k="23" />
    <hkern u1="F" u2="q" k="23" />
    <hkern u1="F" u2="o" k="23" />
    <hkern u1="F" u2="g" k="23" />
    <hkern u1="F" u2="e" k="23" />
    <hkern u1="F" u2="d" k="23" />
    <hkern u1="F" u2="c" k="23" />
    <hkern u1="F" u2="a" k="23" />
    <hkern u1="K" u2="&#x153;" k="50" />
    <hkern u1="K" u2="&#x151;" k="50" />
    <hkern u1="K" u2="&#x14d;" k="50" />
    <hkern u1="K" u2="&#x123;" k="50" />
    <hkern u1="K" u2="&#x121;" k="50" />
    <hkern u1="K" u2="&#x11f;" k="50" />
    <hkern u1="K" u2="&#x11b;" k="50" />
    <hkern u1="K" u2="&#x119;" k="50" />
    <hkern u1="K" u2="&#x117;" k="50" />
    <hkern u1="K" u2="&#x113;" k="50" />
    <hkern u1="K" u2="&#x111;" k="50" />
    <hkern u1="K" u2="&#x10f;" k="50" />
    <hkern u1="K" u2="&#x10d;" k="50" />
    <hkern u1="K" u2="&#x10b;" k="50" />
    <hkern u1="K" u2="&#x107;" k="50" />
    <hkern u1="K" u2="&#x105;" k="50" />
    <hkern u1="K" u2="&#x103;" k="50" />
    <hkern u1="K" u2="&#x101;" k="50" />
    <hkern u1="K" u2="&#xf8;" k="50" />
    <hkern u1="K" u2="&#xf6;" k="50" />
    <hkern u1="K" u2="&#xf5;" k="50" />
    <hkern u1="K" u2="&#xf4;" k="50" />
    <hkern u1="K" u2="&#xf3;" k="50" />
    <hkern u1="K" u2="&#xf2;" k="50" />
    <hkern u1="K" u2="&#xeb;" k="50" />
    <hkern u1="K" u2="&#xea;" k="50" />
    <hkern u1="K" u2="&#xe9;" k="50" />
    <hkern u1="K" u2="&#xe8;" k="50" />
    <hkern u1="K" u2="&#xe7;" k="50" />
    <hkern u1="K" u2="&#xe6;" k="50" />
    <hkern u1="K" u2="&#xe5;" k="50" />
    <hkern u1="K" u2="&#xe4;" k="50" />
    <hkern u1="K" u2="&#xe3;" k="50" />
    <hkern u1="K" u2="&#xe2;" k="50" />
    <hkern u1="K" u2="&#xe1;" k="50" />
    <hkern u1="K" u2="&#xe0;" k="50" />
    <hkern u1="K" u2="q" k="50" />
    <hkern u1="K" u2="o" k="50" />
    <hkern u1="K" u2="g" k="50" />
    <hkern u1="K" u2="e" k="50" />
    <hkern u1="K" u2="d" k="50" />
    <hkern u1="K" u2="c" k="50" />
    <hkern u1="K" u2="a" k="50" />
    <hkern u1="L" u2="&#x153;" k="12" />
    <hkern u1="L" u2="&#x151;" k="12" />
    <hkern u1="L" u2="&#x14d;" k="12" />
    <hkern u1="L" u2="&#x123;" k="12" />
    <hkern u1="L" u2="&#x121;" k="12" />
    <hkern u1="L" u2="&#x11f;" k="12" />
    <hkern u1="L" u2="&#x11b;" k="12" />
    <hkern u1="L" u2="&#x119;" k="12" />
    <hkern u1="L" u2="&#x117;" k="12" />
    <hkern u1="L" u2="&#x113;" k="12" />
    <hkern u1="L" u2="&#x111;" k="12" />
    <hkern u1="L" u2="&#x10f;" k="12" />
    <hkern u1="L" u2="&#x10d;" k="12" />
    <hkern u1="L" u2="&#x10b;" k="12" />
    <hkern u1="L" u2="&#x107;" k="12" />
    <hkern u1="L" u2="&#x105;" k="12" />
    <hkern u1="L" u2="&#x103;" k="12" />
    <hkern u1="L" u2="&#x101;" k="12" />
    <hkern u1="L" u2="&#xf8;" k="12" />
    <hkern u1="L" u2="&#xf6;" k="12" />
    <hkern u1="L" u2="&#xf5;" k="12" />
    <hkern u1="L" u2="&#xf4;" k="12" />
    <hkern u1="L" u2="&#xf3;" k="12" />
    <hkern u1="L" u2="&#xf2;" k="12" />
    <hkern u1="L" u2="&#xeb;" k="12" />
    <hkern u1="L" u2="&#xea;" k="12" />
    <hkern u1="L" u2="&#xe9;" k="12" />
    <hkern u1="L" u2="&#xe8;" k="12" />
    <hkern u1="L" u2="&#xe7;" k="12" />
    <hkern u1="L" u2="&#xe6;" k="12" />
    <hkern u1="L" u2="&#xe5;" k="12" />
    <hkern u1="L" u2="&#xe4;" k="12" />
    <hkern u1="L" u2="&#xe3;" k="12" />
    <hkern u1="L" u2="&#xe2;" k="12" />
    <hkern u1="L" u2="&#xe1;" k="12" />
    <hkern u1="L" u2="&#xe0;" k="12" />
    <hkern u1="L" u2="q" k="12" />
    <hkern u1="L" u2="o" k="12" />
    <hkern u1="L" u2="g" k="12" />
    <hkern u1="L" u2="e" k="12" />
    <hkern u1="L" u2="d" k="12" />
    <hkern u1="L" u2="c" k="12" />
    <hkern u1="L" u2="a" k="12" />
    <hkern u1="P" u2="&#x153;" k="19" />
    <hkern u1="P" u2="&#x151;" k="19" />
    <hkern u1="P" u2="&#x14d;" k="19" />
    <hkern u1="P" u2="&#x123;" k="19" />
    <hkern u1="P" u2="&#x121;" k="19" />
    <hkern u1="P" u2="&#x11f;" k="19" />
    <hkern u1="P" u2="&#x11b;" k="19" />
    <hkern u1="P" u2="&#x119;" k="19" />
    <hkern u1="P" u2="&#x117;" k="19" />
    <hkern u1="P" u2="&#x113;" k="19" />
    <hkern u1="P" u2="&#x111;" k="19" />
    <hkern u1="P" u2="&#x10f;" k="19" />
    <hkern u1="P" u2="&#x10d;" k="19" />
    <hkern u1="P" u2="&#x10b;" k="19" />
    <hkern u1="P" u2="&#x107;" k="19" />
    <hkern u1="P" u2="&#x105;" k="19" />
    <hkern u1="P" u2="&#x103;" k="19" />
    <hkern u1="P" u2="&#x101;" k="19" />
    <hkern u1="P" u2="&#xf8;" k="19" />
    <hkern u1="P" u2="&#xf6;" k="19" />
    <hkern u1="P" u2="&#xf5;" k="19" />
    <hkern u1="P" u2="&#xf4;" k="19" />
    <hkern u1="P" u2="&#xf3;" k="19" />
    <hkern u1="P" u2="&#xf2;" k="19" />
    <hkern u1="P" u2="&#xeb;" k="19" />
    <hkern u1="P" u2="&#xea;" k="19" />
    <hkern u1="P" u2="&#xe9;" k="19" />
    <hkern u1="P" u2="&#xe8;" k="19" />
    <hkern u1="P" u2="&#xe7;" k="19" />
    <hkern u1="P" u2="&#xe6;" k="19" />
    <hkern u1="P" u2="&#xe5;" k="19" />
    <hkern u1="P" u2="&#xe4;" k="19" />
    <hkern u1="P" u2="&#xe3;" k="19" />
    <hkern u1="P" u2="&#xe2;" k="19" />
    <hkern u1="P" u2="&#xe1;" k="19" />
    <hkern u1="P" u2="&#xe0;" k="19" />
    <hkern u1="P" u2="q" k="19" />
    <hkern u1="P" u2="o" k="19" />
    <hkern u1="P" u2="g" k="19" />
    <hkern u1="P" u2="e" k="19" />
    <hkern u1="P" u2="d" k="19" />
    <hkern u1="P" u2="c" k="19" />
    <hkern u1="P" u2="a" k="19" />
    <hkern u1="Q" u2="&#x104;" k="15" />
    <hkern u1="Q" u2="&#x102;" k="15" />
    <hkern u1="Q" u2="&#x100;" k="15" />
    <hkern u1="Q" u2="&#xc6;" k="15" />
    <hkern u1="Q" u2="&#xc5;" k="15" />
    <hkern u1="Q" u2="&#xc4;" k="15" />
    <hkern u1="Q" u2="&#xc3;" k="15" />
    <hkern u1="Q" u2="&#xc2;" k="15" />
    <hkern u1="Q" u2="&#xc1;" k="15" />
    <hkern u1="Q" u2="&#xc0;" k="15" />
    <hkern u1="Q" u2="A" k="15" />
    <hkern u1="R" u2="&#x153;" k="20" />
    <hkern u1="R" u2="&#x151;" k="20" />
    <hkern u1="R" u2="&#x14d;" k="20" />
    <hkern u1="R" u2="&#x123;" k="20" />
    <hkern u1="R" u2="&#x121;" k="20" />
    <hkern u1="R" u2="&#x11f;" k="20" />
    <hkern u1="R" u2="&#x11b;" k="20" />
    <hkern u1="R" u2="&#x119;" k="20" />
    <hkern u1="R" u2="&#x117;" k="20" />
    <hkern u1="R" u2="&#x113;" k="20" />
    <hkern u1="R" u2="&#x111;" k="20" />
    <hkern u1="R" u2="&#x10f;" k="20" />
    <hkern u1="R" u2="&#x10d;" k="20" />
    <hkern u1="R" u2="&#x10b;" k="20" />
    <hkern u1="R" u2="&#x107;" k="20" />
    <hkern u1="R" u2="&#x105;" k="20" />
    <hkern u1="R" u2="&#x103;" k="20" />
    <hkern u1="R" u2="&#x101;" k="20" />
    <hkern u1="R" u2="&#xf8;" k="20" />
    <hkern u1="R" u2="&#xf6;" k="20" />
    <hkern u1="R" u2="&#xf5;" k="20" />
    <hkern u1="R" u2="&#xf4;" k="20" />
    <hkern u1="R" u2="&#xf3;" k="20" />
    <hkern u1="R" u2="&#xf2;" k="20" />
    <hkern u1="R" u2="&#xeb;" k="20" />
    <hkern u1="R" u2="&#xea;" k="20" />
    <hkern u1="R" u2="&#xe9;" k="20" />
    <hkern u1="R" u2="&#xe8;" k="20" />
    <hkern u1="R" u2="&#xe7;" k="20" />
    <hkern u1="R" u2="&#xe6;" k="20" />
    <hkern u1="R" u2="&#xe5;" k="20" />
    <hkern u1="R" u2="&#xe4;" k="20" />
    <hkern u1="R" u2="&#xe3;" k="20" />
    <hkern u1="R" u2="&#xe2;" k="20" />
    <hkern u1="R" u2="&#xe1;" k="20" />
    <hkern u1="R" u2="&#xe0;" k="20" />
    <hkern u1="R" u2="q" k="20" />
    <hkern u1="R" u2="o" k="20" />
    <hkern u1="R" u2="g" k="20" />
    <hkern u1="R" u2="e" k="20" />
    <hkern u1="R" u2="d" k="20" />
    <hkern u1="R" u2="c" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="&#x153;" k="103" />
    <hkern u1="T" u2="&#x151;" k="103" />
    <hkern u1="T" u2="&#x14d;" k="103" />
    <hkern u1="T" u2="&#x123;" k="103" />
    <hkern u1="T" u2="&#x121;" k="103" />
    <hkern u1="T" u2="&#x11f;" k="103" />
    <hkern u1="T" u2="&#x11b;" k="103" />
    <hkern u1="T" u2="&#x119;" k="103" />
    <hkern u1="T" u2="&#x117;" k="103" />
    <hkern u1="T" u2="&#x113;" k="103" />
    <hkern u1="T" u2="&#x111;" k="103" />
    <hkern u1="T" u2="&#x10f;" k="103" />
    <hkern u1="T" u2="&#x10d;" k="103" />
    <hkern u1="T" u2="&#x10b;" k="103" />
    <hkern u1="T" u2="&#x107;" k="103" />
    <hkern u1="T" u2="&#x105;" k="103" />
    <hkern u1="T" u2="&#x103;" k="103" />
    <hkern u1="T" u2="&#x101;" k="103" />
    <hkern u1="T" u2="&#xf8;" k="103" />
    <hkern u1="T" u2="&#xf6;" k="103" />
    <hkern u1="T" u2="&#xf5;" k="103" />
    <hkern u1="T" u2="&#xf4;" k="103" />
    <hkern u1="T" u2="&#xf3;" k="103" />
    <hkern u1="T" u2="&#xf2;" k="103" />
    <hkern u1="T" u2="&#xeb;" k="103" />
    <hkern u1="T" u2="&#xea;" k="103" />
    <hkern u1="T" u2="&#xe9;" k="103" />
    <hkern u1="T" u2="&#xe8;" k="103" />
    <hkern u1="T" u2="&#xe7;" k="103" />
    <hkern u1="T" u2="&#xe6;" k="103" />
    <hkern u1="T" u2="&#xe5;" k="103" />
    <hkern u1="T" u2="&#xe4;" k="103" />
    <hkern u1="T" u2="&#xe3;" k="103" />
    <hkern u1="T" u2="&#xe2;" k="103" />
    <hkern u1="T" u2="&#xe1;" k="103" />
    <hkern u1="T" u2="&#xe0;" k="103" />
    <hkern u1="T" u2="q" k="103" />
    <hkern u1="T" u2="o" k="103" />
    <hkern u1="T" u2="g" k="103" />
    <hkern u1="T" u2="e" k="103" />
    <hkern u1="T" u2="d" k="103" />
    <hkern u1="T" u2="c" k="103" />
    <hkern u1="T" u2="a" k="103" />
    <hkern u1="W" u2="&#x153;" k="51" />
    <hkern u1="W" u2="&#x151;" k="51" />
    <hkern u1="W" u2="&#x14d;" k="51" />
    <hkern u1="W" u2="&#x123;" k="51" />
    <hkern u1="W" u2="&#x121;" k="51" />
    <hkern u1="W" u2="&#x11f;" k="51" />
    <hkern u1="W" u2="&#x11b;" k="51" />
    <hkern u1="W" u2="&#x119;" k="51" />
    <hkern u1="W" u2="&#x117;" k="51" />
    <hkern u1="W" u2="&#x113;" k="51" />
    <hkern u1="W" u2="&#x111;" k="51" />
    <hkern u1="W" u2="&#x10f;" k="51" />
    <hkern u1="W" u2="&#x10d;" k="51" />
    <hkern u1="W" u2="&#x10b;" k="51" />
    <hkern u1="W" u2="&#x107;" k="51" />
    <hkern u1="W" u2="&#x105;" k="51" />
    <hkern u1="W" u2="&#x103;" k="51" />
    <hkern u1="W" u2="&#x101;" k="51" />
    <hkern u1="W" u2="&#xf8;" k="51" />
    <hkern u1="W" u2="&#xf6;" k="51" />
    <hkern u1="W" u2="&#xf5;" k="51" />
    <hkern u1="W" u2="&#xf4;" k="51" />
    <hkern u1="W" u2="&#xf3;" k="51" />
    <hkern u1="W" u2="&#xf2;" k="51" />
    <hkern u1="W" u2="&#xeb;" k="51" />
    <hkern u1="W" u2="&#xea;" k="51" />
    <hkern u1="W" u2="&#xe9;" k="51" />
    <hkern u1="W" u2="&#xe8;" k="51" />
    <hkern u1="W" u2="&#xe7;" k="51" />
    <hkern u1="W" u2="&#xe6;" k="51" />
    <hkern u1="W" u2="&#xe5;" k="51" />
    <hkern u1="W" u2="&#xe4;" k="51" />
    <hkern u1="W" u2="&#xe3;" k="51" />
    <hkern u1="W" u2="&#xe2;" k="51" />
    <hkern u1="W" u2="&#xe1;" k="51" />
    <hkern u1="W" u2="&#xe0;" k="51" />
    <hkern u1="W" u2="q" k="51" />
    <hkern u1="W" u2="o" k="51" />
    <hkern u1="W" u2="g" k="51" />
    <hkern u1="W" u2="e" k="51" />
    <hkern u1="W" u2="d" k="51" />
    <hkern u1="W" u2="c" k="51" />
    <hkern u1="W" u2="a" k="51" />
    <hkern u1="X" u2="&#x153;" k="35" />
    <hkern u1="X" u2="&#x151;" k="35" />
    <hkern u1="X" u2="&#x14d;" k="35" />
    <hkern u1="X" u2="&#x123;" k="35" />
    <hkern u1="X" u2="&#x121;" k="35" />
    <hkern u1="X" u2="&#x11f;" k="35" />
    <hkern u1="X" u2="&#x11b;" k="35" />
    <hkern u1="X" u2="&#x119;" k="35" />
    <hkern u1="X" u2="&#x117;" k="35" />
    <hkern u1="X" u2="&#x113;" k="35" />
    <hkern u1="X" u2="&#x111;" k="35" />
    <hkern u1="X" u2="&#x10f;" k="35" />
    <hkern u1="X" u2="&#x10d;" k="35" />
    <hkern u1="X" u2="&#x10b;" k="35" />
    <hkern u1="X" u2="&#x107;" k="35" />
    <hkern u1="X" u2="&#x105;" k="35" />
    <hkern u1="X" u2="&#x103;" k="35" />
    <hkern u1="X" u2="&#x101;" k="35" />
    <hkern u1="X" u2="&#xf8;" k="35" />
    <hkern u1="X" u2="&#xf6;" k="35" />
    <hkern u1="X" u2="&#xf5;" k="35" />
    <hkern u1="X" u2="&#xf4;" k="35" />
    <hkern u1="X" u2="&#xf3;" k="35" />
    <hkern u1="X" u2="&#xf2;" k="35" />
    <hkern u1="X" u2="&#xeb;" k="35" />
    <hkern u1="X" u2="&#xea;" k="35" />
    <hkern u1="X" u2="&#xe9;" k="35" />
    <hkern u1="X" u2="&#xe8;" k="35" />
    <hkern u1="X" u2="&#xe7;" k="35" />
    <hkern u1="X" u2="&#xe6;" k="35" />
    <hkern u1="X" u2="&#xe5;" k="35" />
    <hkern u1="X" u2="&#xe4;" k="35" />
    <hkern u1="X" u2="&#xe3;" k="35" />
    <hkern u1="X" u2="&#xe2;" k="35" />
    <hkern u1="X" u2="&#xe1;" k="35" />
    <hkern u1="X" u2="&#xe0;" k="35" />
    <hkern u1="X" u2="q" k="35" />
    <hkern u1="X" u2="o" k="35" />
    <hkern u1="X" u2="g" k="35" />
    <hkern u1="X" u2="e" k="35" />
    <hkern u1="X" u2="d" k="35" />
    <hkern u1="X" u2="c" k="35" />
    <hkern u1="X" u2="a" k="35" />
    <hkern u1="b" u2="Y" k="95" />
    <hkern u1="b" u2="X" k="35" />
    <hkern u1="b" u2="W" k="51" />
    <hkern u1="e" u2="Y" k="95" />
    <hkern u1="e" u2="X" k="35" />
    <hkern u1="e" u2="W" k="51" />
    <hkern u1="k" u2="&#x153;" k="30" />
    <hkern u1="k" u2="&#x151;" k="30" />
    <hkern u1="k" u2="&#x14d;" k="30" />
    <hkern u1="k" u2="&#x123;" k="30" />
    <hkern u1="k" u2="&#x121;" k="30" />
    <hkern u1="k" u2="&#x11f;" k="30" />
    <hkern u1="k" u2="&#x11b;" k="30" />
    <hkern u1="k" u2="&#x119;" k="30" />
    <hkern u1="k" u2="&#x117;" k="30" />
    <hkern u1="k" u2="&#x113;" k="30" />
    <hkern u1="k" u2="&#x111;" k="30" />
    <hkern u1="k" u2="&#x10f;" k="30" />
    <hkern u1="k" u2="&#x10d;" k="30" />
    <hkern u1="k" u2="&#x10b;" k="30" />
    <hkern u1="k" u2="&#x107;" k="30" />
    <hkern u1="k" u2="&#x105;" k="30" />
    <hkern u1="k" u2="&#x103;" k="30" />
    <hkern u1="k" u2="&#x101;" k="30" />
    <hkern u1="k" u2="&#xf8;" k="30" />
    <hkern u1="k" u2="&#xf6;" k="30" />
    <hkern u1="k" u2="&#xf5;" k="30" />
    <hkern u1="k" u2="&#xf4;" k="30" />
    <hkern u1="k" u2="&#xf3;" k="30" />
    <hkern u1="k" u2="&#xf2;" k="30" />
    <hkern u1="k" u2="&#xeb;" k="30" />
    <hkern u1="k" u2="&#xea;" k="30" />
    <hkern u1="k" u2="&#xe9;" k="30" />
    <hkern u1="k" u2="&#xe8;" k="30" />
    <hkern u1="k" u2="&#xe7;" k="30" />
    <hkern u1="k" u2="&#xe6;" k="30" />
    <hkern u1="k" u2="&#xe5;" k="30" />
    <hkern u1="k" u2="&#xe4;" k="30" />
    <hkern u1="k" u2="&#xe3;" k="30" />
    <hkern u1="k" u2="&#xe2;" k="30" />
    <hkern u1="k" u2="&#xe1;" k="30" />
    <hkern u1="k" u2="&#xe0;" k="30" />
    <hkern u1="k" u2="q" k="30" />
    <hkern u1="k" u2="o" k="30" />
    <hkern u1="k" u2="g" k="30" />
    <hkern u1="k" u2="e" k="30" />
    <hkern u1="k" u2="d" k="30" />
    <hkern u1="k" u2="c" k="30" />
    <hkern u1="k" u2="a" k="30" />
    <hkern u1="o" u2="Y" k="95" />
    <hkern u1="o" u2="X" k="35" />
    <hkern u1="o" u2="W" k="51" />
    <hkern u1="p" u2="Y" k="95" />
    <hkern u1="p" u2="X" k="35" />
    <hkern u1="p" u2="W" k="51" />
    <hkern u1="&#xc0;" u2="f" k="20" />
    <hkern u1="&#xc1;" u2="f" k="20" />
    <hkern u1="&#xc2;" u2="f" k="20" />
    <hkern u1="&#xc3;" u2="f" k="20" />
    <hkern u1="&#xc4;" u2="f" k="20" />
    <hkern u1="&#xc5;" u2="f" k="20" />
    <hkern u1="&#xe6;" u2="Y" k="95" />
    <hkern u1="&#xe6;" u2="X" k="35" />
    <hkern u1="&#xe6;" u2="W" k="51" />
    <hkern u1="&#xe8;" u2="Y" k="95" />
    <hkern u1="&#xe8;" u2="X" k="35" />
    <hkern u1="&#xe8;" u2="W" k="51" />
    <hkern u1="&#xe9;" u2="Y" k="95" />
    <hkern u1="&#xe9;" u2="X" k="35" />
    <hkern u1="&#xe9;" u2="W" k="51" />
    <hkern u1="&#xea;" u2="Y" k="95" />
    <hkern u1="&#xea;" u2="X" k="35" />
    <hkern u1="&#xea;" u2="W" k="51" />
    <hkern u1="&#xeb;" u2="Y" k="95" />
    <hkern u1="&#xeb;" u2="X" k="35" />
    <hkern u1="&#xeb;" u2="W" k="51" />
    <hkern u1="&#xf2;" u2="Y" k="95" />
    <hkern u1="&#xf2;" u2="X" k="35" />
    <hkern u1="&#xf2;" u2="W" k="51" />
    <hkern u1="&#xf3;" u2="Y" k="95" />
    <hkern u1="&#xf3;" u2="X" k="35" />
    <hkern u1="&#xf3;" u2="W" k="51" />
    <hkern u1="&#xf4;" u2="Y" k="95" />
    <hkern u1="&#xf4;" u2="X" k="35" />
    <hkern u1="&#xf4;" u2="W" k="51" />
    <hkern u1="&#xf5;" u2="Y" k="95" />
    <hkern u1="&#xf5;" u2="X" k="35" />
    <hkern u1="&#xf5;" u2="W" k="51" />
    <hkern u1="&#xf6;" u2="Y" k="95" />
    <hkern u1="&#xf6;" u2="X" k="35" />
    <hkern u1="&#xf6;" u2="W" k="51" />
    <hkern u1="&#xf8;" u2="Y" k="95" />
    <hkern u1="&#xf8;" u2="X" k="35" />
    <hkern u1="&#xf8;" u2="W" k="51" />
    <hkern u1="&#xfe;" u2="Y" k="95" />
    <hkern u1="&#xfe;" u2="X" k="35" />
    <hkern u1="&#xfe;" u2="W" k="51" />
    <hkern u1="&#x100;" u2="f" k="20" />
    <hkern u1="&#x102;" u2="f" k="20" />
    <hkern u1="&#x104;" u2="f" k="20" />
    <hkern u1="&#x113;" u2="Y" k="95" />
    <hkern u1="&#x113;" u2="X" k="35" />
    <hkern u1="&#x113;" u2="W" k="51" />
    <hkern u1="&#x117;" u2="Y" k="95" />
    <hkern u1="&#x117;" u2="X" k="35" />
    <hkern u1="&#x117;" u2="W" k="51" />
    <hkern u1="&#x119;" u2="Y" k="95" />
    <hkern u1="&#x119;" u2="X" k="35" />
    <hkern u1="&#x119;" u2="W" k="51" />
    <hkern u1="&#x11b;" u2="Y" k="95" />
    <hkern u1="&#x11b;" u2="X" k="35" />
    <hkern u1="&#x11b;" u2="W" k="51" />
    <hkern u1="&#x14d;" u2="Y" k="95" />
    <hkern u1="&#x14d;" u2="X" k="35" />
    <hkern u1="&#x14d;" u2="W" k="51" />
    <hkern u1="&#x151;" u2="Y" k="95" />
    <hkern u1="&#x151;" u2="X" k="35" />
    <hkern u1="&#x151;" u2="W" k="51" />
    <hkern u1="&#x153;" u2="Y" k="95" />
    <hkern u1="&#x153;" u2="X" k="35" />
    <hkern u1="&#x153;" u2="W" k="51" />
    <hkern g1="at"
	g2="four"
	k="10" />
    <hkern g1="at"
	g2="six"
	k="20" />
    <hkern g1="at"
	g2="two"
	k="10" />
    <hkern g1="at"
	g2="zero"
	k="40" />
    <hkern g1="at"
	g2="eight"
	k="20" />
    <hkern g1="uni20BF"
	g2="nine"
	k="10" />
    <hkern g1="uni20BF"
	g2="one"
	k="10" />
    <hkern g1="uni20BF"
	g2="three"
	k="10" />
    <hkern g1="cent"
	g2="five"
	k="20" />
    <hkern g1="cent"
	g2="two"
	k="20" />
    <hkern g1="copyright,registered"
	g2="five"
	k="10" />
    <hkern g1="copyright,registered"
	g2="four"
	k="20" />
    <hkern g1="copyright,registered"
	g2="six"
	k="10" />
    <hkern g1="copyright,registered"
	g2="three"
	k="10" />
    <hkern g1="copyright,registered"
	g2="eight"
	k="10" />
    <hkern g1="currency,emptyset"
	g2="five"
	k="30" />
    <hkern g1="currency,emptyset"
	g2="seven"
	k="23" />
    <hkern g1="currency,emptyset"
	g2="six"
	k="3" />
    <hkern g1="currency,emptyset"
	g2="zero"
	k="28" />
    <hkern g1="currency,emptyset"
	g2="eight"
	k="3" />
    <hkern g1="dollar"
	g2="five"
	k="10" />
    <hkern g1="dollar"
	g2="nine"
	k="5" />
    <hkern g1="dollar"
	g2="one"
	k="3" />
    <hkern g1="dollar"
	g2="six"
	k="10" />
    <hkern g1="dollar"
	g2="eight"
	k="20" />
    <hkern g1="Euro"
	g2="five"
	k="10" />
    <hkern g1="Euro"
	g2="seven"
	k="10" />
    <hkern g1="franc"
	g2="five"
	k="50" />
    <hkern g1="franc"
	g2="nine"
	k="20" />
    <hkern g1="franc"
	g2="one"
	k="18" />
    <hkern g1="franc"
	g2="zero"
	k="40" />
    <hkern g1="franc"
	g2="eight"
	k="-20" />
    <hkern g1="lira"
	g2="seven"
	k="20" />
    <hkern g1="lira"
	g2="six"
	k="10" />
    <hkern g1="lira"
	g2="zero"
	k="10" />
    <hkern g1="paragraph"
	g2="five"
	k="5" />
    <hkern g1="paragraph"
	g2="four"
	k="3" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="two"
	k="10" />
    <hkern g1="paragraph"
	g2="zero"
	k="3" />
    <hkern g1="paragraph"
	g2="eight"
	k="20" />
    <hkern g1="percent,perthousand"
	g2="five"
	k="-3009" />
    <hkern g1="backslash"
	g2="eight"
	k="20" />
    <hkern g1="backslash"
	g2="seven"
	k="30" />
    <hkern g1="backslash"
	g2="six"
	k="20" />
    <hkern g1="backslash"
	g2="three"
	k="80" />
    <hkern g1="backslash"
	g2="five"
	k="40" />
    <hkern g1="braceleft"
	g2="four"
	k="10" />
    <hkern g1="braceleft"
	g2="two"
	k="20" />
    <hkern g1="braceright"
	g2="one"
	k="10" />
    <hkern g1="braceright"
	g2="zero"
	k="-25" />
    <hkern g1="braceright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="bracketleft"
	g2="five"
	k="15" />
    <hkern g1="bracketleft"
	g2="nine"
	k="10" />
    <hkern g1="bracketleft"
	g2="asterisk,degree,trademark"
	k="55" />
    <hkern g1="bracketleft"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="bracketleft"
	g2="two"
	k="20" />
    <hkern g1="bracketleft"
	g2="space"
	k="65" />
    <hkern g1="bracketleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="four"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="three"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="two"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="four"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="three"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="zero"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="eight"
	k="8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="four"
	k="33" />
    <hkern g1="guillemotright,guilsinglright"
	g2="five"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="one"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="zero"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="asterisk,degree,trademark"
	k="29" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="numbersign"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="parenleft"
	g2="eight"
	k="-20" />
    <hkern g1="parenleft"
	g2="four"
	k="-20" />
    <hkern g1="parenleft"
	g2="seven"
	k="30" />
    <hkern g1="parenleft"
	g2="six"
	k="-20" />
    <hkern g1="parenleft"
	g2="five"
	k="-20" />
    <hkern g1="parenleft"
	g2="nine"
	k="-20" />
    <hkern g1="parenleft"
	g2="one"
	k="60" />
    <hkern g1="parenleft"
	g2="two"
	k="10" />
    <hkern g1="parenright"
	g2="eight"
	k="30" />
    <hkern g1="parenright"
	g2="four"
	k="20" />
    <hkern g1="parenright"
	g2="asterisk,degree,trademark"
	k="18" />
    <hkern g1="parenright"
	g2="space"
	k="20" />
    <hkern g1="parenright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="eight"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="four"
	k="100" />
    <hkern g1="periodcentered,bullet"
	g2="seven"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="two"
	k="-60" />
    <hkern g1="periodcentered,bullet"
	g2="space"
	k="50" />
    <hkern g1="periodcentered,bullet"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="question"
	g2="three"
	k="80" />
    <hkern g1="question"
	g2="nine"
	k="30" />
    <hkern g1="question"
	g2="one"
	k="100" />
    <hkern g1="question"
	g2="zero"
	k="-10" />
    <hkern g1="question"
	g2="asterisk,degree,trademark"
	k="80" />
    <hkern g1="question"
	g2="space"
	k="10" />
    <hkern g1="question"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="questiondown"
	g2="six"
	k="30" />
    <hkern g1="questiondown"
	g2="three"
	k="30" />
    <hkern g1="questiondown"
	g2="five"
	k="10" />
    <hkern g1="questiondown"
	g2="nine"
	k="40" />
    <hkern g1="questiondown"
	g2="one"
	k="40" />
    <hkern g1="questiondown"
	g2="zero"
	k="40" />
    <hkern g1="questiondown"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="questiondown"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="100" />
    <hkern g1="questiondown"
	g2="two"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five"
	k="-4337" />
    <hkern g1="five"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-20" />
    <hkern g1="five"
	g2="V"
	k="8" />
    <hkern g1="five"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="five"
	g2="X"
	k="3" />
    <hkern g1="five"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="3" />
    <hkern g1="five"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="five"
	g2="backslash"
	k="10" />
    <hkern g1="five"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="five"
	g2="five"
	k="10" />
    <hkern g1="five"
	g2="Euro"
	k="15" />
    <hkern g1="five"
	g2="hbar"
	k="10" />
    <hkern g1="five"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="five"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="five"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="8" />
    <hkern g1="five"
	g2="copyright,registered"
	k="3" />
    <hkern g1="four"
	g2="parenright"
	k="15" />
    <hkern g1="four"
	g2="Hbar"
	k="10" />
    <hkern g1="four"
	g2="Tbar"
	k="10" />
    <hkern g1="four"
	g2="at"
	k="10" />
    <hkern g1="four"
	g2="uni20BF"
	k="8" />
    <hkern g1="four"
	g2="braceright"
	k="3" />
    <hkern g1="four"
	g2="cent"
	k="10" />
    <hkern g1="four"
	g2="Euro"
	k="-20" />
    <hkern g1="four"
	g2="hbar"
	k="15" />
    <hkern g1="four"
	g2="lira"
	k="5" />
    <hkern g1="four"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="four"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="four"
	g2="zero"
	k="10" />
    <hkern g1="four"
	g2="dollar"
	k="10" />
    <hkern g1="nine"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="nine"
	g2="ampersand"
	k="10" />
    <hkern g1="nine"
	g2="eth"
	k="10" />
    <hkern g1="nine"
	g2="florin"
	k="10" />
    <hkern g1="nine"
	g2="four"
	k="8" />
    <hkern g1="nine"
	g2="radical"
	k="3" />
    <hkern g1="nine"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="nine"
	g2="parenright"
	k="-20" />
    <hkern g1="nine"
	g2="Hbar"
	k="15" />
    <hkern g1="nine"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="5" />
    <hkern g1="nine"
	g2="at"
	k="10" />
    <hkern g1="nine"
	g2="uni20BF"
	k="30" />
    <hkern g1="nine"
	g2="braceleft"
	k="10" />
    <hkern g1="nine"
	g2="bracketright"
	k="10" />
    <hkern g1="nine"
	g2="cent"
	k="10" />
    <hkern g1="nine"
	g2="Euro"
	k="13" />
    <hkern g1="nine"
	g2="hbar"
	k="-10" />
    <hkern g1="nine"
	g2="lira"
	k="10" />
    <hkern g1="nine"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="nine"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="nine"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="one"
	g2="x"
	k="15" />
    <hkern g1="one"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="one"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="one"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="one"
	g2="paragraph"
	k="8" />
    <hkern g1="one"
	g2="questiondown"
	k="3" />
    <hkern g1="one"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="one"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="one"
	g2="ampersand"
	k="15" />
    <hkern g1="one"
	g2="eight"
	k="5" />
    <hkern g1="one"
	g2="florin"
	k="10" />
    <hkern g1="one"
	g2="four"
	k="30" />
    <hkern g1="one"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="one"
	g2="six"
	k="10" />
    <hkern g1="one"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="one"
	g2="parenright"
	k="13" />
    <hkern g1="one"
	g2="Hbar"
	k="-10" />
    <hkern g1="one"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="one"
	g2="Tbar"
	k="10" />
    <hkern g1="one"
	g2="at"
	k="30" />
    <hkern g1="one"
	g2="uni20BF"
	k="10" />
    <hkern g1="seven"
	g2="underscore"
	k="15" />
    <hkern g1="seven"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="seven"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="seven"
	g2="nine"
	k="8" />
    <hkern g1="seven"
	g2="percent,perthousand"
	k="3" />
    <hkern g1="seven"
	g2="slash"
	k="10" />
    <hkern g1="seven"
	g2="x"
	k="-20" />
    <hkern g1="seven"
	g2="T,uni0162,Tcaron,uni021A"
	k="15" />
    <hkern g1="seven"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="seven"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="seven"
	g2="paragraph"
	k="30" />
    <hkern g1="seven"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="seven"
	g2="J"
	k="10" />
    <hkern g1="seven"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="seven"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="13" />
    <hkern g1="seven"
	g2="ampersand"
	k="-10" />
    <hkern g1="seven"
	g2="eight"
	k="10" />
    <hkern g1="seven"
	g2="eth"
	k="10" />
    <hkern g1="seven"
	g2="florin"
	k="30" />
    <hkern g1="seven"
	g2="four"
	k="10" />
    <hkern g1="six"
	g2="asterisk,degree,trademark"
	k="15" />
    <hkern g1="six"
	g2="backslash"
	k="10" />
    <hkern g1="six"
	g2="five"
	k="10" />
    <hkern g1="six"
	g2="one"
	k="10" />
    <hkern g1="six"
	g2="periodcentered,bullet"
	k="8" />
    <hkern g1="six"
	g2="seven"
	k="3" />
    <hkern g1="six"
	g2="two"
	k="10" />
    <hkern g1="six"
	g2="underscore"
	k="-20" />
    <hkern g1="six"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="15" />
    <hkern g1="six"
	g2="yen"
	k="5" />
    <hkern g1="six"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="six"
	g2="nine"
	k="30" />
    <hkern g1="six"
	g2="numbersign"
	k="10" />
    <hkern g1="six"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="six"
	g2="slash"
	k="10" />
    <hkern g1="six"
	g2="x"
	k="13" />
    <hkern g1="six"
	g2="T,uni0162,Tcaron,uni021A"
	k="-10" />
    <hkern g1="six"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="six"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="six"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="six"
	g2="paragraph"
	k="10" />
    <hkern g1="three"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="8" />
    <hkern g1="three"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="three"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="three"
	g2="asterisk,degree,trademark"
	k="-20" />
    <hkern g1="three"
	g2="backslash"
	k="15" />
    <hkern g1="three"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="three"
	g2="one"
	k="10" />
    <hkern g1="three"
	g2="periodcentered,bullet"
	k="30" />
    <hkern g1="three"
	g2="question"
	k="10" />
    <hkern g1="three"
	g2="three"
	k="10" />
    <hkern g1="three"
	g2="two"
	k="10" />
    <hkern g1="three"
	g2="underscore"
	k="13" />
    <hkern g1="three"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="three"
	g2="yen"
	k="10" />
    <hkern g1="three"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="three"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="three"
	g2="nine"
	k="10" />
    <hkern g1="three"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="3" />
    <hkern g1="three"
	g2="zero"
	k="50" />
    <hkern g1="three"
	g2="copyright,registered"
	k="30" />
    <hkern g1="three"
	g2="dollar"
	k="10" />
    <hkern g1="two"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="two"
	g2="V"
	k="10" />
    <hkern g1="two"
	g2="X"
	k="10" />
    <hkern g1="two"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="two"
	g2="asterisk,degree,trademark"
	k="13" />
    <hkern g1="two"
	g2="backslash"
	k="-10" />
    <hkern g1="two"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="two"
	g2="five"
	k="10" />
    <hkern g1="two"
	g2="one"
	k="30" />
    <hkern g1="two"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="two"
	g2="uni20BF"
	k="3" />
    <hkern g1="two"
	g2="braceleft"
	k="50" />
    <hkern g1="two"
	g2="braceright"
	k="30" />
    <hkern g1="two"
	g2="bracketright"
	k="10" />
    <hkern g1="two"
	g2="cent"
	k="70" />
    <hkern g1="two"
	g2="Euro"
	k="85" />
    <hkern g1="two"
	g2="hbar"
	k="80" />
    <hkern g1="two"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="two"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="-20" />
    <hkern g1="two"
	g2="zero"
	k="53" />
    <hkern g1="two"
	g2="copyright,registered"
	k="35" />
    <hkern g1="zero"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="zero"
	g2="four"
	k="3" />
    <hkern g1="zero"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="50" />
    <hkern g1="zero"
	g2="radical"
	k="30" />
    <hkern g1="zero"
	g2="six"
	k="10" />
    <hkern g1="zero"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="70" />
    <hkern g1="zero"
	g2="parenright"
	k="85" />
    <hkern g1="zero"
	g2="Hbar"
	k="80" />
    <hkern g1="zero"
	g2="at"
	k="40" />
    <hkern g1="zero"
	g2="uni20BF"
	k="-20" />
    <hkern g1="zero"
	g2="braceleft"
	k="53" />
    <hkern g1="zero"
	g2="braceright"
	k="35" />
    <hkern g1="zero"
	g2="hbar"
	k="20" />
    <hkern g1="zero"
	g2="lira"
	k="10" />
    <hkern g1="zero"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="zero"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="23" />
    <hkern g1="zero"
	g2="zero"
	k="-10" />
    <hkern g1="zero"
	g2="copyright,registered"
	k="50" />
    <hkern g1="zero"
	g2="dollar"
	k="40" />
    <hkern g1="B,germandbls"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="B,germandbls"
	g2="J"
	k="30" />
    <hkern g1="B,germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="35" />
    <hkern g1="B,germandbls"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="50" />
    <hkern g1="B,germandbls"
	g2="T,uni0162,Tcaron,uni021A"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="Tbar"
	k="14" />
    <hkern g1="B,germandbls"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="65" />
    <hkern g1="B,germandbls"
	g2="V"
	k="35" />
    <hkern g1="B,germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,germandbls"
	g2="X"
	k="60" />
    <hkern g1="B,germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="85" />
    <hkern g1="B,germandbls"
	g2="ampersand"
	k="-20" />
    <hkern g1="B,germandbls"
	g2="asterisk,degree,trademark"
	k="80" />
    <hkern g1="B,germandbls"
	g2="at"
	k="20" />
    <hkern g1="B,germandbls"
	g2="backslash"
	k="33" />
    <hkern g1="B,germandbls"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="B,germandbls"
	g2="copyright,registered"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eight"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eth"
	k="10" />
    <hkern g1="B,germandbls"
	g2="five"
	k="38" />
    <hkern g1="B,germandbls"
	g2="four"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="guillemotleft,guilsinglleft"
	k="27" />
    <hkern g1="B,germandbls"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="3" />
    <hkern g1="B,germandbls"
	g2="underscore"
	k="10" />
    <hkern g1="B,germandbls"
	g2="z,zacute,zdotaccent,zcaron"
	k="3" />
    <hkern g1="B,germandbls"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="6" />
    <hkern g1="B,germandbls"
	g2="colon,semicolon"
	k="18" />
    <hkern g1="B,germandbls"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="13" />
    <hkern g1="B,germandbls"
	g2="braceright"
	k="22" />
    <hkern g1="B,germandbls"
	g2="exclam,exclamdown"
	k="20" />
    <hkern g1="B,germandbls"
	g2="parenright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="30" />
    <hkern g1="B,germandbls"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="1" />
    <hkern g1="B,germandbls"
	g2="Hbar"
	k="20" />
    <hkern g1="B,germandbls"
	g2="bracketright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Eth,Dcroat"
	k="27" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="nine"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="numbersign"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="one"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="paragraph"
	k="18" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="periodcentered,bullet"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="question"
	k="22" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="questiondown"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="section"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="seven"
	k="1" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="six"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="three"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="slash"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="parenleft"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="Tbar"
	k="3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="V"
	k="18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="22" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="ampersand"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="asterisk,degree,trademark"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="at"
	k="30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="backslash"
	k="1" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eight"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eth"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="section"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="seven"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="six"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="underscore"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="F"
	g2="Tbar"
	k="10" />
    <hkern g1="F"
	g2="X"
	k="10" />
    <hkern g1="F"
	g2="at"
	k="20" />
    <hkern g1="F"
	g2="backslash"
	k="20" />
    <hkern g1="F"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="F"
	g2="five"
	k="3" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="F"
	g2="numbersign"
	k="10" />
    <hkern g1="F"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="5" />
    <hkern g1="F"
	g2="one"
	k="5" />
    <hkern g1="F"
	g2="paragraph"
	k="40" />
    <hkern g1="F"
	g2="periodcentered,bullet"
	k="20" />
    <hkern g1="F"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="F"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="F"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="F"
	g2="slash"
	k="10" />
    <hkern g1="F"
	g2="braceright"
	k="30" />
    <hkern g1="F"
	g2="exclam,exclamdown"
	k="20" />
    <hkern g1="F"
	g2="parenright"
	k="5" />
    <hkern g1="F"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="16" />
    <hkern g1="F"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="15" />
    <hkern g1="F"
	g2="Hbar"
	k="25" />
    <hkern g1="F"
	g2="two"
	k="40" />
    <hkern g1="F"
	g2="bracketright"
	k="3" />
    <hkern g1="F"
	g2="hbar"
	k="5" />
    <hkern g1="F"
	g2="j"
	k="40" />
    <hkern g1="F"
	g2="braceleft"
	k="23" />
    <hkern g1="Hbar"
	g2="J"
	k="15" />
    <hkern g1="Hbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Hbar"
	g2="Tbar"
	k="5" />
    <hkern g1="Hbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="Hbar"
	g2="V"
	k="40" />
    <hkern g1="Hbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="Hbar"
	g2="one"
	k="10" />
    <hkern g1="Hbar"
	g2="paragraph"
	k="20" />
    <hkern g1="Hbar"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="Hbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="Hbar"
	g2="question"
	k="30" />
    <hkern g1="Hbar"
	g2="questiondown"
	k="20" />
    <hkern g1="Hbar"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="Hbar"
	g2="section"
	k="16" />
    <hkern g1="Hbar"
	g2="seven"
	k="15" />
    <hkern g1="Hbar"
	g2="six"
	k="25" />
    <hkern g1="Hbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="Hbar"
	g2="three"
	k="3" />
    <hkern g1="Hbar"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="5" />
    <hkern g1="Hbar"
	g2="x"
	k="40" />
    <hkern g1="Hbar"
	g2="zero"
	k="23" />
    <hkern g1="Hbar"
	g2="exclam,exclamdown"
	k="9" />
    <hkern g1="Hbar"
	g2="two"
	k="5" />
    <hkern g1="Hbar"
	g2="bracketright"
	k="40" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="K,uni0136"
	g2="V"
	k="20" />
    <hkern g1="K,uni0136"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="K,uni0136"
	g2="X"
	k="10" />
    <hkern g1="K,uni0136"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="K,uni0136"
	g2="ampersand"
	k="20" />
    <hkern g1="K,uni0136"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="K,uni0136"
	g2="at"
	k="16" />
    <hkern g1="K,uni0136"
	g2="backslash"
	k="15" />
    <hkern g1="K,uni0136"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="K,uni0136"
	g2="copyright,registered"
	k="40" />
    <hkern g1="K,uni0136"
	g2="eight"
	k="3" />
    <hkern g1="K,uni0136"
	g2="eth"
	k="5" />
    <hkern g1="K,uni0136"
	g2="four"
	k="40" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="23" />
    <hkern g1="K,uni0136"
	g2="questiondown"
	k="9" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="K,uni0136"
	g2="three"
	k="40" />
    <hkern g1="K,uni0136"
	g2="underscore"
	k="5" />
    <hkern g1="K,uni0136"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="11" />
    <hkern g1="K,uni0136"
	g2="braceright"
	k="30" />
    <hkern g1="K,uni0136"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="K,uni0136"
	g2="parenright"
	k="5" />
    <hkern g1="K,uni0136"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="K,uni0136"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="3" />
    <hkern g1="K,uni0136"
	g2="Hbar"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Eth,Dcroat"
	k="23" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="ampersand"
	k="9" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="copyright,registered"
	k="5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="eight"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="paragraph"
	k="25" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="periodcentered,bullet"
	k="11" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="questiondown"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="section"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="seven"
	k="3" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="six"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="parenright"
	k="21" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="j"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="V"
	k="25" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="11" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="ampersand"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="at"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="backslash"
	k="3" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="questiondown"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="21" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="x"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="hbar"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="13" />
    <hkern g1="P"
	g2="ampersand"
	k="15" />
    <hkern g1="P"
	g2="asterisk,degree,trademark"
	k="21" />
    <hkern g1="P"
	g2="four"
	k="5" />
    <hkern g1="P"
	g2="nine"
	k="-10" />
    <hkern g1="P"
	g2="one"
	k="5" />
    <hkern g1="P"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="P"
	g2="questiondown"
	k="10" />
    <hkern g1="P"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="P"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="13" />
    <hkern g1="P"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="3" />
    <hkern g1="P"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="P"
	g2="slash"
	k="5" />
    <hkern g1="P"
	g2="parenright"
	k="16" />
    <hkern g1="P"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="P"
	g2="bracketright"
	k="5" />
    <hkern g1="P"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="-10" />
    <hkern g1="P"
	g2="j"
	k="-10" />
    <hkern g1="P"
	g2="braceleft"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="ampersand"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="eth"
	k="15" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="five"
	k="13" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="nine"
	k="3" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="paragraph"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="16" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="section"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="three"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="x"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="zero"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Hbar"
	k="13" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="two"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="3" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="V"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="X"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="asterisk,degree,trademark"
	k="16" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="at"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="eight"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="five"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="four"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="section"
	k="40" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="seven"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="six"
	k="13" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="underscore"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="100" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="exclam,exclamdown"
	k="23" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="two"
	k="20" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="braceleft"
	k="3" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="parenleft"
	k="3" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="at"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="backslash"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="13" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="copyright,registered"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="numbersign"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="one"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="questiondown"
	k="23" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="zero"
	k="3" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="3" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="13" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="slash"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="braceright"
	k="5" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Hbar"
	k="18" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="two"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="bracketright"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="j"
	k="16" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="braceleft"
	k="90" />
    <hkern g1="Tbar"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="15" />
    <hkern g1="Tbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Tbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="100" />
    <hkern g1="Tbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Tbar"
	g2="ampersand"
	k="23" />
    <hkern g1="Tbar"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Tbar"
	g2="five"
	k="-10" />
    <hkern g1="Tbar"
	g2="guillemotleft,guilsinglleft"
	k="3" />
    <hkern g1="Tbar"
	g2="guillemotright,guilsinglright"
	k="3" />
    <hkern g1="Tbar"
	g2="hyphen,endash,emdash"
	k="60" />
    <hkern g1="Tbar"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-20" />
    <hkern g1="Tbar"
	g2="one"
	k="13" />
    <hkern g1="Tbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="15" />
    <hkern g1="Tbar"
	g2="question"
	k="5" />
    <hkern g1="Tbar"
	g2="questiondown"
	k="10" />
    <hkern g1="Tbar"
	g2="section"
	k="50" />
    <hkern g1="Tbar"
	g2="six"
	k="18" />
    <hkern g1="Tbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="80" />
    <hkern g1="Tbar"
	g2="three"
	k="15" />
    <hkern g1="Tbar"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="30" />
    <hkern g1="Tbar"
	g2="x"
	k="16" />
    <hkern g1="Tbar"
	g2="zero"
	k="90" />
    <hkern g1="Tbar"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="Tbar"
	g2="underscore"
	k="5" />
    <hkern g1="Tbar"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="Tbar"
	g2="two"
	k="5" />
    <hkern g1="Thorn"
	g2="Eth,Dcroat"
	k="3" />
    <hkern g1="Thorn"
	g2="J"
	k="3" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="60" />
    <hkern g1="Thorn"
	g2="Tbar"
	k="-20" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="13" />
    <hkern g1="Thorn"
	g2="X"
	k="15" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="5" />
    <hkern g1="Thorn"
	g2="ampersand"
	k="10" />
    <hkern g1="Thorn"
	g2="at"
	k="50" />
    <hkern g1="Thorn"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="18" />
    <hkern g1="Thorn"
	g2="copyright,registered"
	k="80" />
    <hkern g1="Thorn"
	g2="eight"
	k="15" />
    <hkern g1="Thorn"
	g2="five"
	k="30" />
    <hkern g1="Thorn"
	g2="four"
	k="16" />
    <hkern g1="Thorn"
	g2="guillemotleft,guilsinglleft"
	k="90" />
    <hkern g1="Thorn"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="Thorn"
	g2="numbersign"
	k="5" />
    <hkern g1="Thorn"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="Thorn"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Eth,Dcroat"
	k="90" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Tbar"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="copyright,registered"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="seven"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="V"
	g2="backslash"
	k="10" />
    <hkern g1="V"
	g2="periodcentered,bullet"
	k="-10" />
    <hkern g1="V"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="V"
	g2="hbar"
	k="20" />
    <hkern g1="V"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="34" />
    <hkern g1="V"
	g2="j"
	k="60" />
    <hkern g1="V"
	g2="braceleft"
	k="41" />
    <hkern g1="V"
	g2="parenleft"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="five"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="34" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="zero"
	k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="underscore"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceright"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright"
	k="-28" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Hbar"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="two"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bracketright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hbar"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceleft"
	k="40" />
    <hkern g1="X"
	g2="eth"
	k="20" />
    <hkern g1="X"
	g2="five"
	k="34" />
    <hkern g1="X"
	g2="four"
	k="60" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="41" />
    <hkern g1="X"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="X"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="X"
	g2="nine"
	k="40" />
    <hkern g1="X"
	g2="numbersign"
	k="30" />
    <hkern g1="X"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="X"
	g2="one"
	k="10" />
    <hkern g1="X"
	g2="paragraph"
	k="30" />
    <hkern g1="X"
	g2="periodcentered,bullet"
	k="40" />
    <hkern g1="X"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="X"
	g2="question"
	k="30" />
    <hkern g1="X"
	g2="questiondown"
	k="10" />
    <hkern g1="X"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-28" />
    <hkern g1="X"
	g2="section"
	k="80" />
    <hkern g1="X"
	g2="seven"
	k="20" />
    <hkern g1="X"
	g2="six"
	k="40" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="X"
	g2="three"
	k="40" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="60" />
    <hkern g1="X"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="X"
	g2="x"
	k="60" />
    <hkern g1="X"
	g2="zero"
	k="40" />
    <hkern g1="X"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="X"
	g2="underscore"
	k="30" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="X"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="50" />
    <hkern g1="X"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="X"
	g2="slash"
	k="30" />
    <hkern g1="X"
	g2="braceright"
	k="20" />
    <hkern g1="X"
	g2="parenright"
	k="40" />
    <hkern g1="X"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="X"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="X"
	g2="Hbar"
	k="40" />
    <hkern g1="X"
	g2="two"
	k="65" />
    <hkern g1="X"
	g2="hbar"
	k="50" />
    <hkern g1="X"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="X"
	g2="braceleft"
	k="50" />
    <hkern g1="X"
	g2="parenleft"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Eth,Dcroat"
	k="41" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Tbar"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="X"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ampersand"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk,degree,trademark"
	k="-28" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eight"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eth"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="five"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="four"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="nine"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="one"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="paragraph"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="question"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="section"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="seven"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="six"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="zero"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Eth,Dcroat"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Tbar"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="V"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="X"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="asterisk,degree,trademark"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="at"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="backslash"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="copyright,registered"
	k="65" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="eth"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="five"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="questiondown"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="13" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="underscore"
	k="98" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="88" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="colon,semicolon"
	k="98" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="85" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceright"
	k="105" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="exclam,exclamdown"
	k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="parenright"
	k="160" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="150" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Hbar"
	k="-35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="two"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hbar"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceleft"
	k="70" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="73" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="three"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="zero"
	k="65" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Hbar"
	k="90" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="braceright"
	k="28" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="bracketright"
	k="80" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="numbersign"
	k="35" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="parenright"
	k="35" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="quotedbl,quotesingle"
	k="35" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="yen"
	k="18" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="20" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="j"
	k="1" />
    <hkern g1="dcroat"
	g2="J"
	k="73" />
    <hkern g1="dcroat"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="dcroat"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="65" />
    <hkern g1="dcroat"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="dcroat"
	g2="at"
	k="90" />
    <hkern g1="dcroat"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="28" />
    <hkern g1="dcroat"
	g2="hyphen,endash,emdash"
	k="80" />
    <hkern g1="dcroat"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="35" />
    <hkern g1="dcroat"
	g2="periodcentered,bullet"
	k="35" />
    <hkern g1="dcroat"
	g2="section"
	k="35" />
    <hkern g1="dcroat"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="18" />
    <hkern g1="dcroat"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="dcroat"
	g2="copyright,registered"
	k="1" />
    <hkern g1="dcroat"
	g2="eight"
	k="10" />
    <hkern g1="dcroat"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="dcroat"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="5" />
    <hkern g1="dcroat"
	g2="three"
	k="20" />
    <hkern g1="dcroat"
	g2="zero"
	k="8" />
    <hkern g1="dcroat"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="dcroat"
	g2="Hbar"
	k="20" />
    <hkern g1="dcroat"
	g2="braceright"
	k="3" />
    <hkern g1="dcroat"
	g2="bracketright"
	k="30" />
    <hkern g1="dcroat"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="dcroat"
	g2="hbar"
	k="8" />
    <hkern g1="eth"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="73" />
    <hkern g1="eth"
	g2="X"
	k="30" />
    <hkern g1="eth"
	g2="ampersand"
	k="65" />
    <hkern g1="eth"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="eth"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="28" />
    <hkern g1="eth"
	g2="questiondown"
	k="80" />
    <hkern g1="eth"
	g2="slash"
	k="35" />
    <hkern g1="eth"
	g2="two"
	k="35" />
    <hkern g1="eth"
	g2="underscore"
	k="35" />
    <hkern g1="eth"
	g2="x"
	k="18" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="eth"
	g2="four"
	k="1" />
    <hkern g1="eth"
	g2="six"
	k="10" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="eth"
	g2="J"
	k="5" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="eth"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="8" />
    <hkern g1="eth"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="-10" />
    <hkern g1="eth"
	g2="at"
	k="20" />
    <hkern g1="eth"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="3" />
    <hkern g1="eth"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="eth"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="eth"
	g2="nine"
	k="8" />
    <hkern g1="f,f_f"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="f,f_f"
	g2="T,uni0162,Tcaron,uni021A"
	k="28" />
    <hkern g1="f,f_f"
	g2="Tbar"
	k="80" />
    <hkern g1="f,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="f,f_f"
	g2="asterisk,degree,trademark"
	k="35" />
    <hkern g1="f,f_f"
	g2="backslash"
	k="35" />
    <hkern g1="f,f_f"
	g2="eth"
	k="18" />
    <hkern g1="f,f_f"
	g2="five"
	k="20" />
    <hkern g1="f,f_f"
	g2="one"
	k="1" />
    <hkern g1="f,f_f"
	g2="question"
	k="10" />
    <hkern g1="f,f_f"
	g2="seven"
	k="10" />
    <hkern g1="f,f_f"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="5" />
    <hkern g1="f,f_f"
	g2="X"
	k="20" />
    <hkern g1="f,f_f"
	g2="ampersand"
	k="8" />
    <hkern g1="f,f_f"
	g2="colon,semicolon"
	k="-10" />
    <hkern g1="f,f_f"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="f,f_f"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="3" />
    <hkern g1="f,f_f"
	g2="questiondown"
	k="30" />
    <hkern g1="f,f_f"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="f,f_f"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="8" />
    <hkern g1="f,f_f"
	g2="yen"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="20" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="T,uni0162,Tcaron,uni021A"
	k="3" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="Tbar"
	k="30" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="V"
	k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="8" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="braceright"
	k="3" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="bracketright"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="x"
	k="-10" />
    <hkern g1="k,uni0137"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="3" />
    <hkern g1="k,uni0137"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="Hbar"
	k="10" />
    <hkern g1="k,uni0137"
	g2="braceright"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="eth"
	k="-10" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="3" />
    <hkern g1="dcaron,lcaron"
	g2="questiondown"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="at"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="T,uni0162,Tcaron,uni021A"
	k="3" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="Tbar"
	k="-20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="exclam,exclamdown"
	k="45" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="hbar"
	k="30" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="numbersign"
	k="35" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="parenright"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="quotedbl,quotesingle"
	k="55" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="yen"
	k="20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="45" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="nine"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="35" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="section"
	k="55" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="three"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="zero"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="bracketright"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="hbar"
	k="25" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="numbersign"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="parenright"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="yen"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="j"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="45" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="35" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="two"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="underscore"
	k="55" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="x"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="nine"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="section"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="copyright,registered"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="eight"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="three"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="zero"
	k="15" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Hbar"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="braceright"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="V"
	k="45" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="backslash"
	k="55" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="eth"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="five"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="X"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="ampersand"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="questiondown"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="slash"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="two"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="underscore"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="x"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="four"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="six"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="15" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="at"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Tbar"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="eth"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="five"
	k="50" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="one"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="question"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="X"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="ampersand"
	k="15" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="three"
	k="3" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="exclam,exclamdown"
	k="-20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="numbersign"
	k="11" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="3" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="11" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="three"
	k="78" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Eth,Dcroat"
	k="28" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Hbar"
	k="-21" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="braceright"
	k="13" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="bracketright"
	k="25" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="hbar"
	k="45" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="parenright"
	k="13" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="quotedbl,quotesingle"
	k="33" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="yen"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="58" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="j"
	k="20" />
    <hkern g1="x"
	g2="X"
	k="3" />
    <hkern g1="x"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="x"
	g2="slash"
	k="11" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="78" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="28" />
    <hkern g1="x"
	g2="at"
	k="-21" />
    <hkern g1="x"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="13" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="x"
	g2="nine"
	k="45" />
    <hkern g1="x"
	g2="periodcentered,bullet"
	k="13" />
    <hkern g1="x"
	g2="section"
	k="33" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="58" />
    <hkern g1="x"
	g2="copyright,registered"
	k="20" />
    <hkern g1="x"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="3" />
    <hkern g1="x"
	g2="three"
	k="13" />
    <hkern g1="x"
	g2="zero"
	k="5" />
    <hkern g1="x"
	g2="Eth,Dcroat"
	k="13" />
    <hkern g1="x"
	g2="Hbar"
	k="10" />
    <hkern g1="x"
	g2="braceright"
	k="-10" />
    <hkern g1="x"
	g2="bracketright"
	k="31" />
    <hkern g1="x"
	g2="exclam,exclamdown"
	k="5" />
    <hkern g1="x"
	g2="hbar"
	k="3" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="seven"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="X"
	k="78" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="colon,semicolon"
	k="28" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-21" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="13" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="questiondown"
	k="25" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="45" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="two"
	k="13" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="underscore"
	k="33" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="x"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="58" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="four"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="J"
	k="3" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="13" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="13" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="at"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="31" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="nine"
	k="3" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="j"
	k="25" />
    <hkern g1="tcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-21" />
    <hkern g1="tcaron"
	g2="T,uni0162,Tcaron,uni021A"
	k="13" />
    <hkern g1="tcaron"
	g2="Tbar"
	k="25" />
    <hkern g1="tcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="45" />
    <hkern g1="tcaron"
	g2="asterisk,degree,trademark"
	k="13" />
    <hkern g1="tcaron"
	g2="backslash"
	k="33" />
    <hkern g1="tcaron"
	g2="eth"
	k="20" />
    <hkern g1="tcaron"
	g2="five"
	k="58" />
    <hkern g1="tcaron"
	g2="one"
	k="20" />
    <hkern g1="tcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="3" />
    <hkern g1="tcaron"
	g2="X"
	k="13" />
    <hkern g1="tcaron"
	g2="ampersand"
	k="5" />
    <hkern g1="tcaron"
	g2="colon,semicolon"
	k="13" />
    <hkern g1="tcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="tcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="-10" />
    <hkern g1="tcaron"
	g2="questiondown"
	k="31" />
    <hkern g1="tcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="tcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="3" />
    <hkern g1="tcaron"
	g2="copyright,registered"
	k="25" />
    <hkern g1="tcaron"
	g2="eight"
	k="10" />
    <hkern g1="tcaron"
	g2="guillemotright,guilsinglright"
	k="23" />
    <hkern g1="tcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="13" />
    <hkern g1="tcaron"
	g2="three"
	k="20" />
    <hkern g1="tcaron"
	g2="bracketright"
	k="10" />
    <hkern g1="tcaron"
	g2="exclam,exclamdown"
	k="3" />
    <hkern g1="at"
	g2="J"
	k="40" />
    <hkern g1="at"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="at"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="at"
	g2="Tbar"
	k="-1" />
    <hkern g1="at"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="at"
	g2="V"
	k="35" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="at"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="at"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="at"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="70" />
    <hkern g1="at"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="copyright,registered"
	g2="J"
	k="10" />
    <hkern g1="copyright,registered"
	g2="four"
	k="10" />
    <hkern g1="copyright,registered"
	g2="one"
	k="10" />
    <hkern g1="copyright,registered"
	g2="seven"
	k="70" />
    <hkern g1="copyright,registered"
	g2="six"
	k="30" />
    <hkern g1="copyright,registered"
	g2="zero"
	k="60" />
    <hkern g1="copyright,registered"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="copyright,registered"
	g2="eth"
	k="30" />
    <hkern g1="copyright,registered"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="80" />
    <hkern g1="copyright,registered"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="florin"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="florin"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="florin"
	g2="z,zacute,zdotaccent,zcaron"
	k="70" />
    <hkern g1="florin"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="florin"
	g2="five"
	k="60" />
    <hkern g1="florin"
	g2="four"
	k="30" />
    <hkern g1="florin"
	g2="nine"
	k="30" />
    <hkern g1="florin"
	g2="one"
	k="80" />
    <hkern g1="florin"
	g2="seven"
	k="10" />
    <hkern g1="florin"
	g2="zero"
	k="20" />
    <hkern g1="florin"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="florin"
	g2="eth"
	k="15" />
    <hkern g1="florin"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="florin"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="8" />
    <hkern g1="paragraph"
	g2="V"
	k="10" />
    <hkern g1="paragraph"
	g2="X"
	k="10" />
    <hkern g1="paragraph"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="paragraph"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="paragraph"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="60" />
    <hkern g1="paragraph"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="30" />
    <hkern g1="paragraph"
	g2="x"
	k="30" />
    <hkern g1="paragraph"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="80" />
    <hkern g1="paragraph"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="paragraph"
	g2="five"
	k="20" />
    <hkern g1="paragraph"
	g2="four"
	k="20" />
    <hkern g1="paragraph"
	g2="nine"
	k="15" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="seven"
	k="8" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="70" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="V"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="X"
	k="80" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="x"
	k="15" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="section"
	g2="J"
	k="30" />
    <hkern g1="section"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="80" />
    <hkern g1="section"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="section"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="section"
	g2="V"
	k="20" />
    <hkern g1="section"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="section"
	g2="X"
	k="10" />
    <hkern g1="section"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="8" />
    <hkern g1="section"
	g2="zero"
	k="20" />
    <hkern g1="section"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="30" />
    <hkern g1="section"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="section"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="backslash"
	g2="Tbar"
	k="60" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="backslash"
	g2="V"
	k="20" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="backslash"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="backslash"
	g2="j"
	k="10" />
    <hkern g1="backslash"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="100" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="100" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="120" />
    <hkern g1="backslash"
	g2="hbar"
	k="80" />
    <hkern g1="backslash"
	g2="X"
	k="25" />
    <hkern g1="backslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="backslash"
	g2="x"
	k="-70" />
    <hkern g1="backslash"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="backslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="backslash"
	g2="Hbar"
	k="20" />
    <hkern g1="braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="80" />
    <hkern g1="braceleft"
	g2="J"
	k="25" />
    <hkern g1="braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="15" />
    <hkern g1="braceleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-70" />
    <hkern g1="braceleft"
	g2="eth"
	k="10" />
    <hkern g1="braceleft"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="braceleft"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="braceleft"
	g2="Eth,Dcroat"
	k="70" />
    <hkern g1="braceleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="braceleft"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-10" />
    <hkern g1="braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="braceright"
	g2="eth"
	k="-10" />
    <hkern g1="braceright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-10" />
    <hkern g1="braceright"
	g2="T,uni0162,Tcaron,uni021A"
	k="-10" />
    <hkern g1="braceright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-70" />
    <hkern g1="braceright"
	g2="j"
	k="-10" />
    <hkern g1="bracketleft"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="bracketleft"
	g2="j"
	k="10" />
    <hkern g1="bracketleft"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="bracketright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="bracketright"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="bracketright"
	g2="hbar"
	k="-10" />
    <hkern g1="bracketright"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-10" />
    <hkern g1="bracketright"
	g2="Hbar"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="eth"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="exclam,exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-30" />
    <hkern g1="exclam,exclamdown"
	g2="Hbar"
	k="-5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Eth,Dcroat"
	k="-8" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="-8" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="110" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="j"
	k="85" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="95" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="43" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="130" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="X"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="x"
	k="8" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="65" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="65" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Eth,Dcroat"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="X"
	k="30" />
    <hkern g1="numbersign"
	g2="J"
	k="30" />
    <hkern g1="numbersign"
	g2="Tbar"
	k="40" />
    <hkern g1="numbersign"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="numbersign"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="numbersign"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="numbersign"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="50" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="x"
	k="10" />
    <hkern g1="parenleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="30" />
    <hkern g1="parenleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="parenleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="parenleft"
	g2="V"
	k="10" />
    <hkern g1="parenleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="parenleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="parenleft"
	g2="hbar"
	k="30" />
    <hkern g1="parenleft"
	g2="X"
	k="20" />
    <hkern g1="parenleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="50" />
    <hkern g1="parenright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="parenright"
	g2="J"
	k="20" />
    <hkern g1="parenright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="50" />
    <hkern g1="parenright"
	g2="Tbar"
	k="30" />
    <hkern g1="parenright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="parenright"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="35" />
    <hkern g1="parenright"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="parenright"
	g2="z,zacute,zdotaccent,zcaron"
	k="60" />
    <hkern g1="parenright"
	g2="Hbar"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="Tbar"
	k="50" />
    <hkern g1="periodcentered,bullet"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="35" />
    <hkern g1="periodcentered,bullet"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="question"
	g2="eth"
	k="35" />
    <hkern g1="question"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="question"
	g2="j"
	k="20" />
    <hkern g1="question"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="question"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="30" />
    <hkern g1="question"
	g2="X"
	k="-10" />
    <hkern g1="question"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="questiondown"
	g2="J"
	k="-10" />
    <hkern g1="questiondown"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="questiondown"
	g2="j"
	k="5" />
    <hkern g1="questiondown"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="12" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="Eth,Dcroat"
	k="-70" />
    <hkern g1="quotedbl,quotesingle"
	g2="V"
	k="-10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="slash"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="slash"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="slash"
	g2="j"
	k="60" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="slash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="slash"
	g2="hbar"
	k="40" />
    <hkern g1="underscore"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="40" />
    <hkern g1="underscore"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="20" />
    <hkern g1="underscore"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="80" />
    <hkern g1="underscore"
	g2="j"
	k="40" />
    <hkern g1="underscore"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="underscore"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="underscore"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="35" />
    <hkern g1="underscore"
	g2="hbar"
	k="30" />
    <hkern g1="underscore"
	g2="Hbar"
	k="25" />
  </font>
</defs></svg>
