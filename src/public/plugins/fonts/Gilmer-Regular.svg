<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Fri Dec  7 19:13:50 2018
 By deploy user
Copyright &#194;&#169; 2018 by Piotr &#197;&#129;apa. All rights reserved.
</metadata>
<defs>
<font id="Gilmer-Regular" horiz-adv-x="607" >
  <font-face 
    font-family="Gilmer"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 5 0 0 0 0 0 0 0"
    ascent="790"
    descent="-210"
    x-height="499"
    cap-height="700"
    bbox="-317 -250 1381 969"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-U+FB02"
  />
<missing-glyph horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="581" 
d="M517 499v-499h-77v429h-233v-429h-76v429h-105v70h105v66q0 85 45 131.5t133 46.5q93 0 159 -52l-19 -65q-64 48 -134 48q-53 0 -80.5 -28.5t-27.5 -82.5v-64h310z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="592" 
d="M310 743q119 0 207 -64v-679h-77v641q-58 33 -125 33q-53 0 -80.5 -28.5t-27.5 -82.5v-64h152v-70h-152v-429h-76v429h-105v70h105v66q0 85 45 131.5t134 46.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="709" 
d="M517 563v-64h179v-70h-179v-429h-77v429h-233v-429h-76v429h-105v70h105v66q0 85 40 131.5t117 46.5q59 0 101 -21l-20 -65q-33 17 -74 17q-88 0 -88 -111v-64h233v66q0 85 40 131.5t117 46.5q59 0 101 -21l-19 -65q-34 17 -75 17q-87 0 -87 -111z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="890" 
d="M826 499v-499h-77v429h-232v-429h-77v429h-233v-429h-76v429h-105v70h105v66q0 85 42 131.5t125 46.5q59 0 101 -21l-20 -65q-33 17 -74 17q-48 0 -73 -28.5t-25 -82.5v-64h233v66q0 85 45.5 131.5t133.5 46.5q92 0 159 -52l-20 -65q-64 48 -134 48q-107 0 -107 -111v-64
h309z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="901" 
d="M619 743q119 0 207 -64v-679h-77v641q-59 33 -125 33q-107 0 -107 -111v-64h151v-70h-151v-429h-77v429h-233v-429h-76v429h-105v70h105v66q0 85 40.5 131.5t119.5 46.5q58 0 100 -21l-19 -65q-34 17 -74 17q-91 0 -91 -111v-64h233v66q0 85 45.5 131.5t133.5 46.5z" />
    <glyph glyph-name=".notdef" horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="259" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="249" 
d="M95 193l-13 507h88l-12 -507h-63zM125 -7q-24 0 -40 16t-16 40q0 23 16 38.5t40 15.5t40 -15.5t16 -38.5q0 -24 -16 -40t-40 -16z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="338" 
d="M66 494l-11 206h82l-10 -206h-61zM212 494l-11 206h82l-10 -206h-61z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="722" 
d="M673 414h-145l-25 -157h146l-10 -65h-146l-30 -192h-70l30 192h-170l-30 -192h-70l30 192h-144l10 65h144l25 157h-145l10 64h145l31 197h70l-31 -197h170l31 197h70l-31 -197h145zM433 257l25 157h-170l-25 -157h170z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="646" 
d="M603 194q0 -83 -70.5 -143t-181.5 -64v-132h-68v136q-94 14 -162 71.5t-93 134.5l76 23q24 -70 87 -115.5t149 -45.5q81 0 132.5 39.5t51.5 91.5q0 41 -29 68.5t-100 49.5l-174 56q-155 50 -155 164q0 73 61.5 126.5t155.5 58.5v132h68v-135q85 -12 145.5 -57t88.5 -110
l-74 -23q-27 56 -82 88.5t-131 32.5q-66 0 -109.5 -33t-43.5 -78q0 -35 25.5 -58t84.5 -42l170 -54q90 -29 134 -69t44 -113z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="999" 
d="M241 333q-80 0 -135.5 54t-55.5 133t55.5 133t135.5 54q81 0 136.5 -54t55.5 -133t-55.5 -133t-136.5 -54zM236 0l454 700h72l-453 -700h-73zM241 396q54 0 90 36.5t36 87.5q0 52 -36 88t-90 36t-89.5 -36t-35.5 -88q0 -51 36 -87.5t89 -36.5zM758 -8q-80 0 -135.5 54.5
t-55.5 132.5q0 79 55.5 133t135.5 54q81 0 136 -54t55 -133q0 -78 -55 -132.5t-136 -54.5zM758 55q53 0 89 36.5t36 87.5q0 52 -35.5 88t-89.5 36t-89.5 -36t-35.5 -88q0 -51 35.5 -87.5t89.5 -36.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="694" 
d="M679 0h-98l-82 86q-97 -99 -224 -99q-87 0 -148.5 55t-61.5 141q0 62 36 116t107 91q-33 38 -50 70.5t-17 74.5q0 82 56 130t141 48q78 0 131 -43.5t68 -107.5l-70 -20q-14 47 -47 74t-82 27q-53 0 -87 -30t-34 -80q0 -48 59 -111l217 -228q38 63 52 130l69 -18
q-16 -87 -70 -165zM281 58q95 0 169 79l-195 204q-113 -62 -113 -155q0 -52 39 -90t100 -38z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="192" 
d="M66 494l-11 206h82l-10 -206h-61z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="281" 
d="M252 -137h-77q-116 194 -115 440q2 249 115 440h77q-115 -201 -115 -440q0 -241 115 -440z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="281" 
d="M29 -137q115 199 115 440q0 239 -115 440h77q113 -191 115 -440q1 -246 -115 -440h-77z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="412" 
d="M357 609l-96 -52l96 -52l-31 -52l-93 57l3 -110h-60l3 110l-93 -57l-31 52l96 52l-96 52l31 52l93 -57l-3 109h60l-3 -109l93 57z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="553" 
d="M483 383v-65h-173v-174h-66v174h-174v65h174v174h66v-174h173z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="241" 
d="M55 -137l43 240h88l-65 -240h-66z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="495" 
d="M70 277v66h355v-66h-355z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="222" 
d="M111 -7q-24 0 -40 16t-16 40q0 23 16 38.5t40 15.5t40 -15.5t16 -38.5q0 -24 -16 -40t-40 -16z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="546" 
d="M40 -91l396 881h70l-396 -881h-70z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="636" 
d="M318 -13q-130 0 -201.5 101t-71.5 262q0 162 71.5 262.5t201.5 100.5t201.5 -100.5t71.5 -262.5q0 -161 -71.5 -262t-201.5 -101zM318 61q94 0 144 78t50 211t-50 211t-144 78q-93 0 -143 -78t-50 -211t50 -211t143 -78z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="385" 
d="M231 700h67v-700h-80v608l-176 -85l-19 69z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="573" 
d="M161 73h361v-73h-480v59l279 269q57 54 83 91.5t26 80.5q0 65 -40.5 102.5t-110.5 37.5q-63 0 -106 -37t-57 -100l-72 20q12 78 76 134t158 56q103 0 167 -56.5t64 -154.5q0 -61 -32.5 -110t-100.5 -113z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="584" 
d="M324 425q98 0 157.5 -59t59.5 -153q0 -95 -70 -161.5t-175 -66.5q-110 0 -177 58t-85 140l73 20q13 -63 62.5 -104.5t124.5 -41.5q78 0 123.5 45.5t45.5 110.5t-41 107t-109 42q-51 0 -91 -27l-15 53l202 240h-355v72h459v-59z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="592" 
d="M568 227v-72h-96v-155h-79v155h-363v60l261 485h87l-255 -473h270v177h79v-177h96z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="583" 
d="M302 446q105 0 171.5 -63.5t66.5 -164.5q0 -98 -69.5 -164.5t-175.5 -66.5q-102 0 -167 53.5t-87 133.5l73 19q18 -60 63.5 -97t114.5 -37q77 0 124 46t47 114q0 70 -45 114.5t-126 44.5q-93 0 -164 -58l-60 19l31 361h405v-72h-335l-19 -224q63 42 152 42z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="567" 
d="M292 462q107 -2 174.5 -69.5t67.5 -167.5q0 -101 -70 -169.5t-180 -68.5t-180.5 68.5t-70.5 169.5q0 74 57 161l210 314h90l-167 -250q30 12 69 12zM284 60q76 0 124 47t48 118t-48 118.5t-125 47.5q-76 0 -124 -47.5t-48 -118.5t48 -118t125 -47z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="524" 
d="M19 700h481v-60l-285 -640h-85l280 627h-391v73z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="603" 
d="M428 379q61 -24 95.5 -71.5t34.5 -110.5q0 -91 -71 -150.5t-185 -59.5q-115 0 -186 59.5t-71 150.5q0 63 34.5 110.5t95.5 71.5q-47 23 -73.5 62t-26.5 89q0 77 62.5 130t164.5 53q101 0 163.5 -53t62.5 -130q0 -50 -26.5 -89t-73.5 -62zM302 645q-70 0 -109.5 -33.5
t-39.5 -85.5t39.5 -85.5t109.5 -33.5q68 0 108 33.5t40 85.5t-40 85.5t-108 33.5zM302 57q79 0 127.5 41t48.5 102q0 62 -48 102t-128 40q-81 0 -129.5 -40t-48.5 -102t48.5 -102.5t129.5 -40.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="567" 
d="M283 713q110 0 180.5 -68.5t70.5 -169.5q0 -74 -57 -161l-210 -314h-90l167 250q-31 -12 -69 -11q-107 1 -174.5 68.5t-67.5 167.5q0 101 70 169.5t180 68.5zM284 310q76 0 124 47.5t48 117.5q0 71 -48 118t-125 47q-76 0 -124 -47t-48 -118t48 -118t125 -47z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="222" 
d="M111 380q-24 0 -40 16t-16 39t16 39t40 16t40 -16t16 -39t-16 -39t-40 -16zM111 -7q-24 0 -40 16t-16 40q0 23 16 38.5t40 15.5t40 -15.5t16 -38.5q0 -24 -16 -40t-40 -16z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="245" 
d="M134 381q-24 0 -40 15.5t-16 39.5q0 23 16 38.5t40 15.5t40 -15.5t16 -38.5q0 -24 -16 -39.5t-40 -15.5zM55 -137l43 240h88l-65 -240h-66z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="548" 
d="M473 551v-72l-332 -128l332 -129v-72l-413 164v73z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="554" 
d="M70 412v65h413v-65h-413zM72 224v65h412v-65h-412z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="548" 
d="M75 551l413 -164v-73l-413 -164v72l332 129l-332 128v72z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="561" 
d="M230 193q0 50 19.5 91t47 67t55 49.5t47 52t19.5 61.5q0 56 -37.5 91.5t-101.5 35.5t-106 -37t-57 -100l-71 21q17 81 77 134.5t158 53.5q95 0 156 -55t61 -141q0 -44 -20 -80t-48.5 -61l-56.5 -49.5t-48 -58.5t-20 -75h-74zM266 -7q-24 0 -40 16t-16 40q0 23 16 38.5
t40 15.5t40 -15.5t16 -38.5q0 -24 -16 -40t-40 -16z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1002" 
d="M493 -177q-192 0 -317.5 125t-125.5 308q0 123 61.5 225t166.5 160.5t230 58.5q193 0 318.5 -125t125.5 -315q0 -111 -54.5 -177t-133.5 -66q-51 0 -80 27t-38 73q-57 -86 -168 -86q-91 0 -153 64t-62 160q0 97 61.5 161t154.5 64q107 0 166 -85v73h69v-312q0 -83 60 -83
q45 0 80 50.5t35 136.5q0 163 -105.5 271.5t-275.5 108.5q-164 0 -279.5 -111t-115.5 -273q0 -159 108.5 -266t272.5 -107q133 0 210 60l17 -58q-87 -62 -228 -62zM485 95q69 0 115.5 45.5t46.5 114.5q0 70 -45.5 115t-116.5 45q-66 0 -109 -45t-43 -115q0 -68 43.5 -114
t108.5 -46z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="695" 
d="M599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260h293l-146 360z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="685" 
d="M513 366q59 -20 92 -65.5t33 -106.5q0 -84 -60 -139t-163 -55h-340v700h317q94 0 155 -51t62 -131q1 -107 -96 -152zM389 629h-235v-237h244q60 0 96.5 34.5t35.5 84.5q0 52 -38.5 85.5t-102.5 32.5zM412 71q67 0 107 37.5t40 91.5q0 51 -39 89t-106 38h-260v-256h258z
" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="780" 
d="M405 -13q-155 0 -259 105t-104 260q0 153 104 257t258 104q120 0 208 -59.5t121 -151.5l-78 -19q-27 68 -93.5 111.5t-157.5 43.5q-124 0 -203 -83t-79 -202q0 -122 78.5 -206.5t203.5 -84.5q91 0 157.5 43t93.5 112l76 -20q-34 -92 -121 -151t-205 -59z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="727" 
d="M341 700q159 0 251.5 -98t92.5 -252t-92.5 -252t-251.5 -98h-266v700h266zM340 73q125 0 195 78t70 201q0 120 -70.5 198t-194.5 78h-186v-555h186z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="623" 
d="M568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="579" 
d="M560 627h-405v-245h354v-71h-354v-311h-80v700h485v-73z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="799" 
d="M757 371v-43q-1 -158 -99 -249.5t-250 -91.5q-156 0 -261 104.5t-105 260.5q0 153 104 257t258 104q118 0 205 -57.5t123 -149.5l-75 -20q-30 69 -95.5 110.5t-156.5 41.5q-125 0 -204 -83t-79 -202q0 -122 79.5 -207t209.5 -85q114 0 187 65.5t80 175.5h-275v69h354z
" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="718" 
d="M563 700h80v-700h-80v321h-408v-321h-80v700h80v-307h408v307z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="230" 
d="M75 0v700h80v-700h-80z" />
    <glyph glyph-name="J" unicode="J" 
d="M541 700v-469q0 -111 -68.5 -177.5t-180.5 -66.5q-96 0 -162.5 50.5t-94.5 134.5l73 22q44 -135 183 -135q80 0 125.5 48.5t45.5 124.5v397h-376v71h455z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="678" 
d="M560 0l-270 330l-135 -147v-183h-80v700h80v-410l375 410h102l-289 -314l315 -386h-98z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="571" 
d="M155 73h388v-73h-468v700h80v-627z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="823" 
d="M672 700h76v-700h-79v563l-257 -364l-257 364v-563h-80v700h76l261 -373z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="723" 
d="M569 700h79v-700h-69l-424 560v-560h-80v700h69l425 -561v561z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="806" 
d="M403 -13q-154 0 -257.5 105t-103.5 260q0 153 103.5 257t257.5 104t257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105zM403 61q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5t-202.5 -83.5t-78.5 -203.5q0 -123 78.5 -207t202.5 -84z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="643" 
d="M384 700q105 0 167 -69t62 -157q0 -90 -62.5 -157.5t-170.5 -67.5h-226v-249h-79v700h309zM378 321q72 0 115 45.5t43 107.5t-43.5 107.5t-110.5 45.5h-228v-306h224z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="804" 
d="M761 352q0 -151 -99 -255l82 -97h-85l-46 54q-94 -67 -211 -67q-152 0 -256 105t-104 260q0 153 104 257t256 104t255.5 -104t103.5 -257zM402 61q92 0 162 49l-166 193h92l124 -148q69 82 69 197q0 121 -78.5 204t-202.5 83t-203 -83t-79 -204q0 -123 79 -207t203 -84z
" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="676" 
d="M542 0l-183 275h-205v-275h-79v700h324q102 0 162 -65t60 -148q0 -74 -46.5 -133.5t-128.5 -74.5l188 -279h-92zM154 628v-282h242q67 0 107.5 41.5t40.5 99.5q0 57 -40.5 99t-106.5 42h-243z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="661" 
d="M344 -13q-114 0 -197 61t-112 149l76 23q24 -70 87.5 -115.5t149.5 -45.5q81 0 132 39.5t51 91.5q0 41 -29 68.5t-99 49.5l-175 56q-155 50 -155 164q0 76 67 130.5t167 54.5q104 0 178.5 -48t106.5 -122l-74 -23q-26 56 -81 88.5t-131 32.5q-67 0 -110 -33t-43 -78
q0 -35 25 -58t84 -42l170 -54q90 -29 134 -69t44 -113q0 -86 -75 -146.5t-191 -60.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="627" 
d="M612 700v-73h-258v-627h-80v627h-259v73h597z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="708" 
d="M562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="679" 
d="M578 700h86l-282 -700h-85l-282 700h86l239 -601z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="986" 
d="M888 700h83l-220 -700h-83l-175 570l-175 -570h-83l-220 700h83l179 -587l179 587h73l179 -587z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="682" 
d="M662 0h-97l-224 296l-224 -296h-97l266 353l-260 347h97l218 -290l218 290h97l-260 -347z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="652" 
d="M551 700h90l-275 -432v-268h-80v268l-275 432h91l224 -357z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="625" 
d="M152 72h436v-72h-547v53l427 575h-416v72h525v-54z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="332" 
d="M297 673h-146v-741h146v-69h-222v880h222v-70z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="546" 
d="M506 -91h-70l-396 881h70z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="332" 
d="M257 743v-880h-222v69h146v741h-146v70h222z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="531" 
d="M490 418h-81l-143 245l-144 -245h-81l185 312h79z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="590" 
d="M43 -131v62h505v-62h-505z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="168" 
d="M168 578h-71l-97 136h89z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="644" 
d="M295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="644" 
d="M349 511q108 0 180 -74.5t72 -186.5q0 -113 -72 -188t-180 -75q-66 0 -116.5 27.5t-80.5 76.5v-91h-77v730h77v-321q64 102 197 102zM338 57q82 0 134.5 54.5t52.5 137.5q0 84 -52.5 138t-134.5 54q-78 0 -133 -55t-55 -137t55 -137t133 -55z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="577" 
d="M303 -13q-112 0 -186 75.5t-74 188.5q0 111 74 185.5t186 74.5q89 0 151.5 -46.5t79.5 -104.5l-72 -21q-13 41 -57 72t-102 31q-82 0 -133 -56.5t-51 -136.5q0 -79 51.5 -135.5t132.5 -56.5q59 0 102.5 29.5t56.5 70.5l72 -19q-17 -59 -79 -105t-152 -46z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="644" 
d="M492 730h77v-730h-77v91q-65 -104 -197 -104q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v321zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="590" 
d="M306 -13q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-28 -61 -88 -102.5t-148 -41.5zM119 282l352 1q-5 72 -51 116t-123 44q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="400" 
d="M207 563v-64h179v-70h-179v-429h-76v429h-105v70h105v66q0 85 40 131.5t117 46.5q59 0 101 -21l-20 -65q-33 17 -74 17q-88 0 -88 -111z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="642" 
d="M491 499h76v-482q0 -104 -69 -172t-178 -68q-164 0 -237 131l68 25q53 -89 170 -89q78 0 123 49t47 124v85q-65 -96 -196 -96q-109 0 -180.5 72.5t-71.5 180.5q0 109 71.5 180.5t180.5 71.5q132 0 196 -95v83zM306 76q78 0 132 52.5t54 129.5q0 78 -54 130.5t-132 52.5
q-82 0 -134.5 -51.5t-52.5 -131.5q0 -79 52.5 -130.5t134.5 -51.5z" />
    <glyph glyph-name="h" unicode="h" 
d="M337 511q93 0 146.5 -58t53.5 -160v-293h-77v282q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v730h77v-319q28 45 75.5 72.5t109.5 27.5z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="235" 
d="M117 592q-23 0 -38 15t-15 38q0 21 15.5 36t37.5 15t37.5 -15t15.5 -36q0 -23 -15 -38t-38 -15zM79 0v499h77v-499h-77z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="232" 
d="M115 592q-23 0 -38 15t-15 38q0 21 15.5 36t37.5 15t37.5 -15t15.5 -36q0 -23 -15 -38t-38 -15zM76 -51v550h77v-546q0 -83 -37.5 -129.5t-111.5 -46.5q-40 0 -82 20l19 64q26 -16 57 -16q78 0 78 104z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="526" 
d="M420 0l-192 230l-76 -79v-151h-77v730h77v-480l238 249h95l-207 -217l233 -282h-91z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="227" 
d="M75 0v730h77v-730h-77z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="921" 
d="M657 511q90 0 142 -55.5t52 -155.5v-300h-77v288q0 72 -33 111.5t-97 39.5t-103.5 -47t-39.5 -125v-267h-77v288q0 71 -31.5 111t-91.5 40q-66 0 -107.5 -50.5t-41.5 -133.5v-255h-77v499h77v-82q56 94 166 94q114 0 160 -96q55 96 179 96z" />
    <glyph glyph-name="n" unicode="n" 
d="M337 511q93 0 146.5 -58t53.5 -160v-293h-77v282q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v499h77v-88q28 45 75.5 72.5t109.5 27.5z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="613" 
d="M307 -13q-113 0 -188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5t188 -74.5t75 -186.5t-75 -187.5t-188 -75.5zM307 58q80 0 133.5 54.5t53.5 136.5t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137t54 -136.5t135 -54.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="644" 
d="M349 511q108 0 180 -74.5t72 -186.5q0 -113 -72 -188t-180 -75q-66 0 -116.5 27.5t-80.5 76.5v-301h-77v709h77v-90q64 102 197 102zM338 57q82 0 134.5 54.5t52.5 137.5q0 84 -52.5 138t-134.5 54q-78 0 -133 -55t-55 -137t55 -137t133 -55z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="644" 
d="M492 499h77v-709h-77v301q-65 -104 -197 -104q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="354" 
d="M152 413q23 51 69 76t110 16v-72q-84 11 -131.5 -37.5t-47.5 -162.5v-233h-77v499h77v-86z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="510" 
d="M258 -13q-83 0 -146.5 43.5t-82.5 107.5l72 20q14 -45 57.5 -74.5t101.5 -29.5q55 0 90 24t35 58q0 29 -17 44.5t-56 27.5l-142 42q-116 34 -116 121q0 57 53 98.5t130 41.5t132.5 -35t78.5 -89l-70 -21q-14 34 -52 57t-90 23q-47 0 -76.5 -23t-29.5 -51q0 -40 67 -60
l142 -42q57 -17 89.5 -46.5t32.5 -83.5q0 -66 -57.5 -109.5t-145.5 -43.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="409" 
d="M349 81l21 -66q-51 -22 -104 -22q-73 0 -109.5 42.5t-36.5 116.5v277h-99v70h99v172h77v-172h171v-70h-171v-277q0 -88 81 -88q37 0 71 17z" />
    <glyph glyph-name="u" unicode="u" 
d="M455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="541" 
d="M447 499h83l-217 -499h-85l-217 499h83l177 -415z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="769" 
d="M677 499h80l-171 -499h-75l-126 384l-127 -384h-75l-172 499h81l131 -401l128 401h67l128 -401z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="523" 
d="M512 0h-92l-158 200l-159 -200h-92l199 252l-189 247h90l151 -194l150 194h90l-189 -247z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="546" 
d="M453 499h82l-307 -709h-81l95 220l-231 489h85l185 -403z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="516" 
d="M150 67h322v-67h-427v51l314 381h-308v67h412v-53z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="377" 
d="M237 424q0 -99 -75 -121q75 -23 75 -121v-168q0 -88 76 -88h29v-68h-44q-135 0 -135 147v177q0 38 -24 62t-63 24h-21v70h21q39 0 63 23.5t24 62.5v176q0 148 135 148h44v-69h-29q-76 0 -76 -88v-167z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="210" 
d="M70 -125v930h70v-930h-70z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="377" 
d="M301 338h21v-70h-21q-39 0 -63 -24t-24 -62v-177q0 -147 -135 -147h-44v68h29q76 0 76 88v168q0 98 75 121q-75 22 -75 121v167q0 88 -76 88h-29v69h44q135 0 135 -148v-176q0 -39 24 -62.5t63 -23.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="589" 
d="M129 284l-69 20q0 52 29.5 86.5t90.5 34.5q39 0 80.5 -20.5t77.5 -40.5t62 -20q60 0 60 74l69 -20q0 -52 -29 -87.5t-89 -35.5q-40 0 -81.5 20.5t-77.5 40.5t-62 20q-61 0 -61 -72z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="222" 
d="M111 505q24 0 40 -15.5t16 -39.5q0 -23 -16 -38.5t-40 -15.5t-40 15.5t-16 38.5q0 24 16 39.5t40 15.5zM141 306l12 -507h-88l13 507h63z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="575" 
d="M305 157q59 0 102.5 29.5t56.5 70.5l72 -20q-15 -53 -68 -96.5t-131 -51.5v-105h-68v105q-98 13 -161 85.5t-63 175.5q0 102 62.5 174t161.5 85v106h68v-106q77 -8 130.5 -52.5t68.5 -96.5l-72 -21q-13 40 -57 71t-102 31q-81 0 -132.5 -56t-51.5 -136q0 -79 51.5 -135.5
t132.5 -56.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="646" 
d="M221 74h387v-74h-560v74h95v217h-77v66h77v159q0 95 55 147t146 52q87 0 143.5 -49t70.5 -120l-70 -20q-33 117 -145 117q-58 0 -90 -34.5t-32 -96.5v-155h255v-66h-255v-217z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="616" 
d="M542 320q0 -79 -46 -141l80 -80l-47 -47l-78 80q-63 -47 -143 -47q-81 0 -142 47l-79 -80l-47 47l80 80q-45 63 -45 141q0 80 46 142l-81 79l47 47l80 -80q60 47 141 47t142 -47l79 80l47 -47l-80 -79q46 -62 46 -142zM144 320q0 -70 47 -117t117 -47t117.5 47t48.5 117
q-1 70 -48.5 117.5t-117.5 47.5t-117 -47.5t-47 -117.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="670" 
d="M650 700l-242 -380h176v-53h-209v-67h209v-53h-209v-147h-80v147h-206v53h206v67h-206v53h173l-242 380h90l225 -358l225 358h90z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="210" 
d="M70 445v360h70v-360h-70zM70 -125v360h70v-360h-70z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="553" 
d="M426 201q57 -37 57 -100q0 -65 -57 -110t-146 -45q-80 0 -143.5 43t-81.5 106l71 21q15 -46 58 -75t101 -29q53 1 87.5 27t34.5 58q0 27 -16.5 41t-56.5 27l-147 48q-123 38 -123 134q0 38 24.5 71t59.5 52q-56 37 -56 100q0 59 53 101t132 42q71 0 122.5 -33t72.5 -86
l-70 -20q-14 32 -46.5 52.5t-79.5 20.5q-53 1 -81.5 -24t-28.5 -51q0 -44 68 -70l143 -48q59 -20 90 -49.5t31 -83.5q0 -69 -72 -120zM222 272l138 -43q62 38 62 89q0 29 -17 45t-56 29l-136 46q-33 -14 -53 -38t-20 -49q0 -56 82 -79z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="279" 
d="M47 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM232 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="755" 
d="M378 16q-143 0 -240 97t-97 239q0 140 97.5 236t239.5 96q143 0 239.5 -95.5t96.5 -236.5q0 -143 -96.5 -239.5t-239.5 -96.5zM378 69q120 0 201 81t81 202q0 120 -81 199.5t-201 79.5t-201.5 -79.5t-81.5 -199.5q0 -121 81.5 -202t201.5 -81zM382 178q-74 0 -123.5 49.5
t-49.5 123.5q0 72 49.5 121t122.5 49q61 0 103 -30.5t56 -74.5l-56 -13q-9 28 -37 46.5t-65 18.5q-52 0 -84 -34t-32 -83q0 -50 32 -84.5t84 -34.5q38 0 65.5 18.5t36.5 46.5l55 -15q-13 -43 -55 -73.5t-102 -30.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="471" 
d="M249 495l-100 -175l100 -175h-78l-101 175l101 175h78zM401 495l-100 -175l100 -175h-77l-102 175l102 175h77z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="568" 
d="M498 439v-210h-69v145h-359v65h428z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="755" 
d="M378 684q143 0 239.5 -96t96.5 -238t-96.5 -238t-239.5 -96t-240 96t-97 238t97 238t240 96zM378 67q121 0 201.5 81t80.5 202t-80.5 202t-201.5 81t-202 -81t-81 -202t81 -202t202 -81zM523 410q0 -33 -20 -61t-56 -39l79 -125h-65l-74 120h-79v-120h-56v332h160
q50 0 80.5 -32.5t30.5 -74.5zM308 465v-110h100q26 0 42.5 16t16.5 39t-16 39t-42 16h-101z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="263" 
d="M0 592v60h263v-60h-263z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="401" 
d="M201 433q-67 0 -113.5 45.5t-46.5 110.5q0 66 46.5 111t113.5 45t113 -45t46 -111q0 -65 -46 -110.5t-113 -45.5zM201 492q41 0 69 28.5t28 68.5q1 41 -27 69t-70 28t-70 -28t-28 -69q0 -40 28 -68.5t70 -28.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="553" 
d="M310 449h173v-65h-173v-150h-66v150h-174v65h174v149h66v-149zM70 103v65h413v-65h-413z" />
    <glyph glyph-name="two.sups" unicode="&#xb2;" horiz-adv-x="339" 
d="M119 506h193v-56h-289v43l160 153q30 29 43 48t13 41q0 31 -19.5 49.5t-54.5 18.5q-67 0 -83 -75l-59 16q7 48 45.5 82t95.5 34q63 0 101.5 -33t38.5 -90q0 -35 -17.5 -61.5t-53.5 -60.5z" />
    <glyph glyph-name="three.sups" unicode="&#xb3;" horiz-adv-x="350" 
d="M212 696q50 -6 80.5 -39.5t30.5 -82.5q0 -56 -40.5 -94.5t-103.5 -38.5q-68 0 -107.5 35t-48.5 85l57 16q6 -35 32.5 -58t64.5 -23q39 0 62 22.5t23 55.5t-22.5 55t-64.5 22q-8 0 -28 -2l-10 38l95 108h-197v55h274v-46z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="168" 
d="M0 578l80 136h88l-97 -136h-71z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="645" 
d="M578 700v-73h-64v-837h-73v837h-127v-837h-74v511q-86 0 -143 57t-57 141q0 85 59 143t146 58h333z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="222" 
d="M111 235q-24 0 -40 16t-16 39t16 39t40 16t40 -16t16 -39t-16 -39t-40 -16z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="209" 
d="M101 -222q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l31 97h62l-18 -58q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5z" />
    <glyph glyph-name="one.sups" unicode="&#xb9;" horiz-adv-x="243" 
d="M131 850h54v-400h-62v328l-100 -48l-15 55z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="471" 
d="M148 495l101 -175l-101 -175h-78l100 175l-100 175h78zM300 495l101 -175l-101 -175h-78l100 175l-100 175h78z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="776" 
d="M123 300v328l-100 -48l-15 55l123 65h54v-400h-62zM523 700h65l-437 -700h-65zM759 140v-56h-52v-84h-60v84h-203v49l144 267h67l-139 -260h131v89h60v-89h52z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="770" 
d="M123 300v328l-100 -48l-15 55l123 65h54v-400h-62zM523 700h65l-437 -700h-65zM550 56h193v-56h-289v43l160 153q30 29 43 48t13 41q0 31 -19.5 49.5t-54.5 18.5q-67 0 -83 -75l-59 16q7 48 45.5 82t95.5 34q63 0 101.5 -33t38.5 -90q0 -35 -17.5 -61.5t-53.5 -60.5z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="883" 
d="M323 424q0 -56 -40.5 -94.5t-103.5 -38.5q-68 0 -107.5 35t-48.5 85l57 16q6 -35 32.5 -58t64.5 -23q39 0 62 22.5t23 55.5t-22.5 55t-64.5 22q-8 0 -28 -2l-10 38l95 108h-197v55h274v-46l-97 -108q50 -6 80.5 -39.5t30.5 -82.5zM630 700h65l-437 -700h-65zM866 140v-56
h-52v-84h-60v84h-203v49l144 267h67l-139 -260h131v89h60v-89h52z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="541" 
d="M276 505q24 0 40 -15.5t16 -39.5q0 -23 -16 -38.5t-40 -15.5t-40 15.5t-16 38.5q0 24 16 39.5t40 15.5zM312 306q0 -50 -19.5 -91t-47 -67t-55 -49.5t-47 -52t-19.5 -61.5q0 -56 38 -91.5t101 -35.5q64 0 106 36.5t58 99.5l70 -20q-17 -81 -77 -134.5t-158 -53.5
q-95 0 -156 55t-61 141q0 44 20 80t48.5 61l56.5 49.5t48 58.5t20 75h74z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="695" 
d="M391 779h-71l-97 136h89zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260h293l-146 360z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="695" 
d="M471 915l-97 -136h-71l80 136h88zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260h293l-146 360z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="695" 
d="M347 868l-70 -91h-79l108 132h81l109 -132h-78zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260h293l-146 360z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="695" 
d="M282 841q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h59q0 -117 -81 -117q-30 0 -74 29.5t-61 29.5zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260h293l-146 360z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="695" 
d="M255 776q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM440 776q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85z
M201 260h293l-146 360z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="695" 
d="M347 753q-47 0 -79 31t-32 77t32 77t79 31q48 0 80 -31t32 -77t-32 -77t-80 -31zM347 917q-26 0 -41.5 -15.5t-15.5 -40.5t15.5 -40.5t41.5 -15.5q27 0 42.5 15.5t15.5 40.5t-15.5 40.5t-42.5 15.5zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260
h293l-146 360z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="975" 
d="M920 627h-366v-236h315v-70h-315v-248h365v-73h-443v189h-262l-114 -189h-89l426 700h483v-73zM258 261h218v361z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="780" 
d="M404 62q91 0 157.5 43t93.5 112l76 -20q-31 -85 -109.5 -143t-185.5 -66l-14 -46q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l27 85q-144 11 -238 114t-94 250q0 153 104 257t258 104
q120 0 208 -59.5t121 -151.5l-78 -19q-27 68 -93.5 111.5t-157.5 43.5q-124 0 -203 -83t-79 -202q0 -122 78.5 -206.5t203.5 -84.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="623" 
d="M362 779h-71l-97 136h89zM568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="623" 
d="M442 915l-97 -136h-71l80 136h88zM568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="623" 
d="M318 868l-70 -91h-79l108 132h81l109 -132h-78zM568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="623" 
d="M226 776q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM411 776q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z
" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="230" 
d="M159 779h-71l-97 136h89zM75 0v700h80v-700h-80z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="230" 
d="M72 779l80 136h88l-97 -136h-71zM75 0v700h80v-700h-80z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="230" 
d="M156 909l109 -132h-78l-71 91l-70 -91h-79l108 132h81zM75 0v700h80v-700h-80z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="230" 
d="M23 776q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM208 776q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM75 0v700h80v-700h-80z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="758" 
d="M372 700q159 0 251.5 -98t92.5 -252t-92.5 -252t-251.5 -98h-266v315h-71v69h71v316h266zM371 73q124 0 194.5 78t70.5 201q0 120 -70.5 198t-194.5 78h-186v-244h217v-69h-217v-242h186z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="723" 
d="M297 841q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h59q0 -117 -81 -117q-30 0 -74 29.5t-61 29.5zM569 700h79v-700h-69l-424 560v-560h-80v700h69l425 -561v561z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="806" 
d="M447 779h-71l-97 136h89zM403 -13q-154 0 -257.5 105t-103.5 260q0 153 103.5 257t257.5 104t257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105zM403 61q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5t-202.5 -83.5t-78.5 -203.5q0 -123 78.5 -207
t202.5 -84z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="806" 
d="M359 779l80 136h88l-97 -136h-71zM403 -13q-154 0 -257.5 105t-103.5 260q0 153 103.5 257t257.5 104t257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105zM403 61q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5t-202.5 -83.5t-78.5 -203.5
q0 -123 78.5 -207t202.5 -84z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="806" 
d="M404 868l-70 -91h-79l108 132h81l109 -132h-78zM403 713q154 0 257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105t-257.5 105t-103.5 260q0 153 103.5 257t257.5 104zM403 61q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5t-202.5 -83.5t-78.5 -203.5
q0 -123 78.5 -207t202.5 -84z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="806" 
d="M473 782q-30 0 -74 29.5t-61 29.5q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h59q0 -117 -81 -117zM403 -13q-154 0 -257.5 105t-103.5 260q0 153 103.5 257t257.5 104t257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105zM403 61
q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5t-202.5 -83.5t-78.5 -203.5q0 -123 78.5 -207t202.5 -84z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="806" 
d="M311 776q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM496 776q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM403 -13q-154 0 -257.5 105t-103.5 260q0 153 103.5 257t257.5 104
t257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105zM403 61q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5t-202.5 -83.5t-78.5 -203.5q0 -123 78.5 -207t202.5 -84z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="503" 
d="M433 487l-136 -136l135 -135l-45 -47l-135 136l-135 -135l-47 46l136 135l-135 135l45 46l135 -135l136 135z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="806" 
d="M638 630q59 -50 92.5 -122t33.5 -156q0 -155 -103.5 -260t-257.5 -105q-100 0 -183 47l-33 -47h-65l54 77q-63 50 -98.5 125t-35.5 163q0 153 103.5 257t257.5 104q104 0 191 -52l36 52h66zM122 352q0 -141 98 -225l331 472q-65 40 -148 40q-124 0 -202.5 -83.5
t-78.5 -203.5zM403 61q124 0 202.5 84t78.5 207q0 131 -90 215l-331 -471q64 -35 140 -35z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="708" 
d="M398 779h-71l-97 136h89zM562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="708" 
d="M478 915l-97 -136h-71l80 136h88zM562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="708" 
d="M354 868l-70 -91h-79l108 132h81l109 -132h-78zM562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="708" 
d="M262 776q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM447 776q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5
t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="652" 
d="M451 915l-97 -136h-71l80 136h88zM551 700h90l-275 -432v-268h-80v268l-275 432h91l224 -357z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="643" 
d="M384 586q105 0 167 -69t62 -157q0 -89 -62.5 -157t-170.5 -68h-226v-135h-79v700h79v-114h230zM378 207q72 0 115 45.5t43 107.5t-43 108t-111 46h-228v-307h224z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="590" 
d="M407 396q63 -21 99.5 -69t36.5 -119q0 -98 -65 -153t-174 -55h-68v70h67q76 0 119.5 37.5t43.5 102.5q0 67 -41 104t-114 37h-53v65h22q65 0 104.5 36t39.5 95q0 55 -36 89.5t-94 34.5q-71 0 -109 -45.5t-38 -126.5v-499h-77v499q0 110 59.5 176t162.5 66q90 0 149 -53.5
t59 -137.5q0 -108 -93 -154z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="644" 
d="M352 578h-71l-97 136h89zM295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="644" 
d="M264 578l80 136h88l-97 -136h-71zM295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="644" 
d="M308 667l-70 -91h-79l108 132h81l109 -132h-78zM492 499h77v-499h-77v91q-65 -104 -197 -104q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5
t134.5 -54.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="644" 
d="M378 581q-30 0 -74 29.5t-61 29.5q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h59q0 -117 -81 -117zM295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137
t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="644" 
d="M216 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM401 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5
q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="644" 
d="M308 552q-47 0 -79 31t-32 77t32 77t79 31q48 0 80 -31t32 -77t-32 -77t-80 -31zM308 604q27 0 42.5 15.5t15.5 40.5t-15.5 40.5t-42.5 15.5q-26 0 -41.5 -15.5t-15.5 -40.5t15.5 -40.5t41.5 -15.5zM295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5
q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1041" 
d="M999 258q0 -11 -2 -37h-426q7 -75 57.5 -120.5t125.5 -45.5q55 0 95 23t59 58l71 -21q-29 -57 -84.5 -92.5t-136.5 -35.5q-129 0 -194 100v-87h-72v91q-65 -104 -197 -104q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h72v-89q66 101 190 101
q112 0 178.5 -71.5t66.5 -181.5zM570 279l354 1q-5 73 -51 118.5t-123 45.5q-74 0 -124.5 -48.5t-55.5 -116.5zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="577" 
d="M303 57q59 0 102.5 29.5t56.5 70.5l72 -19q-15 -53 -67.5 -96.5t-128.5 -52.5l-15 -47q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l27 85q-101 10 -166.5 83.5t-65.5 179.5q0 111 74 185.5
t186 74.5q89 0 151.5 -46.5t79.5 -104.5l-72 -21q-13 41 -57 72t-102 31q-82 0 -133 -56.5t-51 -136.5q0 -79 51.5 -135.5t132.5 -56.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="590" 
d="M343 578h-71l-97 136h89zM306 -13q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-28 -61 -88 -102.5t-148 -41.5zM119 282l352 1q-5 72 -51 116t-123 44
q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="590" 
d="M255 578l80 136h88l-97 -136h-71zM306 -13q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-28 -61 -88 -102.5t-148 -41.5zM119 282l352 1q-5 72 -51 116t-123 44
q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="590" 
d="M299 667l-70 -91h-79l108 132h81l109 -132h-78zM547 261q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-28 -61 -88 -102.5t-148 -41.5q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179zM119 282l352 1
q-5 72 -51 116t-123 44q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="590" 
d="M206 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM391 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM306 -13q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5
q116 0 183 -71t67 -179q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-28 -61 -88 -102.5t-148 -41.5zM119 282l352 1q-5 72 -51 116t-123 44q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="228" 
d="M157 578h-71l-97 136h89zM75 0v499h78v-499h-78z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="228" 
d="M69 578l80 136h88l-97 -136h-71zM75 0v499h78v-499h-78z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="228" 
d="M153 708l109 -132h-78l-71 91l-70 -91h-79l108 132h81zM75 0v499h78v-499h-78z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="228" 
d="M36 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13q21 0 34.5 -13t13.5 -33t-13.5 -33.5t-34.5 -13.5zM191 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM75 0v499h78v-499h-78z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="611" 
d="M416 589q150 -162 150 -344q0 -115 -73 -187t-187 -72q-111 0 -186 72t-75 178q0 107 73 173.5t183 66.5q86 0 144 -46q-39 73 -103 140l-196 -49l-12 49l164 42q-49 43 -115 88h107q44 -33 84 -69l119 30l12 -50zM306 58q82 0 132 49.5t50 126.5q0 74 -50 123t-132 49
t-132 -48.5t-50 -121.5q0 -78 50 -128t132 -50z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M243 640q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h59q0 -117 -81 -117q-30 0 -74 29.5t-61 29.5zM337 511q93 0 146.5 -58t53.5 -160v-293h-77v282q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v499h77v-88
q28 45 75.5 72.5t109.5 27.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="613" 
d="M351 578h-71l-97 136h89zM307 -13q-113 0 -188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5t188 -74.5t75 -186.5t-75 -187.5t-188 -75.5zM307 58q80 0 133.5 54.5t53.5 136.5t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137t54 -136.5t135 -54.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="613" 
d="M263 578l80 136h88l-97 -136h-71zM307 -13q-113 0 -188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5t188 -74.5t75 -186.5t-75 -187.5t-188 -75.5zM307 58q80 0 133.5 54.5t53.5 136.5t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137t54 -136.5t135 -54.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="613" 
d="M307 667l-70 -91h-79l108 132h81l109 -132h-78zM307 511q113 0 188 -74.5t75 -186.5t-75 -187.5t-188 -75.5t-188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5zM307 58q80 0 133.5 54.5t53.5 136.5t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137t54 -136.5t135 -54.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="613" 
d="M377 581q-30 0 -74 29.5t-61 29.5q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h59q0 -117 -81 -117zM307 -13q-113 0 -188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5t188 -74.5t75 -186.5t-75 -187.5t-188 -75.5zM307 58q80 0 133.5 54.5t53.5 136.5
t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137t54 -136.5t135 -54.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="613" 
d="M215 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM400 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM307 -13q-113 0 -188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5
t188 -74.5t75 -186.5t-75 -187.5t-188 -75.5zM307 58q80 0 133.5 54.5t53.5 136.5t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137t54 -136.5t135 -54.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="580" 
d="M290 471q-21 0 -36 15.5t-15 35.5t15 35t36 15t36 -15t15 -35t-15 -35.5t-36 -15.5zM70 318v65h440v-65h-440zM290 129q-21 0 -36 15.5t-15 35.5t15 35t36 15t36 -15t15 -35t-15 -35.5t-36 -15.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="613" 
d="M480 450q90 -76 90 -200q0 -112 -75 -187.5t-188 -75.5q-70 0 -129 31l-22 -31h-62l42 60q-93 75 -93 203q0 112 75.5 186.5t188.5 74.5q70 0 132 -32l23 33h61zM118 249q0 -87 60 -142l219 312q-42 22 -90 22q-81 0 -135 -55t-54 -137zM307 58q80 0 133.5 54.5
t53.5 136.5q0 86 -57 140l-217 -311q38 -20 87 -20z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M348 578h-71l-97 136h89zM455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M428 714l-97 -136h-71l80 136h88zM455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M304 667l-70 -91h-79l108 132h81l109 -132h-78zM455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M211 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM396 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58
t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="546" 
d="M402 714l-97 -136h-71l80 136h88zM453 499h82l-307 -709h-81l95 220l-231 489h85l185 -403z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="644" 
d="M349 511q108 0 180 -74.5t72 -186.5q0 -113 -72 -188t-180 -75q-66 0 -116.5 27.5t-80.5 76.5v-301h-77v940h77v-321q64 102 197 102zM338 57q82 0 134.5 54.5t52.5 137.5q0 84 -52.5 138t-134.5 54q-78 0 -133 -55t-55 -137t55 -137t133 -55z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="546" 
d="M186 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM371 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM453 499h82l-307 -709h-81l95 220l-231 489h85l185 -403z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="695" 
d="M479 853v-60h-263v60h263zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260h293l-146 360z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="644" 
d="M177 592v60h263v-60h-263zM295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="695" 
d="M347 766q-71 0 -109.5 38t-38.5 96h65q1 -36 21 -58t62 -22t62.5 22.5t20.5 57.5h65q0 -58 -38.5 -96t-109.5 -38zM599 0l-76 188h-351l-76 -188h-85l287 700h99l287 -700h-85zM201 260h293l-146 360z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="644" 
d="M308 565q-71 0 -109.5 38t-38.5 96h65q1 -36 21 -58t62 -22t62.5 22.5t20.5 57.5h65q0 -58 -38.5 -96t-109.5 -38zM295 -13q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h77v-499h-77v91q-65 -104 -197 -104zM306 57q78 0 133 55t55 137t-55 137
t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="695" 
d="M706 -132l17 -54q-32 -37 -86 -37q-45 0 -72.5 24.5t-27.5 67.5q0 32 23 67t59 64h-20l-76 188h-351l-76 -188h-85l287 700h99l287 -700q-81 -68 -81 -121q0 -20 11 -31.5t31 -11.5q37 0 61 32zM201 260h293l-146 360z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="644" 
d="M591 -132l17 -54q-32 -37 -86 -37q-45 0 -72.5 24.5t-27.5 67.5q0 32 23 67t59 64h-12v91q-65 -104 -197 -104q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v90h77v-499q-81 -68 -81 -121q0 -20 11 -31.5t31 -11.5q37 0 61 32zM306 57q78 0 133 55
t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="780" 
d="M360 779l80 136h88l-97 -136h-71zM405 -13q-155 0 -259 105t-104 260q0 153 104 257t258 104q120 0 208 -59.5t121 -151.5l-78 -19q-27 68 -93.5 111.5t-157.5 43.5q-124 0 -203 -83t-79 -202q0 -122 78.5 -206.5t203.5 -84.5q91 0 157.5 43t93.5 112l76 -20
q-34 -92 -121 -151t-205 -59z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="577" 
d="M259 578l80 136h88l-97 -136h-71zM303 -13q-112 0 -186 75.5t-74 188.5q0 111 74 185.5t186 74.5q89 0 151.5 -46.5t79.5 -104.5l-72 -21q-13 41 -57 72t-102 31q-82 0 -133 -56.5t-51 -136.5q0 -79 51.5 -135.5t132.5 -56.5q59 0 102.5 29.5t56.5 70.5l72 -19
q-17 -59 -79 -105t-152 -46z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="780" 
d="M404 776q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM405 -13q-155 0 -259 105t-104 260q0 153 104 257t258 104q120 0 208 -59.5t121 -151.5l-78 -19q-27 68 -93.5 111.5t-157.5 43.5q-124 0 -203 -83t-79 -202
q0 -122 78.5 -206.5t203.5 -84.5q91 0 157.5 43t93.5 112l76 -20q-34 -92 -121 -151t-205 -59z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="577" 
d="M303 575q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM303 -13q-112 0 -186 75.5t-74 188.5q0 111 74 185.5t186 74.5q89 0 151.5 -46.5t79.5 -104.5l-72 -21q-13 41 -57 72t-102 31q-82 0 -133 -56.5t-51 -136.5
q0 -79 51.5 -135.5t132.5 -56.5q59 0 102.5 29.5t56.5 70.5l72 -19q-17 -59 -79 -105t-152 -46z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="780" 
d="M363 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM404 62q91 0 157.5 43t93.5 112l76 -20q-34 -92 -121 -151t-205 -59q-155 0 -259 105t-104 260q0 153 104 257t258 104q120 0 208 -59.5t121 -151.5l-78 -19q-27 68 -93.5 111.5t-157.5 43.5q-124 0 -203 -83
t-79 -202q0 -122 78.5 -206.5t203.5 -84.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="577" 
d="M262 576l-108 132h79l70 -91l71 91h78l-109 -132h-81zM303 57q59 0 102.5 29.5t56.5 70.5l72 -19q-17 -59 -79 -105t-152 -46q-112 0 -186 75.5t-74 188.5q0 111 74 185.5t186 74.5q89 0 151.5 -46.5t79.5 -104.5l-72 -21q-13 41 -57 72t-102 31q-82 0 -133 -56.5
t-51 -136.5q0 -79 51.5 -135.5t132.5 -56.5z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="727" 
d="M300 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM341 700q159 0 251.5 -98t92.5 -252t-92.5 -252t-251.5 -98h-266v700h266zM340 73q125 0 195 78t70 201q0 120 -70.5 198t-194.5 78h-186v-555h186z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="721" 
d="M492 730h77v-730h-77v91q-65 -104 -197 -104q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v321zM651 730h76l-29 -182h-60zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="758" 
d="M372 700q159 0 251.5 -98t92.5 -252t-92.5 -252t-251.5 -98h-266v315h-71v69h71v316h266zM371 73q124 0 194.5 78t70.5 201q0 120 -70.5 198t-194.5 78h-186v-244h217v-69h-217v-242h186z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="653" 
d="M642 646v-54h-73v-592h-77v91q-65 -104 -197 -104q-108 0 -180 75t-72 188q0 112 72 186.5t180 74.5q131 0 197 -102v183h-190v54h190v84h77v-84h73zM306 57q78 0 133 55t55 137t-55 137t-133 55q-82 0 -134.5 -54t-52.5 -138q0 -83 52.5 -137.5t134.5 -54.5z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="623" 
d="M450 853v-60h-263v60h263zM568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="590" 
d="M168 592v60h263v-60h-263zM306 -13q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-28 -61 -88 -102.5t-148 -41.5zM119 282l352 1q-5 72 -51 116t-123 44
q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="623" 
d="M318 776q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="590" 
d="M299 575q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM306 -13q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21
q-28 -61 -88 -102.5t-148 -41.5zM119 282l352 1q-5 72 -51 116t-123 44q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="623" 
d="M589 -132l17 -54q-32 -37 -86 -37q-45 0 -72.5 24.5t-27.5 67.5q0 32 23 67t59 64h-427v700h493v-73h-414v-236h363v-70h-363v-248h413v-73q-81 -68 -81 -121q0 -20 11 -31.5t31 -11.5q37 0 61 32z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="590" 
d="M547 261q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-31 -66 -93 -106q-102 -81 -102 -146q0 -20 11 -31.5t31 -11.5q37 0 61 32l17 -54q-32 -37 -86 -37q-45 0 -72.5 24.5t-27.5 67.5q0 58 68 121q-21 -3 -43 -3q-116 0 -189.5 72
t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179zM119 282l352 1q-5 72 -51 116t-123 44q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="623" 
d="M277 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM568 627h-414v-236h363v-70h-363v-248h413v-73h-492v700h493v-73z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="590" 
d="M258 576l-108 132h79l70 -91l71 91h78l-109 -132h-81zM547 261q0 -23 -2 -36h-426q5 -76 55 -123t132 -47q61 0 103 27.5t62 69.5l71 -21q-28 -61 -88 -102.5t-148 -41.5q-116 0 -189.5 72t-73.5 191q0 112 71 186.5t183 74.5q116 0 183 -71t67 -179zM119 282l352 1
q-5 72 -51 116t-123 44q-74 0 -123.5 -47t-54.5 -114z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="799" 
d="M399 766q-71 0 -109.5 38t-38.5 96h65q1 -36 21 -58t62 -22t62.5 22.5t20.5 57.5h65q0 -58 -38.5 -96t-109.5 -38zM757 371v-43q-1 -158 -99 -249.5t-250 -91.5q-156 0 -261 104.5t-105 260.5q0 153 104 257t258 104q118 0 205 -57.5t123 -149.5l-75 -20
q-30 69 -95.5 110.5t-156.5 41.5q-125 0 -204 -83t-79 -202q0 -122 79.5 -207t209.5 -85q114 0 187 65.5t80 175.5h-275v69h354z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="642" 
d="M310 565q-71 0 -109.5 38t-38.5 96h65q1 -36 21 -58t62 -22t62.5 22.5t20.5 57.5h65q0 -58 -38.5 -96t-109.5 -38zM491 499h76v-482q0 -104 -69 -172t-178 -68q-164 0 -237 131l68 25q53 -89 170 -89q78 0 123 49t47 124v85q-65 -96 -196 -96q-109 0 -180.5 72.5
t-71.5 180.5q0 109 71.5 180.5t180.5 71.5q132 0 196 -95v83zM306 76q78 0 132 52.5t54 129.5q0 78 -54 130.5t-132 52.5q-82 0 -134.5 -51.5t-52.5 -131.5q0 -79 52.5 -130.5t134.5 -51.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="799" 
d="M399 776q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM757 371v-43q-1 -158 -99 -249.5t-250 -91.5q-156 0 -261 104.5t-105 260.5q0 153 104 257t258 104q118 0 205 -57.5t123 -149.5l-75 -20q-30 69 -95.5 110.5
t-156.5 41.5q-125 0 -204 -83t-79 -202q0 -122 79.5 -207t209.5 -85q114 0 187 65.5t80 175.5h-275v69h354z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="642" 
d="M310 575q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM491 499h76v-482q0 -104 -69 -172t-178 -68q-164 0 -237 131l68 25q53 -89 170 -89q78 0 123 49t47 124v85q-65 -96 -196 -96q-109 0 -180.5 72.5t-71.5 180.5
q0 109 71.5 180.5t180.5 71.5q132 0 196 -95v83zM306 76q78 0 132 52.5t54 129.5q0 78 -54 130.5t-132 52.5q-82 0 -134.5 -51.5t-52.5 -131.5q0 -79 52.5 -130.5t134.5 -51.5z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="799" 
d="M757 371v-43q-1 -158 -99 -249.5t-250 -91.5q-156 0 -261 104.5t-105 260.5q0 153 104 257t258 104q118 0 205 -57.5t123 -149.5l-75 -20q-30 69 -95.5 110.5t-156.5 41.5q-125 0 -204 -83t-79 -202q0 -122 79.5 -207t209.5 -85q114 0 187 65.5t80 175.5h-275v69h354z
M354 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="642" 
d="M358 760l-13 -179h-75l28 179h60zM491 499h76v-482q0 -104 -69 -172t-178 -68q-164 0 -237 131l68 25q53 -89 170 -89q78 0 123 49t47 124v85q-65 -96 -196 -96q-109 0 -180.5 72.5t-71.5 180.5q0 109 71.5 180.5t180.5 71.5q132 0 196 -95v83zM306 76q78 0 132 52.5
t54 129.5q0 78 -54 130.5t-132 52.5q-82 0 -134.5 -51.5t-52.5 -131.5q0 -79 52.5 -130.5t134.5 -51.5z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="745" 
d="M728 577v-58h-71v-519h-80v321h-408v-321h-80v519h-71v58h71v123h80v-123h408v123h80v-123h71zM577 393v126h-408v-126h408z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="614" 
d="M344 511q93 0 146.5 -58t53.5 -160v-293h-77v282q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v592h-71v54h71v84h77v-84h192v-54h-192v-181q28 45 75.5 72.5t109.5 27.5z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="230" 
d="M-16 793v60h263v-60h-263zM75 0v700h80v-700h-80z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="228" 
d="M-18 592v60h263v-60h-263zM75 0v499h78v-499h-78z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="230" 
d="M191 -132l16 -54q-30 -37 -87 -37q-44 0 -71 24.5t-27 66.5q0 63 67 132h-14v700h80v-700q-67 -69 -67 -120q0 -44 40 -44q36 0 63 32z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="228" 
d="M113 575q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM189 -132l16 -54q-30 -37 -87 -37q-44 0 -71 24.5t-27 66.5q0 63 67 132h-12v499h78v-499q-67 -69 -67 -120q0 -44 40 -44q36 0 63 32z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="230" 
d="M116 776q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM75 0v700h80v-700h-80z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="228" 
d="M75 0v499h78v-499h-78z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="678" 
d="M343 386l315 -386h-98l-270 330l-135 -147v-183h-80v700h80v-410l375 410h102zM291 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="526" 
d="M278 282l233 -282h-91l-192 230l-76 -79v-151h-77v730h77v-480l238 249h95zM237 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="571" 
d="M253 915l-97 -136h-71l80 136h88zM155 73h388v-73h-468v700h80v-627z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="227" 
d="M70 809l80 136h88l-97 -136h-71zM75 0v730h77v-730h-77z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="571" 
d="M155 73h388v-73h-468v700h80v-627zM260 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="227" 
d="M75 0v730h77v-730h-77zM63 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="578" 
d="M155 73h388v-73h-468v700h80v-627zM536 700l-29 -174h-60l11 174h78z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="314" 
d="M75 0v730h77v-730h-77zM221 548l12 182h76l-28 -182h-60z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="603" 
d="M187 73h388v-73h-468v276l-77 -33v71l77 33v353h80v-318l210 90v-70l-210 -91v-238z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="276" 
d="M266 506v-70l-89 -41v-394h-77v358l-90 -42v69l90 42v303h77v-267z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="723" 
d="M486 915l-97 -136h-71l80 136h88zM569 700h79v-700h-69l-424 560v-560h-80v700h69l425 -561v561z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M432 714l-97 -136h-71l80 136h88zM337 511q93 0 146.5 -58t53.5 -160v-293h-77v282q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v499h77v-88q28 45 75.5 72.5t109.5 27.5z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="723" 
d="M569 700h79v-700h-69l-424 560v-560h-80v700h69l425 -561v561zM316 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" 
d="M337 511q93 0 146.5 -58t53.5 -160v-293h-77v282q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v499h77v-88q28 45 75.5 72.5t109.5 27.5zM261 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="723" 
d="M321 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM569 700h79v-700h-69l-424 560v-560h-80v700h69l425 -561v561z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M268 576l-108 132h79l70 -91l71 91h78l-109 -132h-81zM337 511q93 0 146.5 -58t53.5 -160v-293h-77v282q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v499h77v-88q28 45 75.5 72.5t109.5 27.5z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="723" 
d="M569 700h79v-738q0 -82 -37 -127.5t-112 -45.5q-37 0 -81 20l18 64q28 -16 59 -16q74 0 74 101v55l-414 547v-560h-80v700h69l425 -561v561z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M337 511q93 0 146.5 -58t53.5 -160v-340q0 -83 -37 -129.5t-112 -46.5q-41 0 -81 20l18 64q26 -16 57 -16q78 0 78 104v333q0 74 -36.5 115.5t-103.5 41.5q-75 0 -121.5 -52t-46.5 -136v-251h-77v499h77v-88q28 45 75.5 72.5t109.5 27.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="806" 
d="M272 793v60h263v-60h-263zM403 -13q-154 0 -257.5 105t-103.5 260q0 153 103.5 257t257.5 104t257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105zM403 61q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5t-202.5 -83.5t-78.5 -203.5q0 -123 78.5 -207
t202.5 -84z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="613" 
d="M176 592v60h263v-60h-263zM307 -13q-113 0 -188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5t188 -74.5t75 -186.5t-75 -187.5t-188 -75.5zM307 58q80 0 133.5 54.5t53.5 136.5t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137t54 -136.5t135 -54.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="806" 
d="M279 779l77 136h78l-83 -136h-72zM432 779l86 136h78l-92 -136h-72zM403 -13q-154 0 -257.5 105t-103.5 260q0 153 103.5 257t257.5 104t257.5 -104t103.5 -257q0 -155 -103.5 -260t-257.5 -105zM403 61q124 0 202.5 84t78.5 207q0 120 -78.5 203.5t-202.5 83.5
t-202.5 -83.5t-78.5 -203.5q0 -123 78.5 -207t202.5 -84z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="613" 
d="M183 578l77 136h78l-83 -136h-72zM336 578l86 136h78l-92 -136h-72zM307 -13q-113 0 -188.5 75.5t-75.5 187.5t75.5 186.5t188.5 74.5t188 -74.5t75 -186.5t-75 -187.5t-188 -75.5zM307 58q80 0 133.5 54.5t53.5 136.5t-53.5 137t-133.5 55q-81 0 -135 -55t-54 -137
t54 -136.5t135 -54.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1002" 
d="M947 627h-365v-236h314v-70h-314v-248h364v-73h-558q-160 0 -253 98t-93 252t93 252t253 98h559v-73zM388 73h114v554h-114q-124 0 -195 -77.5t-71 -197.5q0 -123 70.5 -201t195.5 -78z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1041" 
d="M998 258q0 -23 -2 -37h-425q8 -75 58 -120.5t125 -45.5q55 0 95 23t59 58l71 -21q-27 -55 -84.5 -91.5t-139.5 -36.5q-78 0 -136 35.5t-88 95.5q-33 -60 -92 -95t-132 -35q-113 0 -188.5 75t-75.5 188q0 112 75.5 186.5t188.5 74.5q74 0 133 -35t91 -95q32 60 88.5 94.5
t129.5 34.5q116 0 182.5 -71.5t66.5 -181.5zM570 279l354 1q-6 74 -52 119t-123 45q-74 0 -124 -48.5t-55 -116.5zM307 59q80 0 133.5 55t53.5 136t-53.5 136.5t-133.5 55.5q-81 0 -135 -55.5t-54 -136.5t54 -136t135 -55z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="676" 
d="M450 915l-97 -136h-71l80 136h88zM542 0l-183 275h-205v-275h-79v700h324q102 0 162 -65t60 -148q0 -74 -46.5 -133.5t-128.5 -74.5l188 -279h-92zM154 628v-282h242q67 0 107.5 41.5t40.5 99.5q0 57 -40.5 99t-106.5 42h-243z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="354" 
d="M323 714l-97 -136h-71l80 136h88zM152 413q23 51 69 76t110 16v-72q-84 11 -131.5 -37.5t-47.5 -162.5v-233h-77v499h77v-86z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="676" 
d="M446 279l188 -279h-92l-183 275h-205v-275h-79v700h324q102 0 162 -65t60 -148q0 -74 -46.5 -133.5t-128.5 -74.5zM154 628v-282h242q67 0 107.5 41.5t40.5 99.5q0 57 -40.5 99t-106.5 42h-243zM294 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="354" 
d="M152 413q23 51 69 76t110 16v-72q-84 11 -131.5 -37.5t-47.5 -162.5v-233h-77v499h77v-86zM64 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="676" 
d="M285 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM542 0l-183 275h-205v-275h-79v700h324q102 0 162 -65t60 -148q0 -74 -46.5 -133.5t-128.5 -74.5l188 -279h-92zM154 628v-282h242q67 0 107.5 41.5t40.5 99.5q0 57 -40.5 99t-106.5 42h-243z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="354" 
d="M158 576l-108 132h79l70 -91l71 91h78l-109 -132h-81zM152 413q23 51 69 76t110 16v-72q-84 11 -131.5 -37.5t-47.5 -162.5v-233h-77v499h77v-86z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="661" 
d="M278 779l80 136h88l-97 -136h-71zM344 -13q-114 0 -197 61t-112 149l76 23q24 -70 87.5 -115.5t149.5 -45.5q81 0 132 39.5t51 91.5q0 41 -29 68.5t-99 49.5l-175 56q-155 50 -155 164q0 76 67 130.5t167 54.5q104 0 178.5 -48t106.5 -122l-74 -23q-26 56 -81 88.5
t-131 32.5q-67 0 -110 -33t-43 -78q0 -35 25 -58t84 -42l170 -54q90 -29 134 -69t44 -113q0 -86 -75 -146.5t-191 -60.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="510" 
d="M195 578l80 136h88l-97 -136h-71zM258 -13q-83 0 -146.5 43.5t-82.5 107.5l72 20q14 -45 57.5 -74.5t101.5 -29.5q55 0 90 24t35 58q0 29 -17 44.5t-56 27.5l-142 42q-116 34 -116 121q0 57 53 98.5t130 41.5t132.5 -35t78.5 -89l-70 -21q-14 34 -52 57t-90 23
q-47 0 -76.5 -23t-29.5 -51q0 -40 67 -60l142 -42q57 -17 89.5 -46.5t32.5 -83.5q0 -66 -57.5 -109.5t-145.5 -43.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="661" 
d="M610 194q0 -81 -68 -140.5t-175 -65.5l-14 -46q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l27 86q-99 10 -171.5 69t-98.5 139l76 23q24 -70 87.5 -115.5t149.5 -45.5q81 0 132 39.5t51 91.5
q0 41 -29 68.5t-99 49.5l-175 56q-155 50 -155 164q0 76 67 130.5t167 54.5q104 0 178.5 -48t106.5 -122l-74 -23q-26 56 -81 88.5t-131 32.5q-67 0 -110 -33t-43 -78q0 -35 25 -58t84 -42l170 -54q90 -29 134 -69t44 -113z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="510" 
d="M461 140q0 -63 -53 -106t-136 -47l-14 -45q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l28 88q-67 12 -116.5 52.5t-65.5 94.5l72 20q14 -45 57.5 -74.5t101.5 -29.5q55 0 90 24t35 58
q0 29 -17 44.5t-56 27.5l-142 42q-116 34 -116 121q0 57 53 98.5t130 41.5t132.5 -35t78.5 -89l-70 -21q-14 34 -52 57t-90 23q-47 0 -76.5 -23t-29.5 -51q0 -40 67 -60l142 -42q57 -17 89.5 -46.5t32.5 -83.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="661" 
d="M281 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM432 376q90 -29 134 -69t44 -113q0 -86 -75 -146.5t-191 -60.5q-114 0 -197 61t-112 149l76 23q24 -70 87.5 -115.5t149.5 -45.5q81 0 132 39.5t51 91.5q0 41 -29 68.5t-99 49.5l-175 56q-155 50 -155 164
q0 76 67 130.5t167 54.5q104 0 178.5 -48t106.5 -122l-74 -23q-26 56 -81 88.5t-131 32.5q-67 0 -110 -33t-43 -78q0 -35 25 -58t84 -42z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="510" 
d="M199 576l-108 132h79l70 -91l71 91h78l-109 -132h-81zM339 270q57 -17 89.5 -46.5t32.5 -83.5q0 -66 -57.5 -109.5t-145.5 -43.5q-83 0 -146.5 43.5t-82.5 107.5l72 20q14 -45 57.5 -74.5t101.5 -29.5q55 0 90 24t35 58q0 29 -17 44.5t-56 27.5l-142 42q-116 34 -116 121
q0 57 53 98.5t130 41.5t132.5 -35t78.5 -89l-70 -21q-14 34 -52 57t-90 23q-47 0 -76.5 -23t-29.5 -51q0 -40 67 -60z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="627" 
d="M354 627v-627l-18 -58q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l31 97h-18v627h-259v73h597v-73h-258z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="409" 
d="M271 -58q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l30 94q-106 22 -106 155v277h-99v70h99v172h77v-172h171v-70h-171v-277q0 -88 81 -88q37 0 71 17l21 -66q-42 -18 -83 -21z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="627" 
d="M273 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM612 700v-73h-258v-627h-80v627h-259v73h597z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="416" 
d="M345 717l-26 -161h-59l10 161h75zM349 81l21 -66q-51 -22 -104 -22q-73 0 -109.5 42.5t-36.5 116.5v277h-99v70h99v172h77v-172h171v-70h-171v-277q0 -88 81 -88q37 0 71 17z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="627" 
d="M612 627h-258v-249h166v-65h-166v-313h-80v313h-179v65h179v249h-259v73h597v-73z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="410" 
d="M350 81l21 -66q-53 -22 -105 -22q-73 0 -109.5 42.5t-36.5 116.5v118h-91v57h91v110h-99v62h99v172h77v-172h172v-62h-172v-110h149v-57h-149v-118q0 -88 82 -88q37 0 71 17z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="708" 
d="M486 853v-60h-263v60h263zM562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M436 652v-60h-263v60h263zM455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="708" 
d="M354 753q-47 0 -79 31t-32 77t32 77t79 31q48 0 80 -31t32 -77t-32 -77t-80 -31zM354 917q-26 0 -41.5 -15.5t-15.5 -40.5t15.5 -40.5t41.5 -15.5q27 0 42.5 15.5t15.5 40.5t-15.5 40.5t-42.5 15.5zM562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5
t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M304 552q-47 0 -79 31t-32 77t32 77t79 31q48 0 80 -31t32 -77t-32 -77t-80 -31zM304 716q-26 0 -41.5 -15.5t-15.5 -40.5t15.5 -40.5t41.5 -15.5q27 0 42.5 15.5t15.5 40.5t-15.5 40.5t-42.5 15.5zM455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58
t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="708" 
d="M385 915l-83 -136h-72l77 136h78zM455 779h-72l86 136h78zM562 700h80v-422q0 -132 -76.5 -211.5t-211.5 -79.5t-211.5 79.5t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5t54 159.5v420z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M335 714l-83 -136h-72l77 136h78zM405 578h-72l86 136h78zM455 499h77v-499h-77v87q-61 -100 -184 -100q-94 0 -147.5 58t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="708" 
d="M562 700h80v-422q0 -170 -121 -248q-50 -38 -80 -78t-30 -71q0 -20 11 -31.5t31 -11.5q34 0 61 32l16 -53q-30 -37 -86 -37q-45 0 -72 24.5t-27 67.5q0 58 66 119q-24 -4 -57 -4q-135 0 -211.5 79.5t-76.5 211.5v422h81v-420q0 -100 53.5 -159.5t153.5 -59.5t154 59.5
t54 159.5v420z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M555 -132l16 -54q-30 -37 -86 -37q-44 0 -71.5 24.5t-27.5 67.5q0 31 23 66.5t58 64.5h-12v87q-61 -100 -184 -100q-94 0 -147.5 58t-53.5 161v293h77v-282q0 -74 36.5 -115.5t103.5 -41.5q76 0 122 52t46 136v251h77v-499q-36 -31 -58 -63.5t-22 -57.5q0 -20 11 -31.5
t31 -11.5q35 0 61 32z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="986" 
d="M493 868l-70 -91h-79l108 132h81l109 -132h-78zM888 700h83l-220 -700h-83l-175 570l-175 -570h-83l-220 700h83l179 -587l179 587h73l179 -587z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="769" 
d="M384 667l-70 -91h-79l108 132h81l109 -132h-78zM677 499h80l-171 -499h-75l-126 384l-127 -384h-75l-172 499h81l131 -401l128 401h67l128 -401z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="652" 
d="M327 868l-70 -91h-79l108 132h81l109 -132h-78zM551 700h90l-275 -432v-268h-80v268l-275 432h91l224 -357z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="546" 
d="M279 667l-70 -91h-79l108 132h81l109 -132h-78zM453 499h82l-307 -709h-81l95 220l-231 489h85l185 -403z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="652" 
d="M234 776q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM419 776q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM551 700h90l-275 -432v-268h-80v268l-275 432h91l224 -357z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="625" 
d="M442 915l-97 -136h-71l80 136h88zM152 72h436v-72h-547v53l427 575h-416v72h525v-54z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="516" 
d="M383 714l-97 -136h-71l80 136h88zM150 67h322v-67h-427v51l314 381h-308v67h412v-53z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="625" 
d="M318 776q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM152 72h436v-72h-547v53l427 575h-416v72h525v-54z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="516" 
d="M259 575q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM150 67h322v-67h-427v51l314 381h-308v67h412v-53z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="625" 
d="M277 777l-108 132h79l70 -91l71 91h78l-109 -132h-81zM152 72h436v-72h-547v53l427 575h-416v72h525v-54z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="516" 
d="M218 576l-108 132h79l70 -91l71 91h78l-109 -132h-81zM150 67h322v-67h-427v51l314 381h-308v67h412v-53z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="502" 
d="M379 674q-72 0 -86 -117l-18 -134h144v-64h-152l-44 -327q-23 -188 -155 -188q-62 0 -98 38l29 60q27 -29 68 -29q69 0 84 119l43 327h-128v64h136l18 134q23 186 158 186q66 0 106 -36l-30 -62q-29 29 -75 29z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="661" 
d="M344 -13q-114 0 -197 61t-112 149l76 23q24 -70 87.5 -115.5t149.5 -45.5q81 0 132 39.5t51 91.5q0 41 -29 68.5t-99 49.5l-175 56q-155 50 -155 164q0 76 67 130.5t167 54.5q104 0 178.5 -48t106.5 -122l-74 -23q-26 56 -81 88.5t-131 32.5q-67 0 -110 -33t-43 -78
q0 -35 25 -58t84 -42l170 -54q90 -29 134 -69t44 -113q0 -86 -75 -146.5t-191 -60.5zM289 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="510" 
d="M258 -13q-83 0 -146.5 43.5t-82.5 107.5l72 20q14 -45 57.5 -74.5t101.5 -29.5q55 0 90 24t35 58q0 29 -17 44.5t-56 27.5l-142 42q-116 34 -116 121q0 57 53 98.5t130 41.5t132.5 -35t78.5 -89l-70 -21q-14 34 -52 57t-90 23q-47 0 -76.5 -23t-29.5 -51q0 -40 67 -60
l142 -42q57 -17 89.5 -46.5t32.5 -83.5q0 -66 -57.5 -109.5t-145.5 -43.5zM194 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="627" 
d="M612 700v-73h-258v-627h-80v627h-259v73h597zM268 -250l13 179h74l-27 -179h-60z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="409" 
d="M370 15q-51 -22 -104 -22q-73 0 -109.5 42.5t-36.5 116.5v277h-99v70h99v172h77v-172h171v-70h-171v-277q0 -88 81 -88q37 0 71 17zM201 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="298" 
d="M298 576h-78l-71 91l-70 -91h-79l108 132h81z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="298" 
d="M220 708h78l-109 -132h-81l-108 132h79l70 -91z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="296" 
d="M148 565q-71 0 -109.5 38t-38.5 96h65q1 -36 21 -58t62 -22t62.5 22.5t20.5 57.5h65q0 -58 -38.5 -96t-109.5 -38z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="108" 
d="M54 590q-23 0 -38.5 15t-15.5 38t15.5 38t38.5 15t38.5 -15t15.5 -38t-15.5 -38t-38.5 -15z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="223" 
d="M111 552q-47 0 -79 31t-32 77t32 77t79 31q48 0 80 -31t32 -77t-32 -77t-80 -31zM111 604q27 0 42.5 15.5t15.5 40.5t-15.5 40.5t-42.5 15.5q-26 0 -41.5 -15.5t-15.5 -40.5t15.5 -40.5t41.5 -15.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="185" 
d="M99 -223q-44 0 -71.5 24.5t-27.5 67.5q0 31 23 66.5t58 64.5h65q-36 -31 -58 -63.5t-22 -57.5q0 -20 11 -31.5t31 -11.5q35 0 61 32l16 -54q-30 -37 -86 -37z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="301" 
d="M220 581q-30 0 -74 29.5t-61 29.5q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h59q0 -117 -81 -117z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="317" 
d="M0 578l77 136h78l-83 -136h-72zM153 578l86 136h78l-92 -136h-72z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M0 578h-71l-97 136h89z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M-168 578l80 136h88l-97 -136h-71z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M0 576h-78l-71 91l-70 -91h-79l108 132h81z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-81 581q-30 0 -74 29.5t-61 29.5q-27 0 -27 -56h-58q0 118 81 118q30 0 74 -30t60 -30q27 0 27 56h60q0 -117 -82 -117z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-263 592v60h264v-60h-264z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M-148 565q-71 0 -109.5 38t-38.5 96h65q1 -36 21 -58t62 -22t62.5 22.5t20.5 57.5h65q0 -58 -38.5 -96t-109.5 -38z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-54 575q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-232 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM-47 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M-112 552q-47 0 -79 31t-32 77t32 77t79 31q48 0 80.5 -31t32.5 -77t-32.5 -77t-80.5 -31zM-112 604q27 0 42.5 15.5t15.5 40.5t-15.5 40.5t-42.5 15.5q-26 0 -41.5 -15.5t-15.5 -40.5t15.5 -40.5t41.5 -15.5z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-317 578l77 136h78l-83 -136h-72zM-164 578l86 136h79l-93 -136h-72z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-78 708h78l-109 -132h-81l-108 132h79l70 -91z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M0 760l-13 -179h-75l28 179h60z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-88 -250l13 179h75l-28 -179h-60z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-108 -222q-58 0 -101 32l13 44q46 -32 91 -26t45 38q0 26 -33.5 35.5t-69.5 1.5l31 97h62l-18 -58q40 -3 64 -25t24 -54q0 -40 -31 -62.5t-77 -22.5z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-86 -223q-44 0 -71.5 24.5t-27.5 67.5q0 31 23 66.5t58 64.5h65q-36 -31 -58 -63.5t-22 -57.5q0 -20 11 -31.5t31 -11.5q35 0 61 32l16 -54q-30 -37 -86 -37z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="693" 
d="M396 700l286 -700h-671l286 700h99zM125 72h443l-221 549z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="823" 
d="M778 363q0 -95 -44 -169.5t-121 -121.5h158v-72h-284v72q94 32 153 109.5t59 175.5q0 118 -81 199.5t-206 81.5t-206.5 -82t-81.5 -199q0 -98 59 -175.5t153 -109.5v-72h-283v72h156q-76 47 -120 121.5t-44 169.5q0 147 104.5 248.5t262.5 101.5t262 -101.5t104 -248.5z
" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="646" 
d="M620 63l17 -64q-31 -12 -60 -12q-94 0 -112 87q-63 -87 -179 -87q-80 0 -134 52v-249h-77v709h77v-282q0 -72 38 -114.5t102 -42.5q76 0 122 52t46 136v251h77v-376q0 -67 45 -67q17 0 38 7z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="649" 
d="M618 439h-109v-316q0 -64 44 -64q21 0 37 8l18 -63q-29 -13 -61 -13q-113 0 -113 132v316h-225v-439h-75v439h-104v70h588v-70z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="986" 
d="M537 779h-71l-97 136h89zM888 700h83l-220 -700h-83l-175 570l-175 -570h-83l-220 700h83l179 -587l179 587h73l179 -587z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="769" 
d="M428 578h-71l-97 136h89zM677 499h80l-171 -499h-75l-126 384l-127 -384h-75l-172 499h81l131 -401l128 401h67l128 -401z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="986" 
d="M617 915l-97 -136h-71l80 136h88zM888 700h83l-220 -700h-83l-175 570l-175 -570h-83l-220 700h83l179 -587l179 587h73l179 -587z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="769" 
d="M508 714l-97 -136h-71l80 136h88zM677 499h80l-171 -499h-75l-126 384l-127 -384h-75l-172 499h81l131 -401l128 401h67l128 -401z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="986" 
d="M401 776q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM586 776q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM888 700h83l-220 -700h-83l-175 570l-175 -570h-83l-220 700h83
l179 -587l179 587h73l179 -587z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="769" 
d="M292 575q-20 0 -33.5 13.5t-13.5 33.5t13.5 33t33.5 13t33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM477 575q-21 0 -34 13.5t-13 33.5t13 33t34 13q20 0 33.5 -13t13.5 -33t-13.5 -33.5t-33.5 -13.5zM677 499h80l-171 -499h-75l-126 384l-127 -384h-75l-172 499h81
l131 -401l128 401h67l128 -401z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="652" 
d="M371 779h-71l-97 136h89zM551 700h90l-275 -432v-268h-80v268l-275 432h91l224 -357z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="546" 
d="M322 578h-71l-97 136h89zM453 499h82l-307 -709h-81l95 220l-231 489h85l185 -403z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="625" 
d="M70 277v66h485v-66h-485z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="925" 
d="M70 277v66h785v-66h-785z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="241" 
d="M186 700l-43 -240h-88l66 240h65z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="241" 
d="M55 460l43 240h88l-65 -240h-66z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="241" 
d="M55 -137l43 240h88l-65 -240h-66z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="393" 
d="M186 700l-43 -240h-88l66 240h65zM338 700l-43 -240h-88l66 240h65z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="393" 
d="M55 460l43 240h88l-65 -240h-66zM207 460l44 240h87l-65 -240h-66z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="393" 
d="M55 -137l43 240h88l-65 -240h-66zM207 -137l44 240h87l-65 -240h-66z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="503" 
d="M213 0v477h-168v71h168v182h77v-182h168v-71h-168v-477h-77z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="503" 
d="M213 0v182h-168v71h168v224h-168v71h168v182h77v-182h168v-71h-168v-224h168v-71h-168v-182h-77z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="302" 
d="M152 196q-41 1 -69 28t-28 67q0 39 28 65t69 27q40 1 67.5 -26t27.5 -66q0 -40 -27.5 -68t-67.5 -27z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="623" 
d="M111 -7q-24 0 -40 16t-16 40q0 23 16 38.5t40 15.5t40 -15.5t16 -38.5q0 -24 -16 -40t-40 -16zM312 -7q-24 0 -40 16t-16 40q0 23 16 38.5t40 15.5q23 0 39 -15.5t16 -38.5q0 -24 -16 -40t-39 -16zM512 -7q-24 0 -40 16t-16 40q0 23 16 38.5t40 15.5t40 -15.5t16 -38.5
q0 -24 -16 -40t-40 -16z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1431" 
d="M241 333q-80 0 -135.5 54t-55.5 133t55.5 133t135.5 54q81 0 136.5 -54t55.5 -133t-55.5 -133t-136.5 -54zM236 0l454 700h72l-453 -700h-73zM241 396q54 0 90 36.5t36 87.5q0 52 -36 88t-90 36t-89.5 -36t-35.5 -88q0 -51 36 -87.5t89 -36.5zM758 -8q-80 0 -135.5 54.5
t-55.5 132.5q0 79 55.5 133t135.5 54q81 0 136 -54t55 -133q0 -78 -55 -132.5t-136 -54.5zM1190 -8q-80 0 -135.5 54.5t-55.5 132.5q0 79 55.5 133t135.5 54q81 0 136 -54t55 -133q0 -78 -55 -132.5t-136 -54.5zM758 55q53 0 89 36.5t36 87.5q0 52 -35.5 88t-89.5 36
t-89.5 -36t-35.5 -88q0 -51 35.5 -87.5t89.5 -36.5zM1190 55q53 0 89 36.5t36 87.5q0 52 -35.5 88t-89.5 36t-89.5 -36t-35.5 -88q0 -51 35.5 -87.5t89.5 -36.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="319" 
d="M249 495l-100 -175l100 -175h-78l-101 175l101 175h78z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="319" 
d="M148 495l101 -175l-101 -175h-78l100 175l-100 175h78z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="188" 
d="M-158 0l438 700h65l-437 -700h-66z" />
    <glyph glyph-name="zero.sups" unicode="&#x2070;" horiz-adv-x="386" 
d="M193 441q-77 0 -121 57.5t-44 151.5q0 95 43.5 152.5t121.5 57.5q76 0 120.5 -57.5t44.5 -152.5q0 -94 -44 -151.5t-121 -57.5zM193 501q49 0 74.5 40t25.5 109t-25.5 109.5t-74.5 40.5t-74 -40.5t-25 -109.5q0 -70 25 -109.5t74 -39.5z" />
    <glyph glyph-name="four.sups" unicode="&#x2074;" horiz-adv-x="346" 
d="M328 590v-56h-52v-84h-60v84h-203v49l144 267h67l-139 -260h131v89h60v-89h52z" />
    <glyph glyph-name="five.sups" unicode="&#x2075;" horiz-adv-x="343" 
d="M183 703q59 0 96 -36t37 -95q0 -57 -39.5 -94t-102.5 -37q-61 0 -100 31.5t-51 76.5l55 15q25 -68 94 -68q39 0 61 22t22 55q0 35 -22 56.5t-61 21.5q-52 0 -89 -27l-45 15l18 211h242v-55h-189l-10 -110q38 18 84 18z" />
    <glyph glyph-name="six.sups" unicode="&#x2076;" horiz-adv-x="331" 
d="M176 715q60 -1 98.5 -40t38.5 -96q0 -59 -41 -98.5t-106 -39.5t-107 39.5t-41 98.5q0 43 31 91l119 180h67l-90 -138q10 3 31 3zM166 496q39 0 63 23.5t24 59.5q0 35 -24.5 58.5t-63.5 23.5q-38 0 -62 -23.5t-24 -58.5q0 -36 24 -59.5t63 -23.5z" />
    <glyph glyph-name="seven.sups" unicode="&#x2077;" horiz-adv-x="305" 
d="M11 850h282v-46l-155 -354h-67l151 345h-211v55z" />
    <glyph glyph-name="eight.sups" unicode="&#x2078;" horiz-adv-x="350" 
d="M255 667q72 -31 72 -105q0 -52 -41.5 -86.5t-109.5 -34.5q-69 0 -111 34.5t-42 86.5q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM176 807q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5
t-54 17.5zM176 494q40 0 64 21t24 52t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="nine.sups" unicode="&#x2079;" horiz-adv-x="367" 
d="M183 860q65 0 107 -40t42 -99q-1 -44 -32 -91l-119 -180h-67l90 138q-14 -3 -30 -3q-60 1 -99 40t-39 96q0 59 41.5 99t105.5 40zM184 639q38 0 62 23.5t24 58.5q0 36 -24 60t-63 24q-38 0 -62.5 -24t-24.5 -60q0 -35 24.5 -58.5t63.5 -23.5z" />
    <glyph glyph-name="zero.sinf" unicode="&#x2080;" horiz-adv-x="386" 
d="M193 -160q-77 0 -121 58t-44 152q0 95 43.5 152.5t121.5 57.5q76 0 120.5 -57.5t44.5 -152.5q0 -94 -44 -152t-121 -58zM193 -100q49 0 74.5 40.5t25.5 109.5t-25.5 109.5t-74.5 40.5t-74 -40.5t-25 -109.5q0 -70 25 -110t74 -40z" />
    <glyph glyph-name="one.sinf" unicode="&#x2081;" horiz-adv-x="243" 
d="M131 250h54v-400h-62v328l-100 -48l-15 55z" />
    <glyph glyph-name="two.sinf" unicode="&#x2082;" horiz-adv-x="339" 
d="M119 -94h193v-56h-289v43l160 153q30 29 43 48t13 41q0 31 -19.5 49.5t-54.5 18.5q-67 0 -83 -75l-59 16q7 48 45.5 82t95.5 34q63 0 101.5 -33t38.5 -90q0 -35 -17.5 -61.5t-53.5 -60.5z" />
    <glyph glyph-name="three.sinf" unicode="&#x2083;" horiz-adv-x="350" 
d="M212 96q50 -6 80.5 -39.5t30.5 -82.5q0 -57 -40.5 -95.5t-103.5 -38.5q-68 0 -107.5 35t-48.5 86l57 16q6 -36 32.5 -59t64.5 -23q39 0 62 23t23 56t-22.5 55t-64.5 22q-8 0 -28 -2l-10 38l95 108h-197v55h274v-46z" />
    <glyph glyph-name="four.sinf" unicode="&#x2084;" horiz-adv-x="346" 
d="M328 -10v-56h-52v-84h-60v84h-203v48l144 268h67l-139 -260h131v89h60v-89h52z" />
    <glyph glyph-name="five.sinf" unicode="&#x2085;" horiz-adv-x="343" 
d="M183 103q59 0 96 -36t37 -95q0 -57 -40 -94.5t-102 -37.5q-61 0 -100.5 31.5t-50.5 77.5l55 15q11 -30 35.5 -49.5t58.5 -19.5q39 0 61 22t22 56q0 35 -22 56.5t-61 21.5q-52 0 -89 -27l-45 15l18 211h242v-55h-189l-10 -110q38 18 84 18z" />
    <glyph glyph-name="six.sinf" unicode="&#x2086;" horiz-adv-x="331" 
d="M176 115q60 -1 98.5 -40t38.5 -96q0 -59 -41.5 -99t-105.5 -40q-65 0 -107 40t-41 99q0 43 31 91l119 180h67l-90 -138q10 3 31 3zM166 -105q38 0 62.5 24t24.5 60q0 35 -24.5 58.5t-63.5 23.5q-38 0 -62 -23.5t-24 -58.5q0 -36 24 -60t63 -24z" />
    <glyph glyph-name="seven.sinf" unicode="&#x2087;" horiz-adv-x="305" 
d="M11 250h282v-46l-155 -354h-67l151 345h-211v55z" />
    <glyph glyph-name="eight.sinf" unicode="&#x2088;" horiz-adv-x="350" 
d="M255 67q72 -31 72 -105q0 -53 -41.5 -87.5t-109.5 -34.5q-69 0 -111 34.5t-42 87.5q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM176 207q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5
t-54 17.5zM176 -106q40 0 64 21t24 52t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="nine.sinf" unicode="&#x2089;" horiz-adv-x="331" 
d="M165 260q65 0 107 -40t41 -99q0 -41 -31 -91l-119 -180h-67l90 138q-10 -3 -31 -3q-60 1 -98.5 40t-38.5 96q0 59 41.5 99t105.5 40zM166 39q38 0 62 23.5t24 58.5q0 36 -24 60t-63 24q-38 0 -62.5 -24t-24.5 -60q0 -35 24.5 -58.5t63.5 -23.5z" />
    <glyph glyph-name="franc" unicode="&#x20a3;" horiz-adv-x="613" 
d="M557 627h-378v-233h349v-71h-349v-126h190v-53h-190v-144h-80v144h-58v53h58v503h458v-73z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="646" 
d="M221 74h387v-74h-560v74h95v162h-77v53h77v67h-77v53h77v107q0 95 55 147t146 52q87 0 143.5 -49t70.5 -120l-70 -20q-33 117 -145 117q-58 0 -90 -34.5t-32 -96.5v-103h255v-53h-255v-67h255v-53h-255v-162z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="756" 
d="M664 123l47 -60q-95 -76 -228 -76q-130 0 -226 75.5t-126 196.5h-100v53h91q-2 26 -2 40t2 38h-91v53h100q30 120 126 195t225 75q131 0 228 -74l-48 -61q-75 60 -180 60q-99 0 -170.5 -54t-98.5 -141h321v-53h-332q-2 -24 -2 -37q0 -21 3 -41h331v-53h-320
q27 -88 98.5 -142.5t169.5 -54.5q108 0 182 61z" />
    <glyph glyph-name="uni20BF" unicode="&#x20bf;" horiz-adv-x="669" 
d="M495 366q59 -20 92 -65.5t33 -106.5q0 -84 -60 -139t-163 -55h-40v-130h-63v130h-79v-130h-62v130h-130v71h80v558h-80v71h130v130h62v-130h79v130h63v-130h18q94 0 154.5 -51t61.5 -131q1 -53 -24.5 -92t-71.5 -60zM372 629h-190v-237h199q60 0 95.5 34.5t35.5 84.5
q0 51 -38.5 84.5t-101.5 33.5zM394 71q67 0 107 37.5t40 91.5q0 51 -39 89t-105 38h-215v-256h212z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1073" 
d="M884 400q-67 0 -113.5 45.5t-46.5 111.5t46.5 111t113.5 45t113 -45t46 -111t-46 -111.5t-113 -45.5zM569 139v561h79v-700h-69l-424 560v-560h-80v700h69zM884 652q-42 0 -69.5 -27.5t-27.5 -67.5t27.5 -68t69.5 -28q41 0 68 28t28 68t-27 67.5t-69 27.5zM742 274v61
h284v-61h-284z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="713" 
d="M141 380v271h-111v49h275v-49h-111v-271h-53zM343 380v320h52l111 -158l109 158h53v-320h-52v235l-109 -155l-113 155v-235h-51z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="849" 
d="M434 -11q-168 0 -280 104.5t-112 258.5q0 149 112 254.5t271 105.5t270.5 -105.5t111.5 -254.5v-16h-615v-211q90 -106 242 -106q140 0 229 94h79q-127 -124 -308 -124zM192 366h465v210q-90 106 -232 106q-143 0 -233 -106v-210z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="780" 
d="M123 300v328l-100 -48l-15 55l123 65h54v-400h-62zM523 700h65l-437 -700h-65zM686 217q72 -31 72 -105q0 -52 -41.5 -87t-109.5 -35q-69 0 -111 35t-42 87q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM607 357
q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5t-54 17.5zM607 44q40 0 64 21t24 52t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="887" 
d="M323 424q0 -56 -40.5 -94.5t-103.5 -38.5q-68 0 -107.5 35t-48.5 85l57 16q6 -35 32.5 -58t64.5 -23q39 0 62 22.5t23 55.5t-22.5 55t-64.5 22q-8 0 -28 -2l-10 38l95 108h-197v55h274v-46l-97 -108q50 -6 80.5 -39.5t30.5 -82.5zM630 700h65l-437 -700h-65zM793 217
q72 -31 72 -105q0 -52 -41.5 -87t-109.5 -35q-69 0 -111 35t-42 87q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM714 357q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5t-54 17.5zM714 44
q40 0 64 21t24 52t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="880" 
d="M316 422q0 -57 -39.5 -94t-102.5 -37q-61 0 -100 31.5t-51 76.5l55 15q25 -68 94 -68q39 0 61 22t22 55q0 35 -22 56.5t-61 21.5q-52 0 -89 -27l-45 15l18 211h242v-55h-189l-10 -110q38 18 84 18q59 0 96 -36t37 -95zM623 700h65l-437 -700h-65zM786 217q72 -31 72 -105
q0 -52 -41.5 -87t-109.5 -35q-69 0 -111 35t-42 87q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM707 357q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5t-54 17.5zM707 44q40 0 64 21t24 52
t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="842" 
d="M293 700v-46l-155 -354h-67l151 345h-211v55h282zM585 700h65l-437 -700h-65zM748 217q72 -31 72 -105q0 -52 -41.5 -87t-109.5 -35q-69 0 -111 35t-42 87q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM669 357
q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5t-54 17.5zM669 44q40 0 64 21t24 52t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="853" 
d="M783 377v-68l-580 -1l236 -235l-50 -49l-319 319l319 319l50 -49l-237 -235z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="779" 
d="M709 394l-50 -49l-232 233l-1 -578h-73v579l-233 -234l-50 49l319 319z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="853" 
d="M464 663l319 -319l-319 -319l-50 49l237 235l-581 1v68l580 1l-236 235z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="779" 
d="M659 355l50 -49l-320 -319l-319 319l50 49l233 -234v579h73l1 -578z" />
    <glyph glyph-name="arrowboth" unicode="&#x2194;" horiz-adv-x="1127" 
d="M738 663l319 -319l-319 -319l-50 49l237 235h-723l237 -235l-50 -49l-319 319l319 319l50 -49l-236 -235h721l-236 235z" />
    <glyph glyph-name="arrowupdn" unicode="&#x2195;" horiz-adv-x="779" 
d="M427 -19l232 233l50 -50l-320 -318l-319 318l50 50l233 -234v702l-233 -234l-50 50l319 318l320 -318l-50 -50l-232 233l-1 -350z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="669" 
d="M599 130l-50 -51l-409 409v-330l-70 -1v452h451l1 -70l-333 1z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="670" 
d="M149 613h451v-451h-70l1 332l-410 -410l-51 51l410 409l-331 -1v70z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="669" 
d="M529 541h70v-451h-451v70l331 -1l-409 409l51 52l409 -410z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="670" 
d="M190 148l331 1l1 -70h-452v451h70l-1 -332l410 410l51 -51z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="640" 
d="M307 -15q-106 0 -179 72t-73 178q0 101 69.5 169t177.5 68q130 0 205 -82q-8 108 -66 182t-150 74q-99 0 -174 -72l-19 70q84 71 198 71q132 0 210.5 -103.5t78.5 -272.5q0 -161 -76 -257.5t-202 -96.5zM309 56q86 0 140.5 72t56.5 177q-32 42 -84 70t-116 28
q-74 0 -124.5 -49.5t-50.5 -118.5q0 -75 50.5 -127t127.5 -52z" />
    <glyph glyph-name="emptyset" unicode="&#x2205;" horiz-adv-x="528" 
d="M508 530l-61 -61q51 -64 51 -149q0 -99 -67.5 -167t-166.5 -68q-86 0 -149 52l-60 -60l-35 34l61 61q-50 65 -50 148q0 99 67 167t166 68q86 0 149 -52l61 61zM94 320q0 -58 33 -102l241 240q-46 33 -104 33q-72 0 -121 -49t-49 -122zM436 320q0 57 -34 104l-241 -241
q46 -33 103 -33q73 0 122.5 49t49.5 121z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="641" 
d="M571 730v-940h-77v869h-347v-869h-77v940h501z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="653" 
d="M153 -130h445v-70h-543v62l310 411l-293 395v62h507v-70h-409l285 -388z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="553" 
d="M70 318v65h413v-65h-413z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="680" 
d="M597 790h78l-267 -881h-82l-157 415h-114v67h169l140 -385z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="891" 
d="M649 538q77 0 132 -55.5t55 -132.5q0 -78 -55 -132.5t-132 -54.5q-115 0 -203 127q-88 -127 -204 -127q-77 0 -132 54.5t-55 132.5q0 77 55 132.5t132 55.5q116 0 204 -130q88 130 203 130zM246 228q83 0 165 121q-82 121 -165 121q-50 0 -86 -35t-36 -86q0 -50 36 -85.5
t86 -35.5zM646 228q50 0 85.5 35.5t35.5 85.5t-36 85.5t-85 35.5q-84 0 -166 -121q82 -121 166 -121z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="548" 
d="M110 -156q-70 0 -110 36l30 64q32 -30 78 -30q71 0 88 118l80 572q26 185 163 185q70 0 109 -36l-30 -64q-28 30 -78 30q-74 0 -91 -116l-80 -572q-25 -187 -159 -187z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="609" 
d="M139 371l-69 20q0 52 29.5 87t90.5 35q39 0 80.5 -20.5t77.5 -40.5t62 -20q60 0 60 74l69 -20q0 -52 -29 -87.5t-89 -35.5q-40 0 -81.5 20.5t-77.5 40.5t-62 20q-61 0 -61 -73zM139 199l-69 20q0 52 29.5 86.5t90.5 34.5q39 0 80.5 -20t77.5 -40.5t62 -20.5q60 0 60 74
l69 -20q0 -52 -29 -87t-89 -35q-40 0 -81.5 20t-77.5 40t-62 20q-61 0 -61 -72z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="554" 
d="M484 289v-65h-233l-63 -144h-59l63 144h-120v65h149l54 123h-205v65h233l63 143h59l-63 -143h121v-65h-149l-54 -123h204z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="558" 
d="M483 623v-72l-332 -128l332 -129v-72l-413 164v73zM70 97v65h413v-65h-413z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="558" 
d="M75 623l413 -164v-73l-413 -164v72l332 129l-332 128v72zM75 97v65h413v-65h-413z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="585" 
d="M335 700l195 -350l-195 -350h-84l-196 350l196 350h84zM293 72l154 278l-154 278l-155 -278z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="228" 
d="M113 575q-23 0 -38.5 15t-15.5 38q0 22 15.5 37t38.5 15t38.5 -15t15.5 -37q0 -23 -15.5 -38t-38.5 -15zM75 0v499h78v-499h-78z" />
    <glyph glyph-name="f_f_j" horiz-adv-x="890" 
d="M826 499v-546q0 -83 -37.5 -129.5t-111.5 -46.5q-40 0 -82 20l19 64q26 -16 57 -16q78 0 78 104v480h-232v-429h-77v429h-233v-429h-76v429h-105v70h105v66q0 85 42 131.5t125 46.5q59 0 101 -21l-20 -65q-33 17 -74 17q-48 0 -73 -28.5t-25 -82.5v-64h233v66
q0 85 45.5 131.5t133.5 46.5q92 0 159 -52l-20 -65q-64 48 -134 48q-107 0 -107 -111v-64h309z" />
    <glyph glyph-name="f_j" horiz-adv-x="581" 
d="M517 499v-546q0 -83 -37.5 -129.5t-111.5 -46.5q-40 0 -82 20l19 64q24 -16 57 -16q39 0 58.5 27.5t19.5 76.5v480h-233v-429h-76v429h-105v70h105v66q0 85 45 131.5t133 46.5q93 0 159 -52l-19 -65q-64 48 -134 48q-53 0 -80.5 -28.5t-27.5 -82.5v-64h310z" />
    <glyph glyph-name="f_t" horiz-adv-x="724" 
d="M664 81l21 -66q-51 -22 -104 -22q-73 0 -109.5 42.5t-36.5 116.5v277h-228v-429h-76v429h-105v70h105v66q0 85 40.5 131.5t119.5 46.5q48 0 83 -14l-19 -64q-27 9 -57 9q-91 0 -91 -111v-64h228v172h77v-172h171v-70h-171v-277q0 -88 81 -88q37 0 71 17z" />
    <glyph glyph-name="t_t" horiz-adv-x="724" 
d="M665 81l21 -66q-53 -22 -104 -22q-73 0 -110 42.5t-37 116.5v277h-238v-277q0 -88 81 -88q37 0 71 17l21 -66q-51 -22 -104 -22q-73 0 -109.5 42.5t-36.5 116.5v277h-99v70h99v172h77v-172h238v172h77v-172h172v-70h-172v-277q0 -88 82 -88q37 0 71 17z" />
    <glyph glyph-name="zero.tf" horiz-adv-x="581" 
d="M295 -13q-130 0 -202 101t-72 262q0 162 72 262.5t202 100.5t201.5 -100.5t71.5 -262.5q0 -161 -71.5 -262t-201.5 -101zM295 61q93 0 143 78t50 211t-50 211t-143 78t-143.5 -78t-50.5 -211t50.5 -211t143.5 -78z" />
    <glyph glyph-name="one.tf" horiz-adv-x="581" 
d="M350 73h169v-73h-445v73h195v535l-175 -85l-20 69l209 108h67v-627z" />
    <glyph glyph-name="two.tf" horiz-adv-x="581" 
d="M176 73h360v-73h-479v59l279 269q57 55 83 92t26 80q0 65 -40.5 102.5t-111.5 37.5q-62 0 -105 -37t-57 -100l-72 20q12 78 76 134t157 56q103 0 167 -56.5t64 -154.5q0 -61 -32.5 -110t-99.5 -113z" />
    <glyph glyph-name="three.tf" horiz-adv-x="581" 
d="M326 425q98 0 157 -58.5t59 -153.5q0 -96 -69.5 -162t-174.5 -66q-111 0 -177.5 58t-84.5 140l72 20q13 -63 63 -104.5t124 -41.5q79 0 124.5 45.5t45.5 110.5t-41 107t-109 42q-51 0 -91 -27l-15 53l202 240h-355v72h459v-59z" />
    <glyph glyph-name="four.tf" horiz-adv-x="581" 
d="M557 227v-72h-96v-155h-80v155h-362v60l261 485h86l-254 -473h269v177h80v-177h96z" />
    <glyph glyph-name="five.tf" horiz-adv-x="581" 
d="M309 446q105 0 172 -63t67 -165q0 -98 -69.5 -164.5t-175.5 -66.5q-103 0 -167.5 53.5t-86.5 133.5l72 19q19 -60 64.5 -97t114.5 -37q77 0 123.5 46t46.5 114q0 70 -45 114.5t-125 44.5q-93 0 -164 -58l-60 19l30 361h406v-72h-335l-19 -223q64 41 151 41z" />
    <glyph glyph-name="six.tf" horiz-adv-x="581" 
d="M304 462q107 -2 174.5 -69.5t67.5 -167.5q0 -101 -70 -169.5t-180 -68.5t-180.5 68.5t-70.5 169.5q0 74 57 161l210 314h90l-167 -250q30 12 69 12zM296 60q76 0 124 47t48 118t-48 118.5t-125 47.5q-76 0 -124 -47.5t-48 -118.5t48 -118t125 -47z" />
    <glyph glyph-name="seven.tf" horiz-adv-x="581" 
d="M52 700h481v-60l-285 -640h-85l280 627h-391v73z" />
    <glyph glyph-name="eight.tf" horiz-adv-x="581" 
d="M426 379q61 -25 95.5 -72t34.5 -110q0 -91 -71 -150.5t-185 -59.5q-115 0 -186 59.5t-71 150.5q0 63 34.5 110t95.5 72q-47 23 -73.5 62t-26.5 89q0 77 62.5 130t164.5 53q101 0 163.5 -53t62.5 -130q0 -50 -26.5 -89t-73.5 -62zM300 645q-69 0 -109 -33.5t-40 -85.5
t40 -85.5t109 -33.5q68 0 108 33.5t40 85.5t-40 85.5t-108 33.5zM300 57q79 0 128 41t49 102q0 62 -48.5 102t-128.5 40q-81 0 -129 -40t-48 -102t48 -102.5t129 -40.5z" />
    <glyph glyph-name="nine.tf" horiz-adv-x="581" 
d="M298 713q110 0 180.5 -68.5t70.5 -169.5q0 -74 -57 -161l-210 -314h-90l167 250q-31 -12 -69 -11q-107 1 -174.5 68.5t-67.5 167.5q0 101 70 169.5t180 68.5zM299 310q76 0 124 47.5t48 117.5q0 71 -48 118t-125 47q-76 0 -124 -47t-48 -118t48 -118t125 -47z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="386" 
d="M193 -10q-77 0 -121 58t-44 152q0 95 43.5 152.5t121.5 57.5q76 0 120.5 -57.5t44.5 -152.5q0 -94 -44 -152t-121 -58zM193 51q49 0 74.5 40t25.5 109t-25.5 109.5t-74.5 40.5t-74 -40.5t-25 -109.5q0 -70 25 -109.5t74 -39.5z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="243" 
d="M131 400h54v-400h-62v328l-100 -48l-15 55z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="339" 
d="M119 56h193v-56h-289v43l160 153q30 29 43 48t13 41q0 31 -19.5 49.5t-54.5 18.5q-67 0 -83 -75l-59 16q7 48 45.5 82t95.5 34q63 0 101.5 -33t38.5 -90q0 -35 -17.5 -61.5t-53.5 -60.5z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="350" 
d="M212 246q50 -6 80.5 -39.5t30.5 -82.5q0 -56 -40.5 -95t-103.5 -39q-68 0 -107.5 35.5t-48.5 85.5l57 16q6 -35 32.5 -58t64.5 -23q39 0 62 22.5t23 55.5t-22.5 55t-64.5 22q-8 0 -28 -2l-10 38l95 108h-197v55h274v-46z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="346" 
d="M328 140v-56h-52v-84h-60v84h-203v49l144 267h67l-139 -260h131v89h60v-89h52z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="343" 
d="M183 253q59 0 96 -36t37 -95q0 -57 -40 -94.5t-102 -37.5q-61 0 -100 32t-51 77l55 15q25 -68 94 -68q39 0 61 22t22 55q0 35 -22 56.5t-61 21.5q-52 0 -89 -27l-45 15l18 211h242v-55h-189l-10 -110q38 18 84 18z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="331" 
d="M176 265q60 -1 98.5 -40t38.5 -96q0 -59 -41.5 -99t-105.5 -40q-65 0 -107 40t-41 99q0 43 31 91l119 180h67l-90 -138q10 3 31 3zM166 46q39 0 63 23.5t24 59.5q0 35 -24.5 58.5t-63.5 23.5q-38 0 -62 -23.5t-24 -58.5q0 -36 24 -59.5t63 -23.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="305" 
d="M11 400h282v-46l-155 -354h-67l151 345h-211v55z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="350" 
d="M255 217q72 -31 72 -105q0 -52 -41.5 -87t-109.5 -35q-69 0 -111 35t-42 87q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM176 357q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5t-54 17.5z
M176 44q40 0 64 21t24 52t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="367" 
d="M183 410q65 0 107 -40t42 -99q-1 -44 -32 -91l-119 -180h-67l90 138q-14 -3 -30 -3q-60 1 -99 40t-39 96q0 59 41.5 99t105.5 40zM184 189q38 0 62 23.5t24 58.5q0 36 -24 60t-63 24q-38 0 -62.5 -24t-24.5 -60q0 -35 24.5 -58.5t63.5 -23.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="386" 
d="M193 291q-77 0 -121 57.5t-44 151.5q0 95 43.5 152.5t121.5 57.5q76 0 120.5 -57.5t44.5 -152.5q0 -94 -44 -151.5t-121 -57.5zM193 351q49 0 74.5 40t25.5 109t-25.5 109.5t-74.5 40.5t-74 -40.5t-25 -109.5q0 -70 25 -109.5t74 -39.5z" />
    <glyph glyph-name="one.numr" horiz-adv-x="243" 
d="M131 700h54v-400h-62v328l-100 -48l-15 55z" />
    <glyph glyph-name="two.numr" horiz-adv-x="339" 
d="M119 356h193v-56h-289v43l160 153q30 29 43 48t13 41q0 31 -19.5 49.5t-54.5 18.5q-67 0 -83 -75l-59 16q7 48 45.5 82t95.5 34q63 0 101.5 -33t38.5 -90q0 -35 -17.5 -61.5t-53.5 -60.5z" />
    <glyph glyph-name="three.numr" horiz-adv-x="350" 
d="M212 546q50 -6 80.5 -39.5t30.5 -82.5q0 -56 -40.5 -94.5t-103.5 -38.5q-68 0 -107.5 35t-48.5 85l57 16q6 -35 32.5 -58t64.5 -23q39 0 62 22.5t23 55.5t-22.5 55t-64.5 22q-8 0 -28 -2l-10 38l95 108h-197v55h274v-46z" />
    <glyph glyph-name="four.numr" horiz-adv-x="346" 
d="M328 440v-56h-52v-84h-60v84h-203v49l144 267h67l-139 -260h131v89h60v-89h52z" />
    <glyph glyph-name="five.numr" horiz-adv-x="343" 
d="M183 553q59 0 96 -36t37 -95q0 -57 -39.5 -94t-102.5 -37q-61 0 -100 31.5t-51 76.5l55 15q25 -68 94 -68q39 0 61 22t22 55q0 35 -22 56.5t-61 21.5q-52 0 -89 -27l-45 15l18 211h242v-55h-189l-10 -110q38 18 84 18z" />
    <glyph glyph-name="six.numr" horiz-adv-x="331" 
d="M176 565q60 -1 98.5 -40t38.5 -96q0 -59 -41 -98.5t-106 -39.5t-107 39.5t-41 98.5q0 43 31 91l119 180h67l-90 -138q10 3 31 3zM166 346q39 0 63 23.5t24 59.5q0 35 -24.5 58.5t-63.5 23.5q-38 0 -62 -23.5t-24 -58.5q0 -36 24 -59.5t63 -23.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="305" 
d="M11 700h282v-46l-155 -354h-67l151 345h-211v55z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="350" 
d="M255 517q72 -31 72 -105q0 -52 -41.5 -86.5t-109.5 -34.5q-69 0 -111 34.5t-42 86.5q0 74 73 105q-56 29 -56 87q0 45 37.5 75.5t98.5 30.5q59 0 96.5 -30.5t37.5 -75.5q0 -58 -55 -87zM176 657q-36 0 -56 -17t-20 -43t20 -43t56 -17q33 0 54 17t21 43q0 25 -21 42.5
t-54 17.5zM176 344q40 0 64 21t24 52t-24 51.5t-64 20.5q-41 0 -65.5 -20.5t-24.5 -51.5q0 -32 24.5 -52.5t65.5 -20.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="367" 
d="M183 710q65 0 107 -40t42 -99q-1 -44 -32 -91l-119 -180h-67l90 138q-14 -3 -30 -3q-60 1 -99 40t-39 96q0 59 41.5 99t105.5 40zM184 489q38 0 62 23.5t24 58.5q0 36 -24 60t-63 24q-38 0 -62.5 -24t-24.5 -60q0 -35 24.5 -58.5t63.5 -23.5z" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="E" u2="&#x153;" k="18" />
    <hkern u1="E" u2="&#x151;" k="18" />
    <hkern u1="E" u2="&#x14d;" k="18" />
    <hkern u1="E" u2="&#x123;" k="18" />
    <hkern u1="E" u2="&#x121;" k="18" />
    <hkern u1="E" u2="&#x11f;" k="18" />
    <hkern u1="E" u2="&#x11b;" k="18" />
    <hkern u1="E" u2="&#x119;" k="18" />
    <hkern u1="E" u2="&#x117;" k="18" />
    <hkern u1="E" u2="&#x113;" k="18" />
    <hkern u1="E" u2="&#x111;" k="18" />
    <hkern u1="E" u2="&#x10f;" k="18" />
    <hkern u1="E" u2="&#x10d;" k="18" />
    <hkern u1="E" u2="&#x10b;" k="18" />
    <hkern u1="E" u2="&#x107;" k="18" />
    <hkern u1="E" u2="&#x105;" k="18" />
    <hkern u1="E" u2="&#x103;" k="18" />
    <hkern u1="E" u2="&#x101;" k="18" />
    <hkern u1="E" u2="&#xf8;" k="18" />
    <hkern u1="E" u2="&#xf6;" k="18" />
    <hkern u1="E" u2="&#xf5;" k="18" />
    <hkern u1="E" u2="&#xf4;" k="18" />
    <hkern u1="E" u2="&#xf3;" k="18" />
    <hkern u1="E" u2="&#xf2;" k="18" />
    <hkern u1="E" u2="&#xeb;" k="18" />
    <hkern u1="E" u2="&#xea;" k="18" />
    <hkern u1="E" u2="&#xe9;" k="18" />
    <hkern u1="E" u2="&#xe8;" k="18" />
    <hkern u1="E" u2="&#xe7;" k="18" />
    <hkern u1="E" u2="&#xe6;" k="18" />
    <hkern u1="E" u2="&#xe5;" k="18" />
    <hkern u1="E" u2="&#xe4;" k="18" />
    <hkern u1="E" u2="&#xe3;" k="18" />
    <hkern u1="E" u2="&#xe2;" k="18" />
    <hkern u1="E" u2="&#xe1;" k="18" />
    <hkern u1="E" u2="&#xe0;" k="18" />
    <hkern u1="E" u2="q" k="18" />
    <hkern u1="E" u2="o" k="18" />
    <hkern u1="E" u2="g" k="18" />
    <hkern u1="E" u2="e" k="18" />
    <hkern u1="E" u2="d" k="18" />
    <hkern u1="E" u2="c" k="18" />
    <hkern u1="E" u2="a" k="18" />
    <hkern u1="F" u2="&#x153;" k="28" />
    <hkern u1="F" u2="&#x151;" k="28" />
    <hkern u1="F" u2="&#x14d;" k="28" />
    <hkern u1="F" u2="&#x123;" k="28" />
    <hkern u1="F" u2="&#x121;" k="28" />
    <hkern u1="F" u2="&#x11f;" k="28" />
    <hkern u1="F" u2="&#x11b;" k="28" />
    <hkern u1="F" u2="&#x119;" k="28" />
    <hkern u1="F" u2="&#x117;" k="28" />
    <hkern u1="F" u2="&#x113;" k="28" />
    <hkern u1="F" u2="&#x111;" k="28" />
    <hkern u1="F" u2="&#x10f;" k="28" />
    <hkern u1="F" u2="&#x10d;" k="28" />
    <hkern u1="F" u2="&#x10b;" k="28" />
    <hkern u1="F" u2="&#x107;" k="28" />
    <hkern u1="F" u2="&#x105;" k="28" />
    <hkern u1="F" u2="&#x103;" k="28" />
    <hkern u1="F" u2="&#x101;" k="28" />
    <hkern u1="F" u2="&#xf8;" k="28" />
    <hkern u1="F" u2="&#xf6;" k="28" />
    <hkern u1="F" u2="&#xf5;" k="28" />
    <hkern u1="F" u2="&#xf4;" k="28" />
    <hkern u1="F" u2="&#xf3;" k="28" />
    <hkern u1="F" u2="&#xf2;" k="28" />
    <hkern u1="F" u2="&#xeb;" k="28" />
    <hkern u1="F" u2="&#xea;" k="28" />
    <hkern u1="F" u2="&#xe9;" k="28" />
    <hkern u1="F" u2="&#xe8;" k="28" />
    <hkern u1="F" u2="&#xe7;" k="28" />
    <hkern u1="F" u2="&#xe6;" k="28" />
    <hkern u1="F" u2="&#xe5;" k="28" />
    <hkern u1="F" u2="&#xe4;" k="28" />
    <hkern u1="F" u2="&#xe3;" k="28" />
    <hkern u1="F" u2="&#xe2;" k="28" />
    <hkern u1="F" u2="&#xe1;" k="28" />
    <hkern u1="F" u2="&#xe0;" k="28" />
    <hkern u1="F" u2="q" k="28" />
    <hkern u1="F" u2="o" k="28" />
    <hkern u1="F" u2="g" k="28" />
    <hkern u1="F" u2="e" k="28" />
    <hkern u1="F" u2="d" k="28" />
    <hkern u1="F" u2="c" k="28" />
    <hkern u1="F" u2="a" k="28" />
    <hkern u1="K" u2="&#x153;" k="50" />
    <hkern u1="K" u2="&#x151;" k="50" />
    <hkern u1="K" u2="&#x14d;" k="50" />
    <hkern u1="K" u2="&#x123;" k="50" />
    <hkern u1="K" u2="&#x121;" k="50" />
    <hkern u1="K" u2="&#x11f;" k="50" />
    <hkern u1="K" u2="&#x11b;" k="50" />
    <hkern u1="K" u2="&#x119;" k="50" />
    <hkern u1="K" u2="&#x117;" k="50" />
    <hkern u1="K" u2="&#x113;" k="50" />
    <hkern u1="K" u2="&#x111;" k="50" />
    <hkern u1="K" u2="&#x10f;" k="50" />
    <hkern u1="K" u2="&#x10d;" k="50" />
    <hkern u1="K" u2="&#x10b;" k="50" />
    <hkern u1="K" u2="&#x107;" k="50" />
    <hkern u1="K" u2="&#x105;" k="50" />
    <hkern u1="K" u2="&#x103;" k="50" />
    <hkern u1="K" u2="&#x101;" k="50" />
    <hkern u1="K" u2="&#xf8;" k="50" />
    <hkern u1="K" u2="&#xf6;" k="50" />
    <hkern u1="K" u2="&#xf5;" k="50" />
    <hkern u1="K" u2="&#xf4;" k="50" />
    <hkern u1="K" u2="&#xf3;" k="50" />
    <hkern u1="K" u2="&#xf2;" k="50" />
    <hkern u1="K" u2="&#xeb;" k="50" />
    <hkern u1="K" u2="&#xea;" k="50" />
    <hkern u1="K" u2="&#xe9;" k="50" />
    <hkern u1="K" u2="&#xe8;" k="50" />
    <hkern u1="K" u2="&#xe7;" k="50" />
    <hkern u1="K" u2="&#xe6;" k="50" />
    <hkern u1="K" u2="&#xe5;" k="50" />
    <hkern u1="K" u2="&#xe4;" k="50" />
    <hkern u1="K" u2="&#xe3;" k="50" />
    <hkern u1="K" u2="&#xe2;" k="50" />
    <hkern u1="K" u2="&#xe1;" k="50" />
    <hkern u1="K" u2="&#xe0;" k="50" />
    <hkern u1="K" u2="q" k="50" />
    <hkern u1="K" u2="o" k="50" />
    <hkern u1="K" u2="g" k="50" />
    <hkern u1="K" u2="e" k="50" />
    <hkern u1="K" u2="d" k="50" />
    <hkern u1="K" u2="c" k="50" />
    <hkern u1="K" u2="a" k="50" />
    <hkern u1="L" u2="&#x153;" k="22" />
    <hkern u1="L" u2="&#x151;" k="22" />
    <hkern u1="L" u2="&#x14d;" k="22" />
    <hkern u1="L" u2="&#x123;" k="22" />
    <hkern u1="L" u2="&#x121;" k="22" />
    <hkern u1="L" u2="&#x11f;" k="22" />
    <hkern u1="L" u2="&#x11b;" k="22" />
    <hkern u1="L" u2="&#x119;" k="22" />
    <hkern u1="L" u2="&#x117;" k="22" />
    <hkern u1="L" u2="&#x113;" k="22" />
    <hkern u1="L" u2="&#x111;" k="22" />
    <hkern u1="L" u2="&#x10f;" k="22" />
    <hkern u1="L" u2="&#x10d;" k="22" />
    <hkern u1="L" u2="&#x10b;" k="22" />
    <hkern u1="L" u2="&#x107;" k="22" />
    <hkern u1="L" u2="&#x105;" k="22" />
    <hkern u1="L" u2="&#x103;" k="22" />
    <hkern u1="L" u2="&#x101;" k="22" />
    <hkern u1="L" u2="&#xf8;" k="22" />
    <hkern u1="L" u2="&#xf6;" k="22" />
    <hkern u1="L" u2="&#xf5;" k="22" />
    <hkern u1="L" u2="&#xf4;" k="22" />
    <hkern u1="L" u2="&#xf3;" k="22" />
    <hkern u1="L" u2="&#xf2;" k="22" />
    <hkern u1="L" u2="&#xeb;" k="22" />
    <hkern u1="L" u2="&#xea;" k="22" />
    <hkern u1="L" u2="&#xe9;" k="22" />
    <hkern u1="L" u2="&#xe8;" k="22" />
    <hkern u1="L" u2="&#xe7;" k="22" />
    <hkern u1="L" u2="&#xe6;" k="22" />
    <hkern u1="L" u2="&#xe5;" k="22" />
    <hkern u1="L" u2="&#xe4;" k="22" />
    <hkern u1="L" u2="&#xe3;" k="22" />
    <hkern u1="L" u2="&#xe2;" k="22" />
    <hkern u1="L" u2="&#xe1;" k="22" />
    <hkern u1="L" u2="&#xe0;" k="22" />
    <hkern u1="L" u2="q" k="22" />
    <hkern u1="L" u2="o" k="22" />
    <hkern u1="L" u2="g" k="22" />
    <hkern u1="L" u2="e" k="22" />
    <hkern u1="L" u2="d" k="22" />
    <hkern u1="L" u2="c" k="22" />
    <hkern u1="L" u2="a" k="22" />
    <hkern u1="P" u2="&#x153;" k="26" />
    <hkern u1="P" u2="&#x151;" k="26" />
    <hkern u1="P" u2="&#x14d;" k="26" />
    <hkern u1="P" u2="&#x123;" k="26" />
    <hkern u1="P" u2="&#x121;" k="26" />
    <hkern u1="P" u2="&#x11f;" k="26" />
    <hkern u1="P" u2="&#x11b;" k="26" />
    <hkern u1="P" u2="&#x119;" k="26" />
    <hkern u1="P" u2="&#x117;" k="26" />
    <hkern u1="P" u2="&#x113;" k="26" />
    <hkern u1="P" u2="&#x111;" k="26" />
    <hkern u1="P" u2="&#x10f;" k="26" />
    <hkern u1="P" u2="&#x10d;" k="26" />
    <hkern u1="P" u2="&#x10b;" k="26" />
    <hkern u1="P" u2="&#x107;" k="26" />
    <hkern u1="P" u2="&#x105;" k="26" />
    <hkern u1="P" u2="&#x103;" k="26" />
    <hkern u1="P" u2="&#x101;" k="26" />
    <hkern u1="P" u2="&#xf8;" k="26" />
    <hkern u1="P" u2="&#xf6;" k="26" />
    <hkern u1="P" u2="&#xf5;" k="26" />
    <hkern u1="P" u2="&#xf4;" k="26" />
    <hkern u1="P" u2="&#xf3;" k="26" />
    <hkern u1="P" u2="&#xf2;" k="26" />
    <hkern u1="P" u2="&#xeb;" k="26" />
    <hkern u1="P" u2="&#xea;" k="26" />
    <hkern u1="P" u2="&#xe9;" k="26" />
    <hkern u1="P" u2="&#xe8;" k="26" />
    <hkern u1="P" u2="&#xe7;" k="26" />
    <hkern u1="P" u2="&#xe6;" k="26" />
    <hkern u1="P" u2="&#xe5;" k="26" />
    <hkern u1="P" u2="&#xe4;" k="26" />
    <hkern u1="P" u2="&#xe3;" k="26" />
    <hkern u1="P" u2="&#xe2;" k="26" />
    <hkern u1="P" u2="&#xe1;" k="26" />
    <hkern u1="P" u2="&#xe0;" k="26" />
    <hkern u1="P" u2="q" k="26" />
    <hkern u1="P" u2="o" k="26" />
    <hkern u1="P" u2="g" k="26" />
    <hkern u1="P" u2="e" k="26" />
    <hkern u1="P" u2="d" k="26" />
    <hkern u1="P" u2="c" k="26" />
    <hkern u1="P" u2="a" k="26" />
    <hkern u1="Q" u2="&#x104;" k="15" />
    <hkern u1="Q" u2="&#x102;" k="15" />
    <hkern u1="Q" u2="&#x100;" k="15" />
    <hkern u1="Q" u2="&#xc6;" k="15" />
    <hkern u1="Q" u2="&#xc5;" k="15" />
    <hkern u1="Q" u2="&#xc4;" k="15" />
    <hkern u1="Q" u2="&#xc3;" k="15" />
    <hkern u1="Q" u2="&#xc2;" k="15" />
    <hkern u1="Q" u2="&#xc1;" k="15" />
    <hkern u1="Q" u2="&#xc0;" k="15" />
    <hkern u1="Q" u2="A" k="15" />
    <hkern u1="R" u2="&#x153;" k="20" />
    <hkern u1="R" u2="&#x151;" k="20" />
    <hkern u1="R" u2="&#x14d;" k="20" />
    <hkern u1="R" u2="&#x123;" k="20" />
    <hkern u1="R" u2="&#x121;" k="20" />
    <hkern u1="R" u2="&#x11f;" k="20" />
    <hkern u1="R" u2="&#x11b;" k="20" />
    <hkern u1="R" u2="&#x119;" k="20" />
    <hkern u1="R" u2="&#x117;" k="20" />
    <hkern u1="R" u2="&#x113;" k="20" />
    <hkern u1="R" u2="&#x111;" k="20" />
    <hkern u1="R" u2="&#x10f;" k="20" />
    <hkern u1="R" u2="&#x10d;" k="20" />
    <hkern u1="R" u2="&#x10b;" k="20" />
    <hkern u1="R" u2="&#x107;" k="20" />
    <hkern u1="R" u2="&#x105;" k="20" />
    <hkern u1="R" u2="&#x103;" k="20" />
    <hkern u1="R" u2="&#x101;" k="20" />
    <hkern u1="R" u2="&#xf8;" k="20" />
    <hkern u1="R" u2="&#xf6;" k="20" />
    <hkern u1="R" u2="&#xf5;" k="20" />
    <hkern u1="R" u2="&#xf4;" k="20" />
    <hkern u1="R" u2="&#xf3;" k="20" />
    <hkern u1="R" u2="&#xf2;" k="20" />
    <hkern u1="R" u2="&#xeb;" k="20" />
    <hkern u1="R" u2="&#xea;" k="20" />
    <hkern u1="R" u2="&#xe9;" k="20" />
    <hkern u1="R" u2="&#xe8;" k="20" />
    <hkern u1="R" u2="&#xe7;" k="20" />
    <hkern u1="R" u2="&#xe6;" k="20" />
    <hkern u1="R" u2="&#xe5;" k="20" />
    <hkern u1="R" u2="&#xe4;" k="20" />
    <hkern u1="R" u2="&#xe3;" k="20" />
    <hkern u1="R" u2="&#xe2;" k="20" />
    <hkern u1="R" u2="&#xe1;" k="20" />
    <hkern u1="R" u2="&#xe0;" k="20" />
    <hkern u1="R" u2="q" k="20" />
    <hkern u1="R" u2="o" k="20" />
    <hkern u1="R" u2="g" k="20" />
    <hkern u1="R" u2="e" k="20" />
    <hkern u1="R" u2="d" k="20" />
    <hkern u1="R" u2="c" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="&#x153;" k="108" />
    <hkern u1="T" u2="&#x151;" k="108" />
    <hkern u1="T" u2="&#x14d;" k="108" />
    <hkern u1="T" u2="&#x123;" k="108" />
    <hkern u1="T" u2="&#x121;" k="108" />
    <hkern u1="T" u2="&#x11f;" k="108" />
    <hkern u1="T" u2="&#x11b;" k="108" />
    <hkern u1="T" u2="&#x119;" k="108" />
    <hkern u1="T" u2="&#x117;" k="108" />
    <hkern u1="T" u2="&#x113;" k="108" />
    <hkern u1="T" u2="&#x111;" k="108" />
    <hkern u1="T" u2="&#x10f;" k="108" />
    <hkern u1="T" u2="&#x10d;" k="108" />
    <hkern u1="T" u2="&#x10b;" k="108" />
    <hkern u1="T" u2="&#x107;" k="108" />
    <hkern u1="T" u2="&#x105;" k="108" />
    <hkern u1="T" u2="&#x103;" k="108" />
    <hkern u1="T" u2="&#x101;" k="108" />
    <hkern u1="T" u2="&#xf8;" k="108" />
    <hkern u1="T" u2="&#xf6;" k="108" />
    <hkern u1="T" u2="&#xf5;" k="108" />
    <hkern u1="T" u2="&#xf4;" k="108" />
    <hkern u1="T" u2="&#xf3;" k="108" />
    <hkern u1="T" u2="&#xf2;" k="108" />
    <hkern u1="T" u2="&#xeb;" k="108" />
    <hkern u1="T" u2="&#xea;" k="108" />
    <hkern u1="T" u2="&#xe9;" k="108" />
    <hkern u1="T" u2="&#xe8;" k="108" />
    <hkern u1="T" u2="&#xe7;" k="108" />
    <hkern u1="T" u2="&#xe6;" k="108" />
    <hkern u1="T" u2="&#xe5;" k="108" />
    <hkern u1="T" u2="&#xe4;" k="108" />
    <hkern u1="T" u2="&#xe3;" k="108" />
    <hkern u1="T" u2="&#xe2;" k="108" />
    <hkern u1="T" u2="&#xe1;" k="108" />
    <hkern u1="T" u2="&#xe0;" k="108" />
    <hkern u1="T" u2="q" k="108" />
    <hkern u1="T" u2="o" k="108" />
    <hkern u1="T" u2="g" k="108" />
    <hkern u1="T" u2="e" k="108" />
    <hkern u1="T" u2="d" k="108" />
    <hkern u1="T" u2="c" k="108" />
    <hkern u1="T" u2="a" k="108" />
    <hkern u1="W" u2="&#x153;" k="54" />
    <hkern u1="W" u2="&#x151;" k="54" />
    <hkern u1="W" u2="&#x14d;" k="54" />
    <hkern u1="W" u2="&#x123;" k="54" />
    <hkern u1="W" u2="&#x121;" k="54" />
    <hkern u1="W" u2="&#x11f;" k="54" />
    <hkern u1="W" u2="&#x11b;" k="54" />
    <hkern u1="W" u2="&#x119;" k="54" />
    <hkern u1="W" u2="&#x117;" k="54" />
    <hkern u1="W" u2="&#x113;" k="54" />
    <hkern u1="W" u2="&#x111;" k="54" />
    <hkern u1="W" u2="&#x10f;" k="54" />
    <hkern u1="W" u2="&#x10d;" k="54" />
    <hkern u1="W" u2="&#x10b;" k="54" />
    <hkern u1="W" u2="&#x107;" k="54" />
    <hkern u1="W" u2="&#x105;" k="54" />
    <hkern u1="W" u2="&#x103;" k="54" />
    <hkern u1="W" u2="&#x101;" k="54" />
    <hkern u1="W" u2="&#xf8;" k="54" />
    <hkern u1="W" u2="&#xf6;" k="54" />
    <hkern u1="W" u2="&#xf5;" k="54" />
    <hkern u1="W" u2="&#xf4;" k="54" />
    <hkern u1="W" u2="&#xf3;" k="54" />
    <hkern u1="W" u2="&#xf2;" k="54" />
    <hkern u1="W" u2="&#xeb;" k="54" />
    <hkern u1="W" u2="&#xea;" k="54" />
    <hkern u1="W" u2="&#xe9;" k="54" />
    <hkern u1="W" u2="&#xe8;" k="54" />
    <hkern u1="W" u2="&#xe7;" k="54" />
    <hkern u1="W" u2="&#xe6;" k="54" />
    <hkern u1="W" u2="&#xe5;" k="54" />
    <hkern u1="W" u2="&#xe4;" k="54" />
    <hkern u1="W" u2="&#xe3;" k="54" />
    <hkern u1="W" u2="&#xe2;" k="54" />
    <hkern u1="W" u2="&#xe1;" k="54" />
    <hkern u1="W" u2="&#xe0;" k="54" />
    <hkern u1="W" u2="q" k="54" />
    <hkern u1="W" u2="o" k="54" />
    <hkern u1="W" u2="g" k="54" />
    <hkern u1="W" u2="e" k="54" />
    <hkern u1="W" u2="d" k="54" />
    <hkern u1="W" u2="c" k="54" />
    <hkern u1="W" u2="a" k="54" />
    <hkern u1="X" u2="&#x153;" k="35" />
    <hkern u1="X" u2="&#x151;" k="35" />
    <hkern u1="X" u2="&#x14d;" k="35" />
    <hkern u1="X" u2="&#x123;" k="35" />
    <hkern u1="X" u2="&#x121;" k="35" />
    <hkern u1="X" u2="&#x11f;" k="35" />
    <hkern u1="X" u2="&#x11b;" k="35" />
    <hkern u1="X" u2="&#x119;" k="35" />
    <hkern u1="X" u2="&#x117;" k="35" />
    <hkern u1="X" u2="&#x113;" k="35" />
    <hkern u1="X" u2="&#x111;" k="35" />
    <hkern u1="X" u2="&#x10f;" k="35" />
    <hkern u1="X" u2="&#x10d;" k="35" />
    <hkern u1="X" u2="&#x10b;" k="35" />
    <hkern u1="X" u2="&#x107;" k="35" />
    <hkern u1="X" u2="&#x105;" k="35" />
    <hkern u1="X" u2="&#x103;" k="35" />
    <hkern u1="X" u2="&#x101;" k="35" />
    <hkern u1="X" u2="&#xf8;" k="35" />
    <hkern u1="X" u2="&#xf6;" k="35" />
    <hkern u1="X" u2="&#xf5;" k="35" />
    <hkern u1="X" u2="&#xf4;" k="35" />
    <hkern u1="X" u2="&#xf3;" k="35" />
    <hkern u1="X" u2="&#xf2;" k="35" />
    <hkern u1="X" u2="&#xeb;" k="35" />
    <hkern u1="X" u2="&#xea;" k="35" />
    <hkern u1="X" u2="&#xe9;" k="35" />
    <hkern u1="X" u2="&#xe8;" k="35" />
    <hkern u1="X" u2="&#xe7;" k="35" />
    <hkern u1="X" u2="&#xe6;" k="35" />
    <hkern u1="X" u2="&#xe5;" k="35" />
    <hkern u1="X" u2="&#xe4;" k="35" />
    <hkern u1="X" u2="&#xe3;" k="35" />
    <hkern u1="X" u2="&#xe2;" k="35" />
    <hkern u1="X" u2="&#xe1;" k="35" />
    <hkern u1="X" u2="&#xe0;" k="35" />
    <hkern u1="X" u2="q" k="35" />
    <hkern u1="X" u2="o" k="35" />
    <hkern u1="X" u2="g" k="35" />
    <hkern u1="X" u2="e" k="35" />
    <hkern u1="X" u2="d" k="35" />
    <hkern u1="X" u2="c" k="35" />
    <hkern u1="X" u2="a" k="35" />
    <hkern u1="b" u2="Y" k="95" />
    <hkern u1="b" u2="X" k="35" />
    <hkern u1="b" u2="W" k="54" />
    <hkern u1="e" u2="Y" k="95" />
    <hkern u1="e" u2="X" k="35" />
    <hkern u1="e" u2="W" k="54" />
    <hkern u1="k" u2="&#x153;" k="30" />
    <hkern u1="k" u2="&#x151;" k="30" />
    <hkern u1="k" u2="&#x14d;" k="30" />
    <hkern u1="k" u2="&#x123;" k="30" />
    <hkern u1="k" u2="&#x121;" k="30" />
    <hkern u1="k" u2="&#x11f;" k="30" />
    <hkern u1="k" u2="&#x11b;" k="30" />
    <hkern u1="k" u2="&#x119;" k="30" />
    <hkern u1="k" u2="&#x117;" k="30" />
    <hkern u1="k" u2="&#x113;" k="30" />
    <hkern u1="k" u2="&#x111;" k="30" />
    <hkern u1="k" u2="&#x10f;" k="30" />
    <hkern u1="k" u2="&#x10d;" k="30" />
    <hkern u1="k" u2="&#x10b;" k="30" />
    <hkern u1="k" u2="&#x107;" k="30" />
    <hkern u1="k" u2="&#x105;" k="30" />
    <hkern u1="k" u2="&#x103;" k="30" />
    <hkern u1="k" u2="&#x101;" k="30" />
    <hkern u1="k" u2="&#xf8;" k="30" />
    <hkern u1="k" u2="&#xf6;" k="30" />
    <hkern u1="k" u2="&#xf5;" k="30" />
    <hkern u1="k" u2="&#xf4;" k="30" />
    <hkern u1="k" u2="&#xf3;" k="30" />
    <hkern u1="k" u2="&#xf2;" k="30" />
    <hkern u1="k" u2="&#xeb;" k="30" />
    <hkern u1="k" u2="&#xea;" k="30" />
    <hkern u1="k" u2="&#xe9;" k="30" />
    <hkern u1="k" u2="&#xe8;" k="30" />
    <hkern u1="k" u2="&#xe7;" k="30" />
    <hkern u1="k" u2="&#xe6;" k="30" />
    <hkern u1="k" u2="&#xe5;" k="30" />
    <hkern u1="k" u2="&#xe4;" k="30" />
    <hkern u1="k" u2="&#xe3;" k="30" />
    <hkern u1="k" u2="&#xe2;" k="30" />
    <hkern u1="k" u2="&#xe1;" k="30" />
    <hkern u1="k" u2="&#xe0;" k="30" />
    <hkern u1="k" u2="q" k="30" />
    <hkern u1="k" u2="o" k="30" />
    <hkern u1="k" u2="g" k="30" />
    <hkern u1="k" u2="e" k="30" />
    <hkern u1="k" u2="d" k="30" />
    <hkern u1="k" u2="c" k="30" />
    <hkern u1="k" u2="a" k="30" />
    <hkern u1="o" u2="Y" k="95" />
    <hkern u1="o" u2="X" k="35" />
    <hkern u1="o" u2="W" k="54" />
    <hkern u1="p" u2="Y" k="95" />
    <hkern u1="p" u2="X" k="35" />
    <hkern u1="p" u2="W" k="54" />
    <hkern u1="&#xc0;" u2="f" k="20" />
    <hkern u1="&#xc1;" u2="f" k="20" />
    <hkern u1="&#xc2;" u2="f" k="20" />
    <hkern u1="&#xc3;" u2="f" k="20" />
    <hkern u1="&#xc4;" u2="f" k="20" />
    <hkern u1="&#xc5;" u2="f" k="20" />
    <hkern u1="&#xe6;" u2="Y" k="95" />
    <hkern u1="&#xe6;" u2="X" k="35" />
    <hkern u1="&#xe6;" u2="W" k="54" />
    <hkern u1="&#xe8;" u2="Y" k="95" />
    <hkern u1="&#xe8;" u2="X" k="35" />
    <hkern u1="&#xe8;" u2="W" k="54" />
    <hkern u1="&#xe9;" u2="Y" k="95" />
    <hkern u1="&#xe9;" u2="X" k="35" />
    <hkern u1="&#xe9;" u2="W" k="54" />
    <hkern u1="&#xea;" u2="Y" k="95" />
    <hkern u1="&#xea;" u2="X" k="35" />
    <hkern u1="&#xea;" u2="W" k="54" />
    <hkern u1="&#xeb;" u2="Y" k="95" />
    <hkern u1="&#xeb;" u2="X" k="35" />
    <hkern u1="&#xeb;" u2="W" k="54" />
    <hkern u1="&#xf2;" u2="Y" k="95" />
    <hkern u1="&#xf2;" u2="X" k="35" />
    <hkern u1="&#xf2;" u2="W" k="54" />
    <hkern u1="&#xf3;" u2="Y" k="95" />
    <hkern u1="&#xf3;" u2="X" k="35" />
    <hkern u1="&#xf3;" u2="W" k="54" />
    <hkern u1="&#xf4;" u2="Y" k="95" />
    <hkern u1="&#xf4;" u2="X" k="35" />
    <hkern u1="&#xf4;" u2="W" k="54" />
    <hkern u1="&#xf5;" u2="Y" k="95" />
    <hkern u1="&#xf5;" u2="X" k="35" />
    <hkern u1="&#xf5;" u2="W" k="54" />
    <hkern u1="&#xf6;" u2="Y" k="95" />
    <hkern u1="&#xf6;" u2="X" k="35" />
    <hkern u1="&#xf6;" u2="W" k="54" />
    <hkern u1="&#xf8;" u2="Y" k="95" />
    <hkern u1="&#xf8;" u2="X" k="35" />
    <hkern u1="&#xf8;" u2="W" k="54" />
    <hkern u1="&#xfe;" u2="Y" k="95" />
    <hkern u1="&#xfe;" u2="X" k="35" />
    <hkern u1="&#xfe;" u2="W" k="54" />
    <hkern u1="&#x100;" u2="f" k="20" />
    <hkern u1="&#x102;" u2="f" k="20" />
    <hkern u1="&#x104;" u2="f" k="20" />
    <hkern u1="&#x113;" u2="Y" k="95" />
    <hkern u1="&#x113;" u2="X" k="35" />
    <hkern u1="&#x113;" u2="W" k="54" />
    <hkern u1="&#x117;" u2="Y" k="95" />
    <hkern u1="&#x117;" u2="X" k="35" />
    <hkern u1="&#x117;" u2="W" k="54" />
    <hkern u1="&#x119;" u2="Y" k="95" />
    <hkern u1="&#x119;" u2="X" k="35" />
    <hkern u1="&#x119;" u2="W" k="54" />
    <hkern u1="&#x11b;" u2="Y" k="95" />
    <hkern u1="&#x11b;" u2="X" k="35" />
    <hkern u1="&#x11b;" u2="W" k="54" />
    <hkern u1="&#x14d;" u2="Y" k="95" />
    <hkern u1="&#x14d;" u2="X" k="35" />
    <hkern u1="&#x14d;" u2="W" k="54" />
    <hkern u1="&#x151;" u2="Y" k="95" />
    <hkern u1="&#x151;" u2="X" k="35" />
    <hkern u1="&#x151;" u2="W" k="54" />
    <hkern u1="&#x153;" u2="Y" k="95" />
    <hkern u1="&#x153;" u2="X" k="35" />
    <hkern u1="&#x153;" u2="W" k="54" />
    <hkern g1="at"
	g2="four"
	k="10" />
    <hkern g1="at"
	g2="six"
	k="20" />
    <hkern g1="at"
	g2="two"
	k="10" />
    <hkern g1="at"
	g2="zero"
	k="40" />
    <hkern g1="at"
	g2="eight"
	k="20" />
    <hkern g1="uni20BF"
	g2="nine"
	k="10" />
    <hkern g1="uni20BF"
	g2="one"
	k="10" />
    <hkern g1="uni20BF"
	g2="three"
	k="10" />
    <hkern g1="cent"
	g2="five"
	k="20" />
    <hkern g1="cent"
	g2="two"
	k="20" />
    <hkern g1="copyright,registered"
	g2="five"
	k="10" />
    <hkern g1="copyright,registered"
	g2="four"
	k="20" />
    <hkern g1="copyright,registered"
	g2="six"
	k="10" />
    <hkern g1="copyright,registered"
	g2="three"
	k="10" />
    <hkern g1="copyright,registered"
	g2="eight"
	k="10" />
    <hkern g1="currency,emptyset"
	g2="five"
	k="30" />
    <hkern g1="currency,emptyset"
	g2="seven"
	k="28" />
    <hkern g1="currency,emptyset"
	g2="six"
	k="8" />
    <hkern g1="currency,emptyset"
	g2="zero"
	k="43" />
    <hkern g1="currency,emptyset"
	g2="eight"
	k="8" />
    <hkern g1="dollar"
	g2="five"
	k="10" />
    <hkern g1="dollar"
	g2="nine"
	k="15" />
    <hkern g1="dollar"
	g2="one"
	k="8" />
    <hkern g1="dollar"
	g2="six"
	k="10" />
    <hkern g1="dollar"
	g2="eight"
	k="20" />
    <hkern g1="Euro"
	g2="five"
	k="10" />
    <hkern g1="Euro"
	g2="seven"
	k="10" />
    <hkern g1="franc"
	g2="five"
	k="50" />
    <hkern g1="franc"
	g2="nine"
	k="20" />
    <hkern g1="franc"
	g2="one"
	k="13" />
    <hkern g1="franc"
	g2="zero"
	k="40" />
    <hkern g1="franc"
	g2="eight"
	k="-20" />
    <hkern g1="lira"
	g2="seven"
	k="20" />
    <hkern g1="lira"
	g2="six"
	k="10" />
    <hkern g1="lira"
	g2="zero"
	k="10" />
    <hkern g1="paragraph"
	g2="five"
	k="15" />
    <hkern g1="paragraph"
	g2="four"
	k="8" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="two"
	k="10" />
    <hkern g1="paragraph"
	g2="zero"
	k="8" />
    <hkern g1="paragraph"
	g2="eight"
	k="20" />
    <hkern g1="percent,perthousand"
	g2="five"
	k="-3009" />
    <hkern g1="backslash"
	g2="eight"
	k="20" />
    <hkern g1="backslash"
	g2="seven"
	k="30" />
    <hkern g1="backslash"
	g2="six"
	k="20" />
    <hkern g1="backslash"
	g2="three"
	k="80" />
    <hkern g1="backslash"
	g2="five"
	k="40" />
    <hkern g1="braceleft"
	g2="four"
	k="10" />
    <hkern g1="braceleft"
	g2="two"
	k="20" />
    <hkern g1="braceright"
	g2="one"
	k="10" />
    <hkern g1="braceright"
	g2="zero"
	k="-25" />
    <hkern g1="braceright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="bracketleft"
	g2="five"
	k="25" />
    <hkern g1="bracketleft"
	g2="nine"
	k="10" />
    <hkern g1="bracketleft"
	g2="asterisk,degree,trademark"
	k="65" />
    <hkern g1="bracketleft"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="bracketleft"
	g2="two"
	k="20" />
    <hkern g1="bracketleft"
	g2="space"
	k="55" />
    <hkern g1="bracketleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="four"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="three"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="two"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="four"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="three"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="zero"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="eight"
	k="3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="four"
	k="38" />
    <hkern g1="guillemotright,guilsinglright"
	g2="five"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="one"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="zero"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="asterisk,degree,trademark"
	k="29" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="numbersign"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="parenleft"
	g2="eight"
	k="-20" />
    <hkern g1="parenleft"
	g2="four"
	k="-20" />
    <hkern g1="parenleft"
	g2="seven"
	k="30" />
    <hkern g1="parenleft"
	g2="six"
	k="-20" />
    <hkern g1="parenleft"
	g2="five"
	k="-20" />
    <hkern g1="parenleft"
	g2="nine"
	k="-20" />
    <hkern g1="parenleft"
	g2="one"
	k="60" />
    <hkern g1="parenleft"
	g2="two"
	k="10" />
    <hkern g1="parenright"
	g2="eight"
	k="30" />
    <hkern g1="parenright"
	g2="four"
	k="20" />
    <hkern g1="parenright"
	g2="asterisk,degree,trademark"
	k="53" />
    <hkern g1="parenright"
	g2="space"
	k="20" />
    <hkern g1="parenright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="eight"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="four"
	k="100" />
    <hkern g1="periodcentered,bullet"
	g2="seven"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="two"
	k="-60" />
    <hkern g1="periodcentered,bullet"
	g2="space"
	k="50" />
    <hkern g1="periodcentered,bullet"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="question"
	g2="three"
	k="80" />
    <hkern g1="question"
	g2="nine"
	k="30" />
    <hkern g1="question"
	g2="one"
	k="100" />
    <hkern g1="question"
	g2="zero"
	k="-10" />
    <hkern g1="question"
	g2="asterisk,degree,trademark"
	k="80" />
    <hkern g1="question"
	g2="space"
	k="10" />
    <hkern g1="question"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="questiondown"
	g2="six"
	k="30" />
    <hkern g1="questiondown"
	g2="three"
	k="30" />
    <hkern g1="questiondown"
	g2="five"
	k="10" />
    <hkern g1="questiondown"
	g2="nine"
	k="40" />
    <hkern g1="questiondown"
	g2="one"
	k="40" />
    <hkern g1="questiondown"
	g2="zero"
	k="40" />
    <hkern g1="questiondown"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="questiondown"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="100" />
    <hkern g1="questiondown"
	g2="two"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five"
	k="-4337" />
    <hkern g1="five"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-20" />
    <hkern g1="five"
	g2="V"
	k="23" />
    <hkern g1="five"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="five"
	g2="X"
	k="8" />
    <hkern g1="five"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="8" />
    <hkern g1="five"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="five"
	g2="backslash"
	k="10" />
    <hkern g1="five"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="five"
	g2="five"
	k="10" />
    <hkern g1="five"
	g2="Euro"
	k="15" />
    <hkern g1="five"
	g2="hbar"
	k="10" />
    <hkern g1="five"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="five"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="five"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="23" />
    <hkern g1="five"
	g2="copyright,registered"
	k="8" />
    <hkern g1="four"
	g2="parenright"
	k="15" />
    <hkern g1="four"
	g2="Hbar"
	k="10" />
    <hkern g1="four"
	g2="Tbar"
	k="10" />
    <hkern g1="four"
	g2="at"
	k="10" />
    <hkern g1="four"
	g2="uni20BF"
	k="23" />
    <hkern g1="four"
	g2="braceright"
	k="8" />
    <hkern g1="four"
	g2="cent"
	k="10" />
    <hkern g1="four"
	g2="Euro"
	k="-20" />
    <hkern g1="four"
	g2="hbar"
	k="25" />
    <hkern g1="four"
	g2="lira"
	k="15" />
    <hkern g1="four"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="four"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="four"
	g2="zero"
	k="10" />
    <hkern g1="four"
	g2="dollar"
	k="10" />
    <hkern g1="nine"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="nine"
	g2="ampersand"
	k="10" />
    <hkern g1="nine"
	g2="eth"
	k="10" />
    <hkern g1="nine"
	g2="florin"
	k="10" />
    <hkern g1="nine"
	g2="four"
	k="23" />
    <hkern g1="nine"
	g2="radical"
	k="8" />
    <hkern g1="nine"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="nine"
	g2="parenright"
	k="-20" />
    <hkern g1="nine"
	g2="Hbar"
	k="25" />
    <hkern g1="nine"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="15" />
    <hkern g1="nine"
	g2="at"
	k="10" />
    <hkern g1="nine"
	g2="uni20BF"
	k="30" />
    <hkern g1="nine"
	g2="braceleft"
	k="10" />
    <hkern g1="nine"
	g2="bracketright"
	k="10" />
    <hkern g1="nine"
	g2="cent"
	k="10" />
    <hkern g1="nine"
	g2="Euro"
	k="18" />
    <hkern g1="nine"
	g2="hbar"
	k="-10" />
    <hkern g1="nine"
	g2="lira"
	k="10" />
    <hkern g1="nine"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="nine"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="nine"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="one"
	g2="x"
	k="15" />
    <hkern g1="one"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="one"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="one"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="one"
	g2="paragraph"
	k="23" />
    <hkern g1="one"
	g2="questiondown"
	k="8" />
    <hkern g1="one"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="one"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-20" />
    <hkern g1="one"
	g2="ampersand"
	k="25" />
    <hkern g1="one"
	g2="eight"
	k="15" />
    <hkern g1="one"
	g2="florin"
	k="10" />
    <hkern g1="one"
	g2="four"
	k="30" />
    <hkern g1="one"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="one"
	g2="six"
	k="10" />
    <hkern g1="one"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="one"
	g2="parenright"
	k="18" />
    <hkern g1="one"
	g2="Hbar"
	k="-10" />
    <hkern g1="one"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="one"
	g2="Tbar"
	k="10" />
    <hkern g1="one"
	g2="at"
	k="30" />
    <hkern g1="one"
	g2="uni20BF"
	k="10" />
    <hkern g1="seven"
	g2="underscore"
	k="15" />
    <hkern g1="seven"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="seven"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="seven"
	g2="nine"
	k="23" />
    <hkern g1="seven"
	g2="percent,perthousand"
	k="8" />
    <hkern g1="seven"
	g2="slash"
	k="10" />
    <hkern g1="seven"
	g2="x"
	k="-20" />
    <hkern g1="seven"
	g2="T,uni0162,Tcaron,uni021A"
	k="25" />
    <hkern g1="seven"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="seven"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="seven"
	g2="paragraph"
	k="30" />
    <hkern g1="seven"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="seven"
	g2="J"
	k="10" />
    <hkern g1="seven"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="seven"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="18" />
    <hkern g1="seven"
	g2="ampersand"
	k="-10" />
    <hkern g1="seven"
	g2="eight"
	k="10" />
    <hkern g1="seven"
	g2="eth"
	k="10" />
    <hkern g1="seven"
	g2="florin"
	k="30" />
    <hkern g1="seven"
	g2="four"
	k="10" />
    <hkern g1="six"
	g2="asterisk,degree,trademark"
	k="15" />
    <hkern g1="six"
	g2="backslash"
	k="10" />
    <hkern g1="six"
	g2="five"
	k="10" />
    <hkern g1="six"
	g2="one"
	k="10" />
    <hkern g1="six"
	g2="periodcentered,bullet"
	k="23" />
    <hkern g1="six"
	g2="seven"
	k="8" />
    <hkern g1="six"
	g2="two"
	k="10" />
    <hkern g1="six"
	g2="underscore"
	k="-20" />
    <hkern g1="six"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="25" />
    <hkern g1="six"
	g2="yen"
	k="15" />
    <hkern g1="six"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="six"
	g2="nine"
	k="30" />
    <hkern g1="six"
	g2="numbersign"
	k="10" />
    <hkern g1="six"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="six"
	g2="slash"
	k="10" />
    <hkern g1="six"
	g2="x"
	k="18" />
    <hkern g1="six"
	g2="T,uni0162,Tcaron,uni021A"
	k="-10" />
    <hkern g1="six"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="six"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="six"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="six"
	g2="paragraph"
	k="10" />
    <hkern g1="three"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="23" />
    <hkern g1="three"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="8" />
    <hkern g1="three"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="three"
	g2="asterisk,degree,trademark"
	k="-20" />
    <hkern g1="three"
	g2="backslash"
	k="25" />
    <hkern g1="three"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="15" />
    <hkern g1="three"
	g2="one"
	k="10" />
    <hkern g1="three"
	g2="periodcentered,bullet"
	k="30" />
    <hkern g1="three"
	g2="question"
	k="10" />
    <hkern g1="three"
	g2="three"
	k="10" />
    <hkern g1="three"
	g2="two"
	k="10" />
    <hkern g1="three"
	g2="underscore"
	k="18" />
    <hkern g1="three"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="three"
	g2="yen"
	k="10" />
    <hkern g1="three"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="three"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="three"
	g2="nine"
	k="10" />
    <hkern g1="three"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="8" />
    <hkern g1="three"
	g2="zero"
	k="50" />
    <hkern g1="three"
	g2="copyright,registered"
	k="30" />
    <hkern g1="three"
	g2="dollar"
	k="10" />
    <hkern g1="two"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="two"
	g2="V"
	k="10" />
    <hkern g1="two"
	g2="X"
	k="10" />
    <hkern g1="two"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="two"
	g2="asterisk,degree,trademark"
	k="18" />
    <hkern g1="two"
	g2="backslash"
	k="-10" />
    <hkern g1="two"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="two"
	g2="five"
	k="10" />
    <hkern g1="two"
	g2="one"
	k="30" />
    <hkern g1="two"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="two"
	g2="uni20BF"
	k="8" />
    <hkern g1="two"
	g2="braceleft"
	k="50" />
    <hkern g1="two"
	g2="braceright"
	k="30" />
    <hkern g1="two"
	g2="bracketright"
	k="10" />
    <hkern g1="two"
	g2="cent"
	k="70" />
    <hkern g1="two"
	g2="Euro"
	k="95" />
    <hkern g1="two"
	g2="hbar"
	k="80" />
    <hkern g1="two"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="two"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="-20" />
    <hkern g1="two"
	g2="zero"
	k="58" />
    <hkern g1="two"
	g2="copyright,registered"
	k="45" />
    <hkern g1="zero"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="zero"
	g2="four"
	k="8" />
    <hkern g1="zero"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="50" />
    <hkern g1="zero"
	g2="radical"
	k="30" />
    <hkern g1="zero"
	g2="six"
	k="10" />
    <hkern g1="zero"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="70" />
    <hkern g1="zero"
	g2="parenright"
	k="95" />
    <hkern g1="zero"
	g2="Hbar"
	k="80" />
    <hkern g1="zero"
	g2="at"
	k="40" />
    <hkern g1="zero"
	g2="uni20BF"
	k="-20" />
    <hkern g1="zero"
	g2="braceleft"
	k="58" />
    <hkern g1="zero"
	g2="braceright"
	k="45" />
    <hkern g1="zero"
	g2="hbar"
	k="20" />
    <hkern g1="zero"
	g2="lira"
	k="10" />
    <hkern g1="zero"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="zero"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="28" />
    <hkern g1="zero"
	g2="zero"
	k="-10" />
    <hkern g1="zero"
	g2="copyright,registered"
	k="50" />
    <hkern g1="zero"
	g2="dollar"
	k="40" />
    <hkern g1="B,germandbls"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="B,germandbls"
	g2="J"
	k="30" />
    <hkern g1="B,germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="35" />
    <hkern g1="B,germandbls"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="50" />
    <hkern g1="B,germandbls"
	g2="T,uni0162,Tcaron,uni021A"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="Tbar"
	k="11" />
    <hkern g1="B,germandbls"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="65" />
    <hkern g1="B,germandbls"
	g2="V"
	k="35" />
    <hkern g1="B,germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,germandbls"
	g2="X"
	k="60" />
    <hkern g1="B,germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="85" />
    <hkern g1="B,germandbls"
	g2="ampersand"
	k="-20" />
    <hkern g1="B,germandbls"
	g2="asterisk,degree,trademark"
	k="80" />
    <hkern g1="B,germandbls"
	g2="at"
	k="20" />
    <hkern g1="B,germandbls"
	g2="backslash"
	k="38" />
    <hkern g1="B,germandbls"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="B,germandbls"
	g2="copyright,registered"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eight"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eth"
	k="10" />
    <hkern g1="B,germandbls"
	g2="five"
	k="33" />
    <hkern g1="B,germandbls"
	g2="four"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="guillemotleft,guilsinglleft"
	k="27" />
    <hkern g1="B,germandbls"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="8" />
    <hkern g1="B,germandbls"
	g2="underscore"
	k="10" />
    <hkern g1="B,germandbls"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="B,germandbls"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="9" />
    <hkern g1="B,germandbls"
	g2="colon,semicolon"
	k="23" />
    <hkern g1="B,germandbls"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="18" />
    <hkern g1="B,germandbls"
	g2="braceright"
	k="22" />
    <hkern g1="B,germandbls"
	g2="exclam,exclamdown"
	k="20" />
    <hkern g1="B,germandbls"
	g2="parenright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="30" />
    <hkern g1="B,germandbls"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="4" />
    <hkern g1="B,germandbls"
	g2="Hbar"
	k="20" />
    <hkern g1="B,germandbls"
	g2="bracketright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Eth,Dcroat"
	k="27" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="nine"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="numbersign"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="one"
	k="9" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="paragraph"
	k="23" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="periodcentered,bullet"
	k="18" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="question"
	k="22" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="questiondown"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="section"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="seven"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="six"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="three"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="slash"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="parenleft"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="Tbar"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="V"
	k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="22" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="ampersand"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="asterisk,degree,trademark"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="at"
	k="30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="backslash"
	k="4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eight"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eth"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="section"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="seven"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="six"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="underscore"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="F"
	g2="Tbar"
	k="10" />
    <hkern g1="F"
	g2="X"
	k="10" />
    <hkern g1="F"
	g2="at"
	k="20" />
    <hkern g1="F"
	g2="backslash"
	k="20" />
    <hkern g1="F"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="F"
	g2="five"
	k="8" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="F"
	g2="numbersign"
	k="10" />
    <hkern g1="F"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="5" />
    <hkern g1="F"
	g2="one"
	k="15" />
    <hkern g1="F"
	g2="paragraph"
	k="40" />
    <hkern g1="F"
	g2="periodcentered,bullet"
	k="20" />
    <hkern g1="F"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="F"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="F"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="F"
	g2="slash"
	k="10" />
    <hkern g1="F"
	g2="braceright"
	k="30" />
    <hkern g1="F"
	g2="exclam,exclamdown"
	k="20" />
    <hkern g1="F"
	g2="parenright"
	k="5" />
    <hkern g1="F"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="19" />
    <hkern g1="F"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="15" />
    <hkern g1="F"
	g2="Hbar"
	k="25" />
    <hkern g1="F"
	g2="two"
	k="40" />
    <hkern g1="F"
	g2="bracketright"
	k="8" />
    <hkern g1="F"
	g2="hbar"
	k="15" />
    <hkern g1="F"
	g2="j"
	k="40" />
    <hkern g1="F"
	g2="braceleft"
	k="28" />
    <hkern g1="Hbar"
	g2="J"
	k="15" />
    <hkern g1="Hbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Hbar"
	g2="Tbar"
	k="5" />
    <hkern g1="Hbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="Hbar"
	g2="V"
	k="40" />
    <hkern g1="Hbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="Hbar"
	g2="one"
	k="10" />
    <hkern g1="Hbar"
	g2="paragraph"
	k="20" />
    <hkern g1="Hbar"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="Hbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="Hbar"
	g2="question"
	k="30" />
    <hkern g1="Hbar"
	g2="questiondown"
	k="20" />
    <hkern g1="Hbar"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="Hbar"
	g2="section"
	k="19" />
    <hkern g1="Hbar"
	g2="seven"
	k="15" />
    <hkern g1="Hbar"
	g2="six"
	k="25" />
    <hkern g1="Hbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="Hbar"
	g2="three"
	k="8" />
    <hkern g1="Hbar"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="Hbar"
	g2="x"
	k="40" />
    <hkern g1="Hbar"
	g2="zero"
	k="28" />
    <hkern g1="Hbar"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="Hbar"
	g2="two"
	k="15" />
    <hkern g1="Hbar"
	g2="bracketright"
	k="40" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="K,uni0136"
	g2="V"
	k="20" />
    <hkern g1="K,uni0136"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="K,uni0136"
	g2="X"
	k="10" />
    <hkern g1="K,uni0136"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="K,uni0136"
	g2="ampersand"
	k="20" />
    <hkern g1="K,uni0136"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="K,uni0136"
	g2="at"
	k="19" />
    <hkern g1="K,uni0136"
	g2="backslash"
	k="15" />
    <hkern g1="K,uni0136"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="K,uni0136"
	g2="copyright,registered"
	k="40" />
    <hkern g1="K,uni0136"
	g2="eight"
	k="8" />
    <hkern g1="K,uni0136"
	g2="eth"
	k="15" />
    <hkern g1="K,uni0136"
	g2="four"
	k="40" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="K,uni0136"
	g2="questiondown"
	k="10" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="15" />
    <hkern g1="K,uni0136"
	g2="three"
	k="40" />
    <hkern g1="K,uni0136"
	g2="underscore"
	k="15" />
    <hkern g1="K,uni0136"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="14" />
    <hkern g1="K,uni0136"
	g2="braceright"
	k="30" />
    <hkern g1="K,uni0136"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="K,uni0136"
	g2="parenright"
	k="5" />
    <hkern g1="K,uni0136"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="K,uni0136"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="8" />
    <hkern g1="K,uni0136"
	g2="Hbar"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Eth,Dcroat"
	k="28" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="ampersand"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="copyright,registered"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="eight"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="paragraph"
	k="25" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="periodcentered,bullet"
	k="14" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="questiondown"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="section"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="seven"
	k="8" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="six"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="parenright"
	k="24" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="j"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="T,uni0162,Tcaron,uni021A"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="V"
	k="25" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="ampersand"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="at"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="backslash"
	k="8" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="questiondown"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="24" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="x"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="hbar"
	k="25" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="18" />
    <hkern g1="P"
	g2="ampersand"
	k="15" />
    <hkern g1="P"
	g2="asterisk,degree,trademark"
	k="24" />
    <hkern g1="P"
	g2="four"
	k="15" />
    <hkern g1="P"
	g2="nine"
	k="-10" />
    <hkern g1="P"
	g2="one"
	k="5" />
    <hkern g1="P"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="P"
	g2="questiondown"
	k="10" />
    <hkern g1="P"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="25" />
    <hkern g1="P"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="18" />
    <hkern g1="P"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="8" />
    <hkern g1="P"
	g2="colon,semicolon"
	k="15" />
    <hkern g1="P"
	g2="slash"
	k="15" />
    <hkern g1="P"
	g2="parenright"
	k="29" />
    <hkern g1="P"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="P"
	g2="bracketright"
	k="15" />
    <hkern g1="P"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="-10" />
    <hkern g1="P"
	g2="j"
	k="-10" />
    <hkern g1="P"
	g2="braceleft"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="ampersand"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="eth"
	k="25" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="five"
	k="18" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="nine"
	k="8" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="paragraph"
	k="15" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="15" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="29" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="section"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="three"
	k="15" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="x"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="zero"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Hbar"
	k="18" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="two"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="8" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="V"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="X"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="asterisk,degree,trademark"
	k="29" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="at"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="eight"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="five"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="four"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="section"
	k="40" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="seven"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="six"
	k="18" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="25" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="underscore"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="100" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="exclam,exclamdown"
	k="28" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="two"
	k="20" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="braceleft"
	k="8" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="parenleft"
	k="8" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="at"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="backslash"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="18" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="copyright,registered"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="numbersign"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="one"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="questiondown"
	k="28" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="zero"
	k="8" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="8" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="18" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="slash"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="braceright"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Hbar"
	k="23" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="two"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="bracketright"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="j"
	k="19" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="braceleft"
	k="90" />
    <hkern g1="Tbar"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="25" />
    <hkern g1="Tbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Tbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="100" />
    <hkern g1="Tbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Tbar"
	g2="ampersand"
	k="28" />
    <hkern g1="Tbar"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Tbar"
	g2="five"
	k="-10" />
    <hkern g1="Tbar"
	g2="guillemotleft,guilsinglleft"
	k="8" />
    <hkern g1="Tbar"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="Tbar"
	g2="hyphen,endash,emdash"
	k="60" />
    <hkern g1="Tbar"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-20" />
    <hkern g1="Tbar"
	g2="one"
	k="18" />
    <hkern g1="Tbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="15" />
    <hkern g1="Tbar"
	g2="question"
	k="15" />
    <hkern g1="Tbar"
	g2="questiondown"
	k="10" />
    <hkern g1="Tbar"
	g2="section"
	k="50" />
    <hkern g1="Tbar"
	g2="six"
	k="23" />
    <hkern g1="Tbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="80" />
    <hkern g1="Tbar"
	g2="three"
	k="15" />
    <hkern g1="Tbar"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="30" />
    <hkern g1="Tbar"
	g2="x"
	k="19" />
    <hkern g1="Tbar"
	g2="zero"
	k="90" />
    <hkern g1="Tbar"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="Tbar"
	g2="underscore"
	k="5" />
    <hkern g1="Tbar"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="Tbar"
	g2="two"
	k="5" />
    <hkern g1="Thorn"
	g2="Eth,Dcroat"
	k="8" />
    <hkern g1="Thorn"
	g2="J"
	k="8" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="60" />
    <hkern g1="Thorn"
	g2="Tbar"
	k="-20" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="18" />
    <hkern g1="Thorn"
	g2="X"
	k="15" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="Thorn"
	g2="ampersand"
	k="10" />
    <hkern g1="Thorn"
	g2="at"
	k="50" />
    <hkern g1="Thorn"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="23" />
    <hkern g1="Thorn"
	g2="copyright,registered"
	k="80" />
    <hkern g1="Thorn"
	g2="eight"
	k="15" />
    <hkern g1="Thorn"
	g2="five"
	k="30" />
    <hkern g1="Thorn"
	g2="four"
	k="19" />
    <hkern g1="Thorn"
	g2="guillemotleft,guilsinglleft"
	k="90" />
    <hkern g1="Thorn"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="Thorn"
	g2="numbersign"
	k="5" />
    <hkern g1="Thorn"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="Thorn"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Eth,Dcroat"
	k="90" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Tbar"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="copyright,registered"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="seven"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="V"
	g2="backslash"
	k="10" />
    <hkern g1="V"
	g2="periodcentered,bullet"
	k="-10" />
    <hkern g1="V"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="V"
	g2="hbar"
	k="20" />
    <hkern g1="V"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="31" />
    <hkern g1="V"
	g2="j"
	k="47" />
    <hkern g1="V"
	g2="braceleft"
	k="34" />
    <hkern g1="V"
	g2="parenleft"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="five"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="31" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="47" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="zero"
	k="34" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="underscore"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceright"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright"
	k="-23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Hbar"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="two"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bracketright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hbar"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceleft"
	k="40" />
    <hkern g1="X"
	g2="eth"
	k="20" />
    <hkern g1="X"
	g2="five"
	k="31" />
    <hkern g1="X"
	g2="four"
	k="47" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="34" />
    <hkern g1="X"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="X"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="X"
	g2="nine"
	k="40" />
    <hkern g1="X"
	g2="numbersign"
	k="30" />
    <hkern g1="X"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="X"
	g2="one"
	k="10" />
    <hkern g1="X"
	g2="paragraph"
	k="30" />
    <hkern g1="X"
	g2="periodcentered,bullet"
	k="40" />
    <hkern g1="X"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="X"
	g2="question"
	k="30" />
    <hkern g1="X"
	g2="questiondown"
	k="10" />
    <hkern g1="X"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-23" />
    <hkern g1="X"
	g2="section"
	k="80" />
    <hkern g1="X"
	g2="seven"
	k="20" />
    <hkern g1="X"
	g2="six"
	k="40" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="X"
	g2="three"
	k="40" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="60" />
    <hkern g1="X"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="X"
	g2="x"
	k="60" />
    <hkern g1="X"
	g2="zero"
	k="40" />
    <hkern g1="X"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="X"
	g2="underscore"
	k="30" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="X"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="50" />
    <hkern g1="X"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="X"
	g2="slash"
	k="30" />
    <hkern g1="X"
	g2="braceright"
	k="20" />
    <hkern g1="X"
	g2="parenright"
	k="40" />
    <hkern g1="X"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="X"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="X"
	g2="Hbar"
	k="40" />
    <hkern g1="X"
	g2="two"
	k="65" />
    <hkern g1="X"
	g2="hbar"
	k="50" />
    <hkern g1="X"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="X"
	g2="braceleft"
	k="50" />
    <hkern g1="X"
	g2="parenleft"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Eth,Dcroat"
	k="34" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Tbar"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="X"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ampersand"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk,degree,trademark"
	k="-23" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eight"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eth"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="five"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="four"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="nine"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="one"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="paragraph"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="question"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="section"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="seven"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="six"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="zero"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Eth,Dcroat"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Tbar"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="V"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="X"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="asterisk,degree,trademark"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="at"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="backslash"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="copyright,registered"
	k="65" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="eth"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="five"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="questiondown"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="underscore"
	k="113" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="103" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="colon,semicolon"
	k="113" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="95" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceright"
	k="115" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="exclam,exclamdown"
	k="38" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="parenright"
	k="160" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="150" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Hbar"
	k="-25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="two"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hbar"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceleft"
	k="70" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="78" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="three"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="zero"
	k="65" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Hbar"
	k="90" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="braceright"
	k="33" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="bracketright"
	k="80" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="numbersign"
	k="45" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="parenright"
	k="45" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="quotedbl,quotesingle"
	k="45" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="yen"
	k="13" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="20" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="j"
	k="4" />
    <hkern g1="dcroat"
	g2="J"
	k="78" />
    <hkern g1="dcroat"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="dcroat"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="65" />
    <hkern g1="dcroat"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="dcroat"
	g2="at"
	k="90" />
    <hkern g1="dcroat"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="33" />
    <hkern g1="dcroat"
	g2="hyphen,endash,emdash"
	k="80" />
    <hkern g1="dcroat"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="45" />
    <hkern g1="dcroat"
	g2="periodcentered,bullet"
	k="45" />
    <hkern g1="dcroat"
	g2="section"
	k="45" />
    <hkern g1="dcroat"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="13" />
    <hkern g1="dcroat"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="dcroat"
	g2="copyright,registered"
	k="4" />
    <hkern g1="dcroat"
	g2="eight"
	k="10" />
    <hkern g1="dcroat"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="dcroat"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="5" />
    <hkern g1="dcroat"
	g2="three"
	k="20" />
    <hkern g1="dcroat"
	g2="zero"
	k="23" />
    <hkern g1="dcroat"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="dcroat"
	g2="Hbar"
	k="20" />
    <hkern g1="dcroat"
	g2="braceright"
	k="8" />
    <hkern g1="dcroat"
	g2="bracketright"
	k="30" />
    <hkern g1="dcroat"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="dcroat"
	g2="hbar"
	k="13" />
    <hkern g1="eth"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="78" />
    <hkern g1="eth"
	g2="X"
	k="30" />
    <hkern g1="eth"
	g2="ampersand"
	k="65" />
    <hkern g1="eth"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="eth"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="33" />
    <hkern g1="eth"
	g2="questiondown"
	k="80" />
    <hkern g1="eth"
	g2="slash"
	k="45" />
    <hkern g1="eth"
	g2="two"
	k="45" />
    <hkern g1="eth"
	g2="underscore"
	k="45" />
    <hkern g1="eth"
	g2="x"
	k="13" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="eth"
	g2="four"
	k="4" />
    <hkern g1="eth"
	g2="six"
	k="10" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="eth"
	g2="J"
	k="5" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="eth"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="23" />
    <hkern g1="eth"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="-10" />
    <hkern g1="eth"
	g2="at"
	k="20" />
    <hkern g1="eth"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="8" />
    <hkern g1="eth"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="eth"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="eth"
	g2="nine"
	k="13" />
    <hkern g1="f,f_f"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="f,f_f"
	g2="T,uni0162,Tcaron,uni021A"
	k="33" />
    <hkern g1="f,f_f"
	g2="Tbar"
	k="80" />
    <hkern g1="f,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="45" />
    <hkern g1="f,f_f"
	g2="asterisk,degree,trademark"
	k="45" />
    <hkern g1="f,f_f"
	g2="backslash"
	k="45" />
    <hkern g1="f,f_f"
	g2="eth"
	k="13" />
    <hkern g1="f,f_f"
	g2="five"
	k="20" />
    <hkern g1="f,f_f"
	g2="one"
	k="4" />
    <hkern g1="f,f_f"
	g2="question"
	k="10" />
    <hkern g1="f,f_f"
	g2="seven"
	k="10" />
    <hkern g1="f,f_f"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="5" />
    <hkern g1="f,f_f"
	g2="X"
	k="20" />
    <hkern g1="f,f_f"
	g2="ampersand"
	k="23" />
    <hkern g1="f,f_f"
	g2="colon,semicolon"
	k="-10" />
    <hkern g1="f,f_f"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="f,f_f"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="8" />
    <hkern g1="f,f_f"
	g2="questiondown"
	k="30" />
    <hkern g1="f,f_f"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="f,f_f"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="13" />
    <hkern g1="f,f_f"
	g2="yen"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="20" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="T,uni0162,Tcaron,uni021A"
	k="8" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="Tbar"
	k="30" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="V"
	k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="braceright"
	k="8" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="bracketright"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="x"
	k="-10" />
    <hkern g1="k,uni0137"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="8" />
    <hkern g1="k,uni0137"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="Hbar"
	k="10" />
    <hkern g1="k,uni0137"
	g2="braceright"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="eth"
	k="-10" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="8" />
    <hkern g1="dcaron,lcaron"
	g2="questiondown"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="at"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="T,uni0162,Tcaron,uni021A"
	k="8" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="Tbar"
	k="-20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="exclam,exclamdown"
	k="55" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="hbar"
	k="30" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="numbersign"
	k="35" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="parenright"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="quotedbl,quotesingle"
	k="55" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="yen"
	k="20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="55" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="nine"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="35" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="section"
	k="55" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="three"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="zero"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="bracketright"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="hbar"
	k="25" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="numbersign"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="parenright"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="yen"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="j"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="55" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="35" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="two"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="underscore"
	k="55" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="x"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="nine"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="section"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="copyright,registered"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="eight"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="three"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="zero"
	k="15" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Hbar"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="braceright"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="V"
	k="55" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="backslash"
	k="55" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="eth"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="five"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="X"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="ampersand"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="questiondown"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="slash"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="two"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="underscore"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="x"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="four"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="six"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="15" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="at"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Tbar"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="eth"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="five"
	k="50" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="one"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="question"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="X"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="ampersand"
	k="15" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="three"
	k="8" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="exclam,exclamdown"
	k="-20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="numbersign"
	k="34" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="8" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="34" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="three"
	k="93" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Eth,Dcroat"
	k="43" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Hbar"
	k="-20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="braceright"
	k="28" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="bracketright"
	k="35" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="hbar"
	k="55" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="parenright"
	k="18" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="quotedbl,quotesingle"
	k="38" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="yen"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="73" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="j"
	k="20" />
    <hkern g1="x"
	g2="X"
	k="8" />
    <hkern g1="x"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="x"
	g2="slash"
	k="34" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="93" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="43" />
    <hkern g1="x"
	g2="at"
	k="-20" />
    <hkern g1="x"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="28" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="x"
	g2="nine"
	k="55" />
    <hkern g1="x"
	g2="periodcentered,bullet"
	k="18" />
    <hkern g1="x"
	g2="section"
	k="38" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="73" />
    <hkern g1="x"
	g2="copyright,registered"
	k="20" />
    <hkern g1="x"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="8" />
    <hkern g1="x"
	g2="three"
	k="18" />
    <hkern g1="x"
	g2="zero"
	k="5" />
    <hkern g1="x"
	g2="Eth,Dcroat"
	k="18" />
    <hkern g1="x"
	g2="Hbar"
	k="10" />
    <hkern g1="x"
	g2="braceright"
	k="-10" />
    <hkern g1="x"
	g2="bracketright"
	k="44" />
    <hkern g1="x"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="x"
	g2="hbar"
	k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="34" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="seven"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="X"
	k="93" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="colon,semicolon"
	k="43" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="28" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="questiondown"
	k="35" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="55" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="two"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="underscore"
	k="38" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="x"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="73" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="four"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="J"
	k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="at"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="44" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="nine"
	k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="j"
	k="35" />
    <hkern g1="tcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-20" />
    <hkern g1="tcaron"
	g2="T,uni0162,Tcaron,uni021A"
	k="28" />
    <hkern g1="tcaron"
	g2="Tbar"
	k="35" />
    <hkern g1="tcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="55" />
    <hkern g1="tcaron"
	g2="asterisk,degree,trademark"
	k="18" />
    <hkern g1="tcaron"
	g2="backslash"
	k="38" />
    <hkern g1="tcaron"
	g2="eth"
	k="20" />
    <hkern g1="tcaron"
	g2="five"
	k="73" />
    <hkern g1="tcaron"
	g2="one"
	k="20" />
    <hkern g1="tcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="8" />
    <hkern g1="tcaron"
	g2="X"
	k="18" />
    <hkern g1="tcaron"
	g2="ampersand"
	k="5" />
    <hkern g1="tcaron"
	g2="colon,semicolon"
	k="18" />
    <hkern g1="tcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="tcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="-10" />
    <hkern g1="tcaron"
	g2="questiondown"
	k="44" />
    <hkern g1="tcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="15" />
    <hkern g1="tcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="8" />
    <hkern g1="tcaron"
	g2="copyright,registered"
	k="35" />
    <hkern g1="tcaron"
	g2="eight"
	k="10" />
    <hkern g1="tcaron"
	g2="guillemotright,guilsinglright"
	k="28" />
    <hkern g1="tcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="18" />
    <hkern g1="tcaron"
	g2="three"
	k="20" />
    <hkern g1="tcaron"
	g2="bracketright"
	k="10" />
    <hkern g1="tcaron"
	g2="exclam,exclamdown"
	k="8" />
    <hkern g1="at"
	g2="J"
	k="40" />
    <hkern g1="at"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="at"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="at"
	g2="Tbar"
	k="-2" />
    <hkern g1="at"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="at"
	g2="V"
	k="35" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="at"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="at"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="at"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="70" />
    <hkern g1="at"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="copyright,registered"
	g2="J"
	k="10" />
    <hkern g1="copyright,registered"
	g2="four"
	k="10" />
    <hkern g1="copyright,registered"
	g2="one"
	k="10" />
    <hkern g1="copyright,registered"
	g2="seven"
	k="70" />
    <hkern g1="copyright,registered"
	g2="six"
	k="30" />
    <hkern g1="copyright,registered"
	g2="zero"
	k="60" />
    <hkern g1="copyright,registered"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="copyright,registered"
	g2="eth"
	k="30" />
    <hkern g1="copyright,registered"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="80" />
    <hkern g1="copyright,registered"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="florin"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="florin"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="florin"
	g2="z,zacute,zdotaccent,zcaron"
	k="70" />
    <hkern g1="florin"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="florin"
	g2="five"
	k="60" />
    <hkern g1="florin"
	g2="four"
	k="30" />
    <hkern g1="florin"
	g2="nine"
	k="30" />
    <hkern g1="florin"
	g2="one"
	k="80" />
    <hkern g1="florin"
	g2="seven"
	k="10" />
    <hkern g1="florin"
	g2="zero"
	k="20" />
    <hkern g1="florin"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="florin"
	g2="eth"
	k="15" />
    <hkern g1="florin"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="florin"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="3" />
    <hkern g1="paragraph"
	g2="V"
	k="10" />
    <hkern g1="paragraph"
	g2="X"
	k="10" />
    <hkern g1="paragraph"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="paragraph"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="paragraph"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="60" />
    <hkern g1="paragraph"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="30" />
    <hkern g1="paragraph"
	g2="x"
	k="30" />
    <hkern g1="paragraph"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="80" />
    <hkern g1="paragraph"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="paragraph"
	g2="five"
	k="20" />
    <hkern g1="paragraph"
	g2="four"
	k="20" />
    <hkern g1="paragraph"
	g2="nine"
	k="15" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="seven"
	k="3" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="70" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="V"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="X"
	k="80" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="x"
	k="15" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="z,zacute,zdotaccent,zcaron"
	k="3" />
    <hkern g1="section"
	g2="J"
	k="30" />
    <hkern g1="section"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="80" />
    <hkern g1="section"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="section"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="section"
	g2="V"
	k="20" />
    <hkern g1="section"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="section"
	g2="X"
	k="10" />
    <hkern g1="section"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="3" />
    <hkern g1="section"
	g2="zero"
	k="20" />
    <hkern g1="section"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="30" />
    <hkern g1="section"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="section"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="backslash"
	g2="Tbar"
	k="60" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="backslash"
	g2="V"
	k="20" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="backslash"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="backslash"
	g2="j"
	k="10" />
    <hkern g1="backslash"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="100" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="100" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="120" />
    <hkern g1="backslash"
	g2="hbar"
	k="80" />
    <hkern g1="backslash"
	g2="X"
	k="75" />
    <hkern g1="backslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="25" />
    <hkern g1="backslash"
	g2="x"
	k="-70" />
    <hkern g1="backslash"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="backslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="backslash"
	g2="Hbar"
	k="20" />
    <hkern g1="braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="80" />
    <hkern g1="braceleft"
	g2="J"
	k="75" />
    <hkern g1="braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="25" />
    <hkern g1="braceleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-70" />
    <hkern g1="braceleft"
	g2="eth"
	k="10" />
    <hkern g1="braceleft"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="braceleft"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="braceleft"
	g2="Eth,Dcroat"
	k="70" />
    <hkern g1="braceleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="braceleft"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-10" />
    <hkern g1="braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="braceright"
	g2="eth"
	k="-10" />
    <hkern g1="braceright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-10" />
    <hkern g1="braceright"
	g2="T,uni0162,Tcaron,uni021A"
	k="-10" />
    <hkern g1="braceright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-70" />
    <hkern g1="braceright"
	g2="j"
	k="-10" />
    <hkern g1="bracketleft"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="bracketleft"
	g2="j"
	k="10" />
    <hkern g1="bracketleft"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="bracketright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="bracketright"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="bracketright"
	g2="hbar"
	k="-10" />
    <hkern g1="bracketright"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-10" />
    <hkern g1="bracketright"
	g2="Hbar"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="eth"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="exclam,exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-30" />
    <hkern g1="exclam,exclamdown"
	g2="Hbar"
	k="-15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Eth,Dcroat"
	k="-3" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="-3" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="110" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="j"
	k="95" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="105" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="48" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="130" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="X"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="x"
	k="23" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="75" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="23" />
    <hkern g1="guillemotright,guilsinglright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="75" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Eth,Dcroat"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="X"
	k="30" />
    <hkern g1="numbersign"
	g2="J"
	k="30" />
    <hkern g1="numbersign"
	g2="Tbar"
	k="40" />
    <hkern g1="numbersign"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="numbersign"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="numbersign"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="numbersign"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="50" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="x"
	k="10" />
    <hkern g1="parenleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="30" />
    <hkern g1="parenleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="parenleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="parenleft"
	g2="V"
	k="10" />
    <hkern g1="parenleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="parenleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="parenleft"
	g2="hbar"
	k="30" />
    <hkern g1="parenleft"
	g2="X"
	k="20" />
    <hkern g1="parenleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="50" />
    <hkern g1="parenright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="parenright"
	g2="J"
	k="20" />
    <hkern g1="parenright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="50" />
    <hkern g1="parenright"
	g2="Tbar"
	k="30" />
    <hkern g1="parenright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="parenright"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="35" />
    <hkern g1="parenright"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="parenright"
	g2="z,zacute,zdotaccent,zcaron"
	k="60" />
    <hkern g1="parenright"
	g2="Hbar"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="Tbar"
	k="50" />
    <hkern g1="periodcentered,bullet"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="35" />
    <hkern g1="periodcentered,bullet"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="question"
	g2="eth"
	k="35" />
    <hkern g1="question"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="question"
	g2="j"
	k="20" />
    <hkern g1="question"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="question"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="30" />
    <hkern g1="question"
	g2="X"
	k="-10" />
    <hkern g1="question"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="questiondown"
	g2="J"
	k="-10" />
    <hkern g1="questiondown"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="questiondown"
	g2="j"
	k="5" />
    <hkern g1="questiondown"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="12" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="Eth,Dcroat"
	k="-70" />
    <hkern g1="quotedbl,quotesingle"
	g2="V"
	k="-10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="slash"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="slash"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="slash"
	g2="j"
	k="60" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="slash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="slash"
	g2="hbar"
	k="40" />
    <hkern g1="underscore"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="40" />
    <hkern g1="underscore"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="20" />
    <hkern g1="underscore"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="80" />
    <hkern g1="underscore"
	g2="j"
	k="40" />
    <hkern g1="underscore"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="underscore"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="underscore"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="45" />
    <hkern g1="underscore"
	g2="hbar"
	k="30" />
    <hkern g1="underscore"
	g2="Hbar"
	k="35" />
  </font>
</defs></svg>
