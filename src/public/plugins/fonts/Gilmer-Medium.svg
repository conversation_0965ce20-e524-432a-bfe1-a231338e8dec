<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Wed Dec 12 12:49:52 2018
 By deploy user
Copyright &#194;&#169; 2018 by Piotr &#197;&#129;apa. All rights reserved.
</metadata>
<defs>
<font id="Gilmer-Medium" horiz-adv-x="613" >
  <font-face 
    font-family="Gilmer Medium"
    font-weight="600"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 7 0 0 0 0 0 0 0"
    ascent="790"
    descent="-210"
    x-height="503"
    cap-height="700"
    bbox="-352 -258 1419 975"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-U+FB02"
  />
<missing-glyph horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="606" 
d="M547 503v-503h-104v410h-215v-410h-105v410h-100v93h100v56q0 87 48.5 135.5t142.5 48.5q97 0 168 -49l-26 -85q-64 43 -132 43q-46 0 -71 -25t-25 -70v-54h319z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="617" 
d="M315 743q135 0 232 -65v-678h-104v624q-55 28 -119 28q-46 0 -71 -25t-25 -70v-54h141v-93h-141v-410h-105v410h-100v93h100v56q0 87 49 135.5t143 48.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="725" 
d="M547 557v-54h166v-93h-166v-410h-104v410h-215v-410h-105v410h-100v93h100v56q0 87 43 135.5t125 48.5q62 0 107 -21l-25 -85q-33 15 -69 15q-76 0 -76 -95v-54h215v56q0 87 42.5 135.5t125.5 48.5q62 0 107 -21l-26 -85q-33 15 -68 15q-77 0 -77 -95z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="925" 
d="M867 503v-503h-105v410h-215v-410h-104v410h-215v-410h-105v410h-100v93h100v56q0 87 45.5 135.5t132.5 48.5q62 0 107 -21l-25 -85q-33 15 -69 15q-41 0 -63.5 -25t-22.5 -70v-54h215v56q0 87 48 135.5t142 48.5q98 0 168 -49l-25 -85q-64 43 -133 43q-45 0 -70.5 -25
t-25.5 -70v-54h320z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="937" 
d="M635 743q135 0 232 -65v-678h-105v624q-55 28 -118 28q-46 0 -71.5 -25t-25.5 -70v-54h141v-93h-141v-410h-104v410h-215v-410h-105v410h-100v93h100v56q0 87 44 135.5t129 48.5q62 0 107 -21l-25 -85q-33 15 -69 15q-39 0 -60 -25t-21 -70v-54h215v56q0 87 48.5 135.5
t143.5 48.5z" />
    <glyph glyph-name=".notdef" horiz-adv-x="500" 
d="M410 790v-1000h-317v1000h317zM333 723h-165v-33h65v-37h-66v-33h166v33h-66v37h66v33zM267 594h-100v-104h166v34h-66v70zM233 560v-36h-33v36h33zM333 463h-166v-33h66v-37h-66v-33h100v70h66v33zM333 403h-33v-66h-133v-34h166v100zM333 281h-100v-56h34v23h33v-47
h-100v80h-33v-113h166v113zM333 108h-166v-113h166v113zM300 75v-47h-100v47h100zM333 -28h-166v-33h70l-70 -47v-33h166v33h-102l70 47h32v33z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="253" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="263" 
d="M90 210l-15 490h116l-16 -490h-85zM132 -7q-29 0 -49 19.5t-20 48.5q0 28 20 47.5t49 19.5q28 0 48 -19.5t20 -47.5q0 -29 -20 -48.5t-48 -19.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="362" 
d="M63 481l-13 219h104l-13 -219h-78zM221 481l-13 219h104l-13 -219h-78z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="723" 
d="M678 406h-140l-23 -142h141l-13 -82h-140l-29 -182h-90l29 182h-150l-29 -182h-90l29 182h-140l13 82h139l23 142h-140l13 83h140l29 186h90l-29 -186h150l29 186h90l-29 -186h140zM425 264l23 142h-150l-23 -142h150z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="654" 
d="M614 199q0 -84 -69 -144t-179 -67v-133h-90v137q-93 17 -159 74t-92 132l104 31q23 -64 81 -105t137 -41q70 0 114 33t44 76q0 35 -25 59t-85 43l-177 56q-157 50 -156 171q-1 73 59.5 128t154.5 63v133h90v-137q84 -13 144.5 -59.5t86.5 -109.5l-100 -30
q-25 49 -75.5 78.5t-117.5 29.5q-59 0 -96 -27t-37 -66q0 -30 20.5 -49.5t67.5 -33.5l172 -55q91 -29 137 -70.5t46 -116.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1023" 
d="M243 322q-83 0 -140.5 56t-57.5 137t57.5 136.5t140.5 55.5t140 -55.5t57 -136.5t-57 -137t-140 -56zM240 0l454 700h89l-453 -700h-90zM243 402q49 0 81 33t32 80t-32 79.5t-81 32.5t-81.5 -32.5t-32.5 -79.5t32.5 -80t81.5 -33zM781 -8q-83 0 -140.5 56t-57.5 137
q0 82 57.5 137.5t140.5 55.5t140 -55.5t57 -137.5q0 -81 -57 -137t-140 -56zM781 73q49 0 81 32.5t32 79.5q0 48 -32 80t-81 32t-81.5 -32t-32.5 -80q0 -46 32.5 -79t81.5 -33z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="706" 
d="M696 0h-130l-71 75q-96 -89 -218 -89q-90 0 -153.5 56.5t-63.5 144.5q0 60 34.5 114t101.5 91q-61 70 -61 135q0 87 58.5 136.5t150.5 49.5q81 0 136.5 -45.5t68.5 -111.5l-95 -27q-11 42 -39.5 66.5t-71.5 24.5q-46 0 -75 -26t-29 -68q0 -43 53 -98l196 -208
q34 59 46 119l93 -26q-18 -88 -71 -165zM288 82q76 0 141 62l-171 181q-93 -54 -93 -131q0 -46 34 -79t89 -33z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="204" 
d="M63 481l-13 219h104l-13 -219h-78z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="296" 
d="M273 -137h-103q-115 192 -115 441q2 250 115 440h103q-115 -197 -115 -440q0 -244 115 -441z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="296" 
d="M23 -137q115 197 115 441q0 243 -115 440h103q113 -190 115 -440q0 -249 -115 -441h-103z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="417" 
d="M367 602l-95 -51l95 -51l-35 -60l-92 58l4 -109h-70l3 108l-91 -57l-36 60l96 52l-96 50l36 61l91 -57l-3 108h70l-4 -108l92 57z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="555" 
d="M490 393v-85h-169v-170h-86v170h-170v85h170v170h86v-170h169z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="257" 
d="M50 -137l44 259h113l-72 -259h-85z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="490" 
d="M65 265v87h360v-87h-360z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="238" 
d="M119 -7q-29 0 -49 19.5t-20 48.5q0 28 20 47.5t49 19.5t49 -19.5t20 -47.5q0 -29 -20 -48.5t-49 -19.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="554" 
d="M35 -91l396 881h88l-396 -881h-88z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="643" 
d="M322 -13q-133 0 -207.5 101t-74.5 262t74.5 262t207.5 101t207 -100.5t74 -262.5q0 -161 -74 -262t-207 -101zM322 86q84 0 128 70.5t44 193.5t-44 193.5t-128 70.5t-128.5 -71t-44.5 -193t44.5 -193t128.5 -71z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="405" 
d="M231 700h92v-700h-110v578l-166 -80l-27 94z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="580" 
d="M203 99h329v-99h-492v77l276 263q50 47 73.5 81t23.5 72q0 57 -35 90t-96 33q-55 0 -93.5 -34.5t-51.5 -92.5l-97 28q11 80 77 138t163 58q108 0 174.5 -57t66.5 -158q0 -62 -32 -112t-99 -111z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="593" 
d="M351 430q90 -7 144.5 -64.5t54.5 -147.5q0 -98 -71 -165.5t-178 -67.5q-115 0 -183 60.5t-85 145.5l97 27q12 -58 57 -97.5t109 -39.5q69 0 109 40t40 97t-36.5 94.5t-98.5 37.5q-46 0 -81 -23l-19 68l172 208h-328v97h470v-76z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="603" 
d="M580 242v-96h-91v-146h-109v146h-350v80l251 474h117l-241 -458h223v160h109v-160h91z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="588" 
d="M312 451q103 0 169.5 -64.5t66.5 -164.5t-70.5 -167.5t-178.5 -67.5q-106 0 -172.5 54.5t-88.5 134.5l98 27q17 -53 58 -86.5t100 -33.5q68 0 108.5 40t40.5 100q0 62 -39.5 101t-110.5 39q-80 0 -144 -49l-84 25l31 361h418v-97h-323l-14 -184q58 32 135 32z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="577" 
d="M302 469q109 -4 177 -71t68 -168q0 -103 -73 -173t-185 -70q-113 0 -186 69.5t-73 173.5q0 73 55 156l206 314h123l-155 -235q17 4 43 4zM289 85q67 0 108.5 41t41.5 104q0 62 -42 103t-109 41q-66 0 -108 -41.5t-42 -102.5q0 -62 42 -103.5t109 -41.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="532" 
d="M18 700h492v-77l-276 -623h-115l268 602h-369v98z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="607" 
d="M441 380q60 -24 93 -70.5t33 -109.5q0 -92 -73 -152.5t-190 -60.5q-118 0 -191 60.5t-73 152.5q0 63 33 109.5t93 70.5q-96 48 -96 148q0 79 64.5 132t169.5 53t169 -53t64 -132q0 -100 -96 -148zM304 625q-60 0 -94.5 -29.5t-34.5 -74.5t34.5 -75t94.5 -30q58 0 93 30
t35 75t-34.5 74.5t-93.5 29.5zM304 80q70 0 112.5 36t42.5 91t-42.5 90.5t-112.5 35.5q-71 0 -113 -35.5t-42 -90.5t42 -91t113 -36z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="577" 
d="M288 713q113 0 186 -69.5t73 -172.5q0 -72 -55 -157l-206 -314h-123l156 236q-23 -5 -44 -5q-109 4 -177 71.5t-68 168.5q0 103 73 172.5t185 69.5zM289 326q66 0 108 41.5t42 103.5t-42 103.5t-109 41.5t-108.5 -41t-41.5 -104q0 -62 42 -103.5t109 -41.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="238" 
d="M119 365q-29 0 -49 19.5t-20 48.5q0 28 20 47t49 19t49 -19t20 -47q0 -29 -20 -48.5t-49 -19.5zM119 -7q-29 0 -49 19.5t-20 48.5q0 28 20 47.5t49 19.5t49 -19.5t20 -47.5q0 -29 -20 -48.5t-49 -19.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="261" 
d="M143 365q-29 0 -49 19.5t-20 48.5q0 28 20 47.5t49 19.5q28 0 48 -19.5t20 -47.5q0 -29 -19.5 -48.5t-48.5 -19.5zM50 -137l44 259h113l-72 -259h-85z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="550" 
d="M480 565v-93l-323 -121l323 -122v-93l-425 166v97z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="556" 
d="M65 404v85h425v-85h-425zM66 212v85h425v-85h-425z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="550" 
d="M70 565l425 -166v-97l-425 -166v93l323 122l-323 121v93z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="566" 
d="M220 210q0 46 19 84t45 63.5t52.5 48t45 49t18.5 56.5q0 47 -32.5 76.5t-86.5 29.5q-57 0 -94.5 -35t-51.5 -93l-95 28q15 84 78 140t164 56q99 0 162.5 -55.5t63.5 -142.5q0 -44 -19 -80t-46.5 -60.5l-55 -48t-47 -53t-19.5 -63.5h-101zM270 -7q-29 0 -49 19.5t-20 48.5
q0 28 20 47.5t49 19.5t49 -19.5t20 -47.5q0 -29 -20 -48.5t-49 -19.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="992" 
d="M488 -177q-192 0 -317.5 125t-125.5 308q0 123 61.5 225t166.5 160.5t230 58.5q194 0 319 -124.5t125 -314.5q0 -113 -56.5 -179.5t-139.5 -66.5q-100 0 -126 93q-57 -77 -154 -77q-91 0 -152 64t-61 160q0 97 61 161t153 64q96 0 150 -75v63h86v-312q0 -74 56 -74
q42 0 73.5 48t31.5 130q0 158 -101 262.5t-264 104.5q-160 0 -270.5 -107t-110.5 -264q0 -154 104.5 -257t262.5 -103q130 0 205 60l20 -72q-86 -61 -227 -61zM484 112q61 0 101 40.5t40 103.5q0 62 -39.5 102t-101.5 40q-60 0 -98.5 -40t-38.5 -102t39 -103t98 -41z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="709" 
d="M588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266h259l-129 331z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="689" 
d="M528 366q56 -22 87.5 -66t31.5 -104q0 -87 -62 -141.5t-168 -54.5h-347v700h326q98 0 160 -51t63 -133q1 -103 -91 -150zM394 606h-217v-205h223q51 0 82.5 30t30.5 73q0 45 -33 73.5t-86 28.5zM415 95q57 0 90.5 32t33.5 79q0 45 -33 76.5t-90 31.5h-239v-219h238z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="778" 
d="M404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t260 103.5q126 0 214.5 -63.5t118.5 -155.5l-105 -26q-23 62 -84 103t-144 41q-113 0 -183.5 -75.5t-70.5 -184.5q0 -110 70.5 -186.5t183.5 -76.5q83 0 144 40t83 102l105 -27q-30 -91 -118 -154t-213 -63z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="734" 
d="M355 700q155 0 247.5 -97.5t92.5 -252.5q0 -154 -92 -252t-248 -98h-285v700h285zM353 98q107 0 170 71t63 183q0 110 -63 180t-170 70h-175v-504h175z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="624" 
d="M574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="585" 
d="M567 601h-388v-210h337v-96h-337v-295h-109v700h497v-99z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="799" 
d="M760 380v-56q0 -154 -98 -245.5t-253 -91.5q-159 0 -264.5 104.5t-105.5 259.5q0 154 105 258t260 104q120 0 208.5 -59t123.5 -151l-102 -28q-28 63 -86.5 100t-142.5 37q-113 0 -185 -76t-72 -184q0 -110 73 -187t194 -77q100 0 163 54.5t71 144.5l-244 -1v94h355z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="721" 
d="M543 700h108v-700h-108v308h-364v-308h-109v700h109v-294h364v294z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="250" 
d="M70 0v700h110v-700h-110z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="614" 
d="M551 700v-466q0 -113 -70.5 -180t-185.5 -67q-99 0 -167 50.5t-98 136.5l101 30q17 -56 58.5 -88.5t103.5 -32.5q70 0 110 43t40 110v368h-353v96h461z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="691" 
d="M543 0l-245 312l-119 -130v-182h-109v700h109v-374l334 374h139l-283 -309l307 -391h-133z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="572" 
d="M179 100h368v-100h-477v700h109v-600z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="837" 
d="M663 700h104v-700h-109v512l-240 -336l-239 336v-512h-109v700h104l245 -352z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="728" 
d="M549 700h109v-700h-91l-388 507v-507h-109v700h91l388 -507v507z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="807" 
d="M404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t261 103.5q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5zM404 87q113 0 184 76.5t71 188.5q0 110 -71.5 185.5t-183.5 75.5q-113 0 -184.5 -75.5t-71.5 -185.5q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="650" 
d="M388 700q108 0 172.5 -71.5t64.5 -163.5t-65.5 -163t-176.5 -71h-205v-231h-108v700h318zM381 329q63 0 101 40.5t38 95.5t-38 96t-96 41h-208v-273h203z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="804" 
d="M763 351q0 -146 -96 -248l86 -103h-111l-41 47q-90 -60 -200 -60q-152 0 -257 105t-105 259q0 153 105 257.5t257 104.5t257 -104.5t105 -257.5zM401 87q74 0 134 35l-163 188h123l107 -129q55 72 55 171q0 111 -71.5 186.5t-184.5 75.5t-184.5 -75.5t-71.5 -186.5
q0 -114 71.5 -189.5t184.5 -75.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="686" 
d="M526 0l-171 260h-177v-260h-108v700h333q105 0 167.5 -67t62.5 -154q0 -72 -43 -131t-120 -79l181 -269h-125zM178 603v-247h219q59 0 95 36t36 87t-35.5 87.5t-92.5 36.5h-222z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="665" 
d="M347 -13q-117 0 -201 61t-114 150l103 31q24 -64 81.5 -105t136.5 -41q70 0 114.5 33t44.5 76q0 35 -25.5 59t-85.5 43l-176 56q-157 49 -157 171q0 79 69 135.5t173 56.5q107 0 184.5 -49.5t109.5 -124.5l-101 -30q-25 49 -75.5 78.5t-117.5 29.5q-58 0 -95.5 -27.5
t-37.5 -65.5q0 -30 21 -49.5t68 -33.5l172 -55q91 -29 137 -70.5t46 -116.5q0 -90 -76.5 -151t-197.5 -61z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="632" 
d="M617 700v-98h-246v-602h-109v602h-247v98h602z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="716" 
d="M544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="695" 
d="M567 700h118l-279 -700h-117l-279 700h119l219 -565z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="994" 
d="M870 700h114l-217 -700h-114l-156 520l-157 -520h-114l-216 700h114l162 -549l162 549h98l162 -548z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="695" 
d="M680 0h-131l-201 273l-202 -273h-131l259 353l-253 347h131l196 -267l195 267h131l-253 -347z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="665" 
d="M536 700h122l-270 -437v-263h-110v263l-270 437h122l203 -336z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="630" 
d="M188 97h407v-97h-557v73l398 530h-389v97h537v-73z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="338" 
d="M308 651h-135v-695h135v-93h-238v881h238v-93z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="554" 
d="M519 -91h-88l-396 881h88z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="338" 
d="M268 744v-881h-238v93h136v695h-136v93h238z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="556" 
d="M518 406h-106l-134 233l-134 -233h-106l188 324h105z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="600" 
d="M40 -141v82h520v-82h-520z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="195" 
d="M195 582h-92l-103 138h117z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="644" 
d="M289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="644" 
d="M355 516q107 0 179 -75.5t72 -188.5q0 -114 -72 -189.5t-179 -75.5q-117 0 -180 89v-76h-105v730h105v-303q63 89 180 89zM338 81q72 0 118 48.5t46 122.5t-46 122t-118 48q-70 0 -118 -48.5t-48 -121.5q0 -74 48 -122.5t118 -48.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="579" 
d="M303 -13q-114 0 -189 76t-75 189t74.5 188.5t188.5 75.5q94 0 158.5 -48.5t80.5 -110.5l-97 -28q-11 37 -50 65t-91 28q-72 0 -117 -49.5t-45 -120.5q0 -70 45.5 -120.5t116.5 -50.5q52 0 91 26.5t50 63.5l97 -27q-16 -61 -80 -109t-158 -48z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="644" 
d="M470 730h104v-730h-104v76q-63 -89 -181 -89q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v303zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="592" 
d="M307 -13q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-27 -59 -89 -101t-152 -42zM142 289l309 1q-5 60 -45.5 98t-107.5 38q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="406" 
d="M228 557v-54h166v-93h-166v-410h-105v410h-100v93h100v56q0 87 43 135.5t125 48.5q62 0 107 -21l-25 -85q-33 15 -69 15q-76 0 -76 -95z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="642" 
d="M467 503h105v-485q0 -106 -70.5 -173.5t-184.5 -67.5q-167 0 -240 131l92 34q46 -77 150 -77q70 0 108.5 42.5t39.5 109.5v74q-63 -85 -177 -85q-109 0 -180 73t-71 182q0 112 70.5 183.5t180.5 71.5q114 0 177 -84v71zM306 100q70 0 117 46t47 115t-47 114.5t-117 45.5
q-72 0 -118 -45t-46 -115t46 -115.5t118 -45.5z" />
    <glyph glyph-name="h" unicode="h" 
d="M348 516q96 0 148 -59t52 -162v-295h-104v276q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v730h105v-302q62 88 173 88z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="249" 
d="M125 585q-28 0 -47 18.5t-19 46.5q0 26 19 45t47 19t47 -19t19 -45q0 -28 -19 -46.5t-47 -18.5zM72 0v503h104v-503h-104z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="248" 
d="M124 585q-28 0 -47 18.5t-19 46.5q0 26 19 45t47 19t47 -19t19 -45q0 -28 -19 -46.5t-47 -18.5zM71 -39v542h104v-535q0 -91 -40 -141t-122 -50q-44 0 -88 20l24 84q22 -15 51 -15q71 0 71 95z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="542" 
d="M412 0l-170 213l-67 -72v-141h-105v730h105v-456l210 229h124l-201 -218l224 -285h-120z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="245" 
d="M70 0v730h105v-730h-105z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="926" 
d="M664 516q90 0 143.5 -57t53.5 -158v-301h-105v281q0 65 -28.5 101t-84.5 36q-58 0 -92 -42t-34 -113v-263h-104v281q0 65 -27.5 101t-81.5 36q-60 0 -94.5 -44t-34.5 -120v-254h-105v503h105v-70q57 83 158 83q110 0 155 -86q57 86 176 86z" />
    <glyph glyph-name="n" unicode="n" 
d="M348 516q96 0 148 -59t52 -162v-295h-104v276q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v503h105v-75q62 88 173 88z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="611" 
d="M306 -13q-115 0 -191 75.5t-76 189.5q0 113 76 188.5t191 75.5t191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5zM306 82q72 0 118.5 48.5t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5t-46.5 -121.5q0 -72 46.5 -120.5t118.5 -48.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="644" 
d="M355 516q107 0 179 -75.5t72 -188.5q0 -114 -72 -189.5t-179 -75.5q-117 0 -180 89v-286h-105v713h105v-76q63 89 180 89zM338 81q72 0 118 48.5t46 122.5t-46 122t-118 48q-70 0 -118 -48.5t-48 -121.5q0 -74 48 -122.5t118 -48.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="644" 
d="M470 503h104v-713h-104v286q-63 -89 -181 -89q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="370" 
d="M175 423q23 47 67.5 70.5t104.5 15.5v-98q-83 12 -127.5 -32.5t-44.5 -151.5v-227h-105v503h105v-80z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="515" 
d="M261 -13q-86 0 -151.5 44.5t-85.5 108.5l95 28q14 -41 53.5 -68t91.5 -27q47 0 76 20t29 48q0 23 -15 36.5t-50 23.5l-135 40q-120 36 -120 129q0 60 56 103t136 43q79 0 137 -35t81 -92l-93 -27q-15 30 -48 50t-78 20q-38 0 -63 -18.5t-25 -42.5q0 -33 54 -50l134 -39
q62 -18 97 -48.5t35 -88.5q0 -68 -59.5 -113t-151.5 -45z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="411" 
d="M352 102l26 -86q-57 -24 -111 -24q-79 0 -118 45.5t-39 125.5v247h-92v93h92v168h104v-168h160v-93h-160v-247q0 -77 72 -77q33 0 66 16z" />
    <glyph glyph-name="u" unicode="u" 
d="M439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="558" 
d="M437 503h114l-213 -503h-117l-213 503h114l157 -389z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="790" 
d="M674 503h109l-173 -503h-100l-115 352l-115 -352h-99l-173 503h108l118 -372l117 372h89l116 -371z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="535" 
d="M527 0h-121l-138 180l-139 -180h-121l194 254l-185 249h120l131 -174l130 174h120l-185 -249z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="566" 
d="M446 503h112l-306 -713h-108l94 221l-230 492h114l168 -374z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="523" 
d="M183 89h299v-89h-439v67l290 347h-284v89h424v-69z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="370" 
d="M247 425q0 -96 -75 -121q75 -25 75 -121v-153q0 -40 15 -59.5t52 -19.5h26v-93h-50q-145 0 -145 156v169q0 33 -20.5 53.5t-54.5 20.5h-20v93h20q34 0 54.5 21t20.5 54v169q0 155 145 155h50v-93h-26q-37 0 -52 -19.5t-15 -58.5v-153z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="224" 
d="M65 -125v930h94v-930h-94z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="370" 
d="M300 350h20v-93h-20q-34 0 -54.5 -20.5t-20.5 -53.5v-169q0 -156 -145 -156h-50v93h26q37 0 52 19.5t15 59.5v153q0 96 75 121q-75 25 -75 121v153q0 39 -15 58.5t-52 19.5h-26v93h50q145 0 145 -155v-169q0 -33 20.5 -54t54.5 -21z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="588" 
d="M149 273l-94 27q0 56 31 94.5t93 38.5q49 0 117.5 -36.5t91.5 -36.5q52 0 52 68l93 -27q0 -56 -30 -94.5t-92 -38.5q-49 0 -117.5 36t-91.5 36q-53 0 -53 -67z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="238" 
d="M119 510q29 0 49 -19.5t20 -48.5q0 -28 -20 -47.5t-49 -19.5t-49 19.5t-20 47.5q0 29 20 48.5t49 19.5zM160 293l16 -491h-116l15 491h85z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="575" 
d="M304 179q53 0 92 26.5t50 63.5l97 -27q-15 -54 -67 -98.5t-129 -55.5v-104h-90v105q-96 16 -156.5 88.5t-60.5 172.5t60.5 172t156.5 88v105h90v-104q77 -11 129 -56.5t67 -99.5l-97 -28q-11 37 -50 65t-92 28q-72 0 -116.5 -49.5t-44.5 -120.5t45 -121t116 -50z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="643" 
d="M237 101h371v-101h-563v101h85v184h-69v88h69v134q0 99 58.5 153.5t156.5 54.5q88 0 146.5 -51.5t71.5 -124.5l-97 -28q-27 106 -124 106q-50 0 -77.5 -30.5t-27.5 -85.5v-128h244v-88h-244v-184z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="620" 
d="M544 320q0 -73 -40 -134l81 -80l-61 -61l-80 81q-60 -41 -134 -41t-134 41l-80 -81l-61 61l82 81q-40 57 -40 133q0 75 39 133l-81 81l61 61l80 -81q58 41 134 41t134 -41l80 81l61 -61l-81 -80q40 -61 40 -134zM310 177q61 0 102.5 41t41.5 102t-41.5 102t-102.5 41
t-102 -41t-41 -102t41 -102t102 -41z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="680" 
d="M665 700l-230 -373h159v-64h-199v-62h199v-64h-199v-137h-109v137h-195v64h195v62h-195v64h155l-231 373h123l202 -335l203 335h122z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="224" 
d="M65 445v360h94v-360h-94zM65 -125v360h94v-360h-94z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="557" 
d="M437 209q52 -38 52 -101q0 -69 -59 -115.5t-151 -46.5q-83 0 -147 44t-82 106l94 28q14 -42 51 -67t90 -24q45 0 73.5 21.5t28.5 48.5q0 22 -15 34.5t-49 23.5l-139 44q-126 39 -126 138q0 37 23 68.5t57 51.5q-53 40 -53 102t56 105t136 43q74 0 128 -33.5t76 -87.5
l-93 -27q-13 28 -41.5 45.5t-69.5 18.5q-42 0 -66.5 -20t-24.5 -42q0 -37 55 -57l135 -43q62 -20 96.5 -51t34.5 -89q0 -74 -70 -118zM229 283l126 -37q50 32 50 75q0 24 -15.5 38t-50.5 26l-118 39q-27 -11 -44 -31.5t-17 -41.5q0 -49 69 -68z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="304" 
d="M57 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM246 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="749" 
d="M375 16q-143 0 -240 96.5t-97 239.5q0 141 97 237t240 96t240 -96t97 -237q0 -143 -97 -239.5t-240 -96.5zM375 76q117 0 196 79t79 197q0 117 -79 194.5t-196 77.5t-196.5 -77.5t-79.5 -194.5q0 -118 79.5 -197t196.5 -79zM379 178q-75 0 -124.5 49.5t-49.5 123.5
q0 73 49.5 122t123.5 49q63 0 105.5 -31.5t54.5 -75.5l-63 -16q-8 26 -34 44.5t-62 18.5q-49 0 -79 -32.5t-30 -78.5q0 -47 30 -80t79 -33q35 0 61 18t35 44l63 -16q-13 -44 -55 -75t-104 -31z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="503" 
d="M266 500l-100 -180l100 -180h-98l-103 180l103 180h98zM438 500l-100 -180l100 -180h-99l-102 180l102 180h99z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="575" 
d="M510 448v-230h-91v145h-354v85h445z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="749" 
d="M375 685q143 0 240 -96.5t97 -238.5t-97 -238t-240 -96t-240 96t-97 238t97 238.5t240 96.5zM375 75q118 0 196.5 79t78.5 196q0 118 -78.5 197t-196.5 79q-117 0 -196 -79t-79 -197q0 -117 79 -196t196 -79zM521 408q0 -33 -19 -60.5t-54 -39.5l78 -123h-74l-70 116h-72
v-116h-64v332h162q51 0 82 -33t31 -76zM310 458v-100h94q24 0 39.5 14.5t15.5 35.5t-15 35.5t-38 14.5h-96z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="275" 
d="M0 591v77h275v-77h-275z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="404" 
d="M203 423q-70 0 -117.5 46.5t-47.5 114.5t47.5 114.5t117.5 46.5q69 0 116.5 -46.5t47.5 -114.5t-47.5 -114.5t-116.5 -46.5zM203 496q37 0 62.5 26t25.5 62q0 38 -25.5 63t-62.5 25q-39 0 -64 -25.5t-25 -62.5t25 -62.5t64 -25.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="555" 
d="M321 467h169v-85h-169v-147h-86v147h-170v85h170v146h86v-146zM65 88v85h425v-85h-425z" />
    <glyph glyph-name="two.sups" unicode="&#xb2;" horiz-adv-x="345" 
d="M145 522h175v-72h-299v53l159 150q50 48 50 79q0 25 -16.5 40.5t-45.5 15.5q-58 0 -73 -71l-74 21q6 50 46.5 86t99.5 36q66 0 106 -33t40 -92q0 -34 -16 -61t-51 -58z" />
    <glyph glyph-name="three.sups" unicode="&#xb3;" horiz-adv-x="355" 
d="M227 697q47 -8 75 -40.5t28 -80.5q0 -58 -42 -97t-106 -39q-71 0 -111.5 36t-50.5 89l73 21q5 -34 28.5 -55t58.5 -21q33 0 52.5 19.5t19.5 47.5t-19.5 47t-56.5 19q-15 0 -30 -3l-13 46l84 95h-183v69h282v-57z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="195" 
d="M0 582l78 138h117l-102 -138h-93z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="681" 
d="M621 700v-99h-71v-811h-97v811h-111v-811h-99v496q-89 0 -148.5 59.5t-59.5 146.5q0 88 61.5 148t156.5 60h368z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="238" 
d="M119 223q-29 0 -49 19.5t-20 48.5q0 28 20 47t49 19t49 -19t20 -47q0 -29 -20 -48.5t-49 -19.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="216" 
d="M104 -223q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l30 95h78l-17 -57q36 -3 58.5 -25.5t22.5 -52.5q0 -41 -31.5 -64.5t-80.5 -23.5z" />
    <glyph glyph-name="one.sups" unicode="&#xb9;" horiz-adv-x="258" 
d="M133 850h70v-400h-82v308l-96 -45l-20 70z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="503" 
d="M164 500l102 -180l-102 -180h-99l100 180l-100 180h99zM335 500l103 -180l-103 -180h-98l100 180l-100 180h98z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="791" 
d="M121 300v308l-96 -45l-20 70l128 67h70v-400h-82zM529 700h78l-435 -700h-79zM776 150v-71h-49v-79h-78v79h-197v62l139 259h86l-132 -250h104v78h78v-78h49z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="787" 
d="M121 300v308l-96 -45l-20 70l128 67h70v-400h-82zM529 700h78l-435 -700h-79zM587 72h175v-72h-299v53l159 150q50 48 50 79q0 25 -16.5 40.5t-45.5 15.5q-58 0 -73 -71l-74 21q6 50 46.5 86t99.5 36q66 0 106 -33t40 -92q0 -34 -16 -61t-51 -58z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="888" 
d="M330 426q0 -58 -42 -97t-106 -39q-71 0 -111.5 36t-50.5 89l73 21q5 -34 28.5 -55t58.5 -21q33 0 52.5 19.5t19.5 47.5t-19.5 47t-56.5 19q-15 0 -30 -3l-13 46l84 95h-183v69h282v-57l-89 -96q47 -8 75 -40.5t28 -80.5zM626 700h78l-435 -700h-79zM873 150v-71h-49v-79
h-78v79h-197v62l139 259h86l-132 -250h104v78h78v-78h49z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="546" 
d="M278 510q29 0 49 -19.5t20 -48.5q0 -28 -20 -47.5t-49 -19.5q-28 0 -48 19.5t-20 47.5t20 48t48 20zM328 293q0 -46 -18.5 -84t-45 -63.5t-53 -48t-45 -49t-18.5 -56.5q0 -47 32.5 -76.5t87.5 -29.5q56 0 93.5 34.5t51.5 93.5l95 -29q-15 -83 -77.5 -139.5t-163.5 -56.5
q-99 0 -163 56t-64 142q0 44 19.5 80t46.5 61l54.5 48.5t47 53t19.5 63.5h101z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="709" 
d="M410 780h-92l-103 138h117zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266h259l-129 331z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="709" 
d="M493 918l-102 -138h-93l78 138h117zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266h259l-129 331z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="709" 
d="M355 861l-62 -85h-99l109 137h104l109 -137h-99zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266h259l-129 331z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="709" 
d="M294 831q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131q-31 0 -74 28t-57 28zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266h259l-129 331z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="709" 
d="M259 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM448 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113z
M225 266h259l-129 331z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="709" 
d="M355 747q-50 0 -84 32.5t-34 81.5t34 81.5t84 32.5t83.5 -32.5t33.5 -81.5t-33.5 -81.5t-83.5 -32.5zM355 912q-24 0 -38.5 -14t-14.5 -37t14 -37.5t39 -14.5q24 0 38 14.5t14 37.5t-14 37t-38 14zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266
h259l-129 331z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="984" 
d="M934 602h-351v-199h300v-94h-300v-210h350v-99h-455v171h-249l-102 -171h-119l420 700h506v-98zM287 269h191v322z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="778" 
d="M403 89q83 0 144 40t83 102l105 -27q-27 -83 -103.5 -143.5t-185.5 -71.5l-14 -46q36 -3 58.5 -25.5t22.5 -52.5q0 -41 -31.5 -64.5t-80.5 -23.5q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l26 84q-143 13 -235.5 114.5t-92.5 247.5
q0 155 104 258.5t260 103.5q126 0 214.5 -63.5t118.5 -155.5l-105 -26q-23 62 -84 103t-144 41q-113 0 -183.5 -75.5t-70.5 -184.5q0 -110 70.5 -186.5t183.5 -76.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="624" 
d="M375 780h-92l-103 138h117zM574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="624" 
d="M458 918l-102 -138h-93l78 138h117zM574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="624" 
d="M320 861l-62 -85h-99l109 137h104l109 -137h-99zM574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="624" 
d="M224 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM413 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="250" 
d="M181 780h-92l-103 138h117zM70 0v700h110v-700h-110z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="250" 
d="M69 780l78 138h117l-102 -138h-93zM70 0v700h110v-700h-110z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="250" 
d="M177 913l109 -137h-99l-62 85l-62 -85h-99l109 137h104zM70 0v700h110v-700h-110z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="250" 
d="M30 769q-25 0 -41 16t-16 40t16 40t41 16t41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM219 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM70 0v700h110v-700h-110z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="764" 
d="M386 700q155 0 247 -97.5t92 -252.5q0 -154 -91.5 -252t-247.5 -98h-285v302h-71v93h71v305h285zM384 98q107 0 169.5 71t62.5 183q0 110 -62.5 180t-169.5 70h-176v-207h194v-93h-194v-204h176z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="728" 
d="M304 831q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131q-31 0 -74 28t-57 28zM549 700h109v-700h-91l-388 507v-507h-109v700h91l388 -507v507z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="807" 
d="M460 780h-92l-103 138h117zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t261 103.5q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5zM404 87q113 0 184 76.5t71 188.5q0 110 -71.5 185.5t-183.5 75.5q-113 0 -184.5 -75.5t-71.5 -185.5
q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="807" 
d="M348 780l78 138h117l-102 -138h-93zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t261 103.5q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5zM404 87q113 0 184 76.5t71 188.5q0 110 -71.5 185.5t-183.5 75.5q-113 0 -184.5 -75.5t-71.5 -185.5
q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="807" 
d="M404 861l-62 -85h-99l109 137h104l109 -137h-99zM404 713q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5t-260.5 104.5t-104.5 259.5t104 258.5t261 103.5zM404 87q113 0 184 76.5t71 188.5q0 110 -71.5 185.5t-183.5 75.5q-113 0 -184.5 -75.5t-71.5 -185.5
q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="807" 
d="M474 775q-31 0 -74 28t-57 28q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t261 103.5q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5zM404 87q113 0 184 76.5
t71 188.5q0 110 -71.5 185.5t-183.5 75.5q-113 0 -184.5 -75.5t-71.5 -185.5q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="807" 
d="M309 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM498 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t261 103.5
q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5zM404 87q113 0 184 76.5t71 188.5q0 110 -71.5 185.5t-183.5 75.5q-113 0 -184.5 -75.5t-71.5 -185.5q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="512" 
d="M447 482l-131 -131l131 -131l-60 -60l-130 131l-131 -131l-60 60l131 131l-131 131l59 60l131 -131l131 131z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="807" 
d="M642 629q59 -50 92.5 -122t33.5 -156q0 -155 -104 -259.5t-260 -104.5q-98 0 -180 44l-32 -45h-79l57 81q-62 50 -96.5 124t-34.5 160q0 155 104 258.5t261 103.5q102 0 185 -47l34 48h78zM148 352q0 -123 82 -199l300 429q-56 31 -126 31q-113 0 -184.5 -75.5
t-71.5 -185.5zM404 87q113 0 184 76.5t71 188.5q0 117 -77 191l-299 -427q53 -29 121 -29z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="716" 
d="M414 780h-92l-103 138h117zM544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="716" 
d="M497 918l-102 -138h-93l78 138h117zM544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="716" 
d="M359 861l-62 -85h-99l109 137h104l109 -137h-99zM544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="716" 
d="M263 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM452 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5
v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="665" 
d="M472 918l-102 -138h-93l78 138h117zM536 700h122l-270 -437v-263h-110v263l-270 437h122l203 -336z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="650" 
d="M388 595q108 0 172.5 -72t64.5 -163q0 -92 -65.5 -163t-176.5 -71h-205v-126h-108v700h108v-105h210zM381 223q63 0 101 41t38 96q0 54 -38 95t-96 41h-208v-273h203z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="600" 
d="M434 396q57 -24 90.5 -71t33.5 -113q0 -100 -65.5 -156t-178.5 -56h-62v93h59q67 0 105 32.5t38 89.5q0 58 -36.5 90.5t-102.5 32.5h-47v85h20q57 0 91.5 31.5t34.5 84.5q0 49 -31.5 79t-83.5 30q-61 0 -95 -41t-34 -113v-494h-105v492q0 112 63 180.5t169 68.5
q96 0 158.5 -55t62.5 -141q0 -102 -84 -149z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="644" 
d="M366 582h-92l-103 138h117zM289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="644" 
d="M254 582l78 138h117l-102 -138h-93zM289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5
t118 -48.5z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="644" 
d="M311 663l-62 -85h-99l109 137h104l109 -137h-99zM470 503h104v-503h-104v76q-63 -89 -181 -89q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122
t46 -122.5t118 -48.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="644" 
d="M381 577q-31 0 -74 28t-57 28q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131zM289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81
q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="644" 
d="M215 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM404 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5
q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="644" 
d="M311 549q-50 0 -84 32.5t-34 81.5t34 81.5t84 32.5t83.5 -32.5t33.5 -81.5t-33.5 -81.5t-83.5 -32.5zM311 611q24 0 38 14.5t14 37.5t-14 37t-38 14t-38.5 -14t-14.5 -37t14 -37.5t39 -14.5zM289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5
q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1027" 
d="M988 259q0 -22 -3 -46h-409q9 -63 53.5 -100.5t110.5 -37.5q93 0 132 69l95 -29q-29 -57 -85.5 -92.5t-135.5 -35.5q-119 0 -182 88v-75h-94v76q-63 -89 -181 -89q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h94v-78q65 91 178 91
q112 0 179 -72t67 -185zM575 287h312q-5 63 -45.5 101t-107.5 38q-65 0 -109.5 -40.5t-49.5 -98.5zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="579" 
d="M303 81q52 0 91 26.5t50 63.5l97 -27q-14 -53 -65.5 -98t-128.5 -56l-14 -47q36 -3 58.5 -25.5t22.5 -52.5q0 -41 -31.5 -64.5t-80.5 -23.5q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l27 84q-101 12 -165.5 85.5t-64.5 177.5
q0 113 74.5 188.5t188.5 75.5q94 0 158.5 -48.5t80.5 -110.5l-97 -28q-11 37 -50 65t-91 28q-72 0 -117 -49.5t-45 -120.5q0 -70 45.5 -120.5t116.5 -50.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="592" 
d="M356 582h-92l-103 138h117zM307 -13q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-27 -59 -89 -101t-152 -42zM142 289l309 1q-5 60 -45.5 98t-107.5 38
q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="592" 
d="M244 582l78 138h117l-102 -138h-93zM307 -13q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-27 -59 -89 -101t-152 -42zM142 289l309 1q-5 60 -45.5 98t-107.5 38
q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="592" 
d="M300 663l-62 -85h-99l109 137h104l109 -137h-99zM554 261q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-27 -59 -89 -101t-152 -42q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5zM142 289l309 1q-5 60 -45.5 98
t-107.5 38q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="592" 
d="M205 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM394 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM307 -13q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5
q119 0 187.5 -72.5t68.5 -182.5q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-27 -59 -89 -101t-152 -42zM142 289l309 1q-5 60 -45.5 98t-107.5 38q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="245" 
d="M178 582h-92l-103 138h117zM70 0v503h105v-503h-105z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="245" 
d="M66 582l78 138h117l-102 -138h-93zM70 0v503h105v-503h-105z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="245" 
d="M174 715l109 -137h-99l-62 85l-62 -85h-99l109 137h104zM70 0v503h105v-503h-105z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="245" 
d="M40 571q-25 0 -41.5 16t-16.5 40t16.5 40t41.5 16q24 0 40.5 -16t16.5 -40t-16.5 -40t-40.5 -16zM205 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM70 0v503h105v-503h-105z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="608" 
d="M422 585q146 -156 146 -336q0 -118 -73.5 -190.5t-189.5 -72.5q-113 0 -189 72t-76 179q0 106 74 173t184 67q66 0 113 -32q-33 58 -87 115l-187 -47l-15 58l151 38q-47 44 -117 91h138q48 -38 78 -66l129 33l15 -58zM305 83q71 0 114 42.5t43 109.5q0 64 -43.5 107
t-113.5 43q-71 0 -114 -42.5t-43 -106.5q0 -67 43 -110t114 -43z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M251 633q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131q-31 0 -74 28t-57 28zM348 516q96 0 148 -59t52 -162v-295h-104v276q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v503h105v-75q62 88 173 88z
" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="611" 
d="M362 582h-92l-103 138h117zM306 -13q-115 0 -191 75.5t-76 189.5q0 113 76 188.5t191 75.5t191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5zM306 82q72 0 118.5 48.5t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5t-46.5 -121.5q0 -72 46.5 -120.5
t118.5 -48.5z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="611" 
d="M250 582l78 138h117l-102 -138h-93zM306 -13q-115 0 -191 75.5t-76 189.5q0 113 76 188.5t191 75.5t191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5zM306 82q72 0 118.5 48.5t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5t-46.5 -121.5q0 -72 46.5 -120.5
t118.5 -48.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="611" 
d="M307 663l-62 -85h-99l109 137h104l109 -137h-99zM306 516q115 0 191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5t-191 75.5t-76 189.5q0 113 76 188.5t191 75.5zM306 82q72 0 118.5 48.5t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5t-46.5 -121.5
q0 -72 46.5 -120.5t118.5 -48.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="611" 
d="M377 577q-31 0 -74 28t-57 28q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131zM306 -13q-115 0 -191 75.5t-76 189.5q0 113 76 188.5t191 75.5t191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5zM306 82q72 0 118.5 48.5
t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5t-46.5 -121.5q0 -72 46.5 -120.5t118.5 -48.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="611" 
d="M211 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM400 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM306 -13q-115 0 -191 75.5t-76 189.5q0 113 76 188.5t191 75.5
t191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5zM306 82q72 0 118.5 48.5t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5t-46.5 -121.5q0 -72 46.5 -120.5t118.5 -48.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="580" 
d="M290 469q-24 0 -41.5 17.5t-17.5 41.5q0 23 17.5 40t41.5 17q25 0 42 -17t17 -40q0 -24 -17 -41.5t-42 -17.5zM65 308v85h450v-85h-450zM290 117q-24 0 -41.5 17.5t-17.5 41.5q0 23 17.5 40t41.5 17q25 0 42 -17t17 -40q0 -24 -17 -41.5t-42 -17.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="611" 
d="M484 453q89 -78 89 -201q0 -114 -76 -189.5t-191 -75.5q-68 0 -128 30l-21 -31h-71l44 63q-91 77 -91 203q0 113 76 188.5t191 75.5q71 0 130 -31l21 31h71zM141 251q0 -72 46 -120l192 274q-34 16 -73 16q-72 0 -118.5 -48.5t-46.5 -121.5zM306 82q72 0 118.5 48.5
t46.5 120.5q0 70 -45 120l-191 -274q31 -15 71 -15z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M363 582h-92l-103 138h117zM439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M446 720l-102 -138h-93l78 138h117zM439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M307 663l-62 -85h-99l109 137h104l109 -137h-99zM439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M212 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM401 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59
t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="566" 
d="M427 720l-102 -138h-93l78 138h117zM446 503h112l-306 -713h-108l94 221l-230 492h114l168 -374z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="644" 
d="M355 516q107 0 179 -75.5t72 -188.5q0 -114 -72 -189.5t-179 -75.5q-117 0 -180 89v-286h-105v940h105v-303q63 89 180 89zM338 81q72 0 118 48.5t46 122.5t-46 122t-118 48q-70 0 -118 -48.5t-48 -121.5q0 -74 48 -122.5t118 -48.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="566" 
d="M193 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM382 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM446 503h112l-306 -713h-108l94 221l-230 492h114l168 -374z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="709" 
d="M492 866v-77h-275v77h275zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266h259l-129 331z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="644" 
d="M173 591v77h275v-77h-275zM289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="709" 
d="M354 764q-76 0 -116.5 39.5t-40.5 101.5h84q0 -33 18 -53t55 -20q38 0 56 20t18 53h83q0 -62 -40.5 -101.5t-116.5 -39.5zM588 0l-67 170h-333l-67 -170h-113l277 700h139l277 -700h-113zM225 266h259l-129 331z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="644" 
d="M310 566q-76 0 -116.5 39.5t-40.5 101.5h84q0 -33 18 -53t55 -20q38 0 56 20t18 53h83q0 -62 -40.5 -101.5t-116.5 -39.5zM289 -13q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h104v-503h-104v76q-63 -89 -181 -89zM306 81
q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="709" 
d="M722 -119l19 -63q-32 -41 -96 -41q-46 0 -74.5 25.5t-28.5 69.5q0 63 78 128h-32l-67 170h-333l-67 -170h-113l277 700h139l277 -700q-77 -65 -77 -112q0 -38 38 -38q34 0 60 31zM225 266h259l-129 331z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="644" 
d="M595 -119l19 -63q-32 -41 -96 -41q-46 0 -74.5 25.5t-28.5 69.5q0 63 78 128h-23v76q-63 -89 -181 -89q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v76h104v-503q-77 -65 -77 -112q0 -38 38 -38q34 0 60 31zM306 81q70 0 118 48.5
t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="778" 
d="M347 780l78 138h117l-102 -138h-93zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t260 103.5q126 0 214.5 -63.5t118.5 -155.5l-105 -26q-23 62 -84 103t-144 41q-113 0 -183.5 -75.5t-70.5 -184.5q0 -110 70.5 -186.5t183.5 -76.5q83 0 144 40t83 102l105 -27
q-30 -91 -118 -154t-213 -63z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="579" 
d="M246 582l78 138h117l-102 -138h-93zM303 -13q-114 0 -189 76t-75 189t74.5 188.5t188.5 75.5q94 0 158.5 -48.5t80.5 -110.5l-97 -28q-11 37 -50 65t-91 28q-72 0 -117 -49.5t-45 -120.5q0 -70 45.5 -120.5t116.5 -50.5q52 0 91 26.5t50 63.5l97 -27q-16 -61 -80 -109
t-158 -48z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="778" 
d="M403 770q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t260 103.5q126 0 214.5 -63.5t118.5 -155.5l-105 -26q-23 62 -84 103t-144 41q-113 0 -183.5 -75.5
t-70.5 -184.5q0 -110 70.5 -186.5t183.5 -76.5q83 0 144 40t83 102l105 -27q-30 -91 -118 -154t-213 -63z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="579" 
d="M302 572q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM303 -13q-114 0 -189 76t-75 189t74.5 188.5t188.5 75.5q94 0 158.5 -48.5t80.5 -110.5l-97 -28q-11 37 -50 65t-91 28q-72 0 -117 -49.5t-45 -120.5
q0 -70 45.5 -120.5t116.5 -50.5q52 0 91 26.5t50 63.5l97 -27q-16 -61 -80 -109t-158 -48z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="778" 
d="M352 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM403 89q83 0 144 40t83 102l105 -27q-30 -91 -118 -154t-213 -63q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t260 103.5q126 0 214.5 -63.5t118.5 -155.5l-105 -26q-23 62 -84 103t-144 41q-113 0 -183.5 -75.5
t-70.5 -184.5q0 -110 70.5 -186.5t183.5 -76.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="579" 
d="M251 578l-109 137h99l62 -85l62 85h99l-109 -137h-104zM303 81q52 0 91 26.5t50 63.5l97 -27q-16 -61 -80 -109t-158 -48q-114 0 -189 76t-75 189t74.5 188.5t188.5 75.5q94 0 158.5 -48.5t80.5 -110.5l-97 -28q-11 37 -50 65t-91 28q-72 0 -117 -49.5t-45 -120.5
q0 -70 45.5 -120.5t116.5 -50.5z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="734" 
d="M292 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM355 700q155 0 247.5 -97.5t92.5 -252.5q0 -154 -92 -252t-248 -98h-285v700h285zM353 98q107 0 170 71t63 183q0 110 -63 180t-170 70h-175v-504h175z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="741" 
d="M470 730h104v-730h-104v76q-63 -89 -181 -89q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v303zM650 730h99l-30 -192h-81zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="764" 
d="M386 700q155 0 247 -97.5t92 -252.5q0 -154 -91.5 -252t-247.5 -98h-285v302h-71v93h71v305h285zM384 98q107 0 169.5 71t62.5 183q0 110 -62.5 180t-169.5 70h-176v-207h194v-93h-194v-204h176z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="652" 
d="M644 649v-66h-70v-583h-104v76q-63 -89 -181 -89q-107 0 -178.5 75.5t-71.5 189.5q0 113 71.5 188.5t178.5 75.5q118 0 181 -89v156h-176v66h176v81h104v-81h70zM306 81q70 0 118 48.5t48 122.5q0 73 -48 121.5t-118 48.5q-72 0 -118 -48t-46 -122t46 -122.5t118 -48.5z
" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="624" 
d="M457 866v-77h-275v77h275zM574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="592" 
d="M162 591v77h275v-77h-275zM307 -13q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-27 -59 -89 -101t-152 -42zM142 289l309 1q-5 60 -45.5 98t-107.5 38
q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="624" 
d="M319 770q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="592" 
d="M300 572q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM307 -13q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28
q-27 -59 -89 -101t-152 -42zM142 289l309 1q-5 60 -45.5 98t-107.5 38q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="624" 
d="M594 -119l19 -63q-32 -41 -96 -41q-46 0 -74.5 25.5t-28.5 69.5q0 63 78 128h-422v700h504v-98h-397v-199h346v-94h-346v-210h396v-99q-77 -65 -77 -112q0 -38 38 -38q34 0 60 31z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="592" 
d="M554 261q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-31 -67 -95 -105q-43 -35 -68.5 -72t-25.5 -64q0 -39 38 -39q34 0 60 31l19 -63q-32 -41 -96 -41q-46 0 -74.5 25.5t-28.5 69.5q0 58 64 117q-22 -2 -34 -2q-119 0 -193.5 73t-74.5 191
q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5zM142 289l309 1q-5 60 -45.5 98t-107.5 38q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="624" 
d="M268 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM574 602h-397v-199h346v-94h-346v-210h396v-99h-503v700h504v-98z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="592" 
d="M248 578l-109 137h99l62 -85l62 85h99l-109 -137h-104zM554 261q0 -19 -3 -42h-409q5 -64 49 -103t118 -39q104 0 143 81l96 -28q-27 -59 -89 -101t-152 -42q-119 0 -193.5 73t-74.5 191q0 114 72 189.5t187 75.5q119 0 187.5 -72.5t68.5 -182.5zM142 289l309 1
q-5 60 -45.5 98t-107.5 38q-65 0 -107.5 -40t-48.5 -97z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="799" 
d="M401 764q-76 0 -116.5 39.5t-40.5 101.5h84q0 -33 18 -53t55 -20q38 0 56 20t18 53h83q0 -62 -40.5 -101.5t-116.5 -39.5zM760 380v-56q0 -154 -98 -245.5t-253 -91.5q-159 0 -264.5 104.5t-105.5 259.5q0 154 105 258t260 104q120 0 208.5 -59t123.5 -151l-102 -28
q-28 63 -86.5 100t-142.5 37q-113 0 -185 -76t-72 -184q0 -110 73 -187t194 -77q100 0 163 54.5t71 144.5l-244 -1v94h355z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="642" 
d="M312 566q-76 0 -116.5 39.5t-40.5 101.5h84q0 -33 18 -53t55 -20q38 0 56 20t18 53h83q0 -62 -40.5 -101.5t-116.5 -39.5zM467 503h105v-485q0 -106 -70.5 -173.5t-184.5 -67.5q-167 0 -240 131l92 34q46 -77 150 -77q70 0 108.5 42.5t39.5 109.5v74q-63 -85 -177 -85
q-109 0 -180 73t-71 182q0 112 70.5 183.5t180.5 71.5q114 0 177 -84v71zM306 100q70 0 117 46t47 115t-47 114.5t-117 45.5q-72 0 -118 -45t-46 -115t46 -115.5t118 -45.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="799" 
d="M401 770q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM760 380v-56q0 -154 -98 -245.5t-253 -91.5q-159 0 -264.5 104.5t-105.5 259.5q0 154 105 258t260 104q120 0 208.5 -59t123.5 -151l-102 -28q-28 63 -86.5 100
t-142.5 37q-113 0 -185 -76t-72 -184q0 -110 73 -187t194 -77q100 0 163 54.5t71 144.5l-244 -1v94h355z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="642" 
d="M312 572q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM467 503h105v-485q0 -106 -70.5 -173.5t-184.5 -67.5q-167 0 -240 131l92 34q46 -77 150 -77q70 0 108.5 42.5t39.5 109.5v74q-63 -85 -177 -85q-109 0 -180 73
t-71 182q0 112 70.5 183.5t180.5 71.5q114 0 177 -84v71zM306 100q70 0 117 46t47 115t-47 114.5t-117 45.5q-72 0 -118 -45t-46 -115t46 -115.5t118 -45.5z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="799" 
d="M760 380v-56q0 -154 -98 -245.5t-253 -91.5q-159 0 -264.5 104.5t-105.5 259.5q0 154 105 258t260 104q120 0 208.5 -59t123.5 -151l-102 -28q-28 63 -86.5 100t-142.5 37q-113 0 -185 -76t-72 -184q0 -110 73 -187t194 -77q100 0 163 54.5t71 144.5l-244 -1v94h355z
M346 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="642" 
d="M372 775l-13 -189h-98l30 189h81zM467 503h105v-485q0 -106 -70.5 -173.5t-184.5 -67.5q-167 0 -240 131l92 34q46 -77 150 -77q70 0 108.5 42.5t39.5 109.5v74q-63 -85 -177 -85q-109 0 -180 73t-71 182q0 112 70.5 183.5t180.5 71.5q114 0 177 -84v71zM306 100
q70 0 117 46t47 115t-47 114.5t-117 45.5q-72 0 -118 -45t-46 -115t46 -115.5t118 -45.5z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="750" 
d="M735 590v-72h-69v-518h-108v308h-364v-308h-109v518h-70v72h70v110h109v-110h364v110h108v-110h69zM558 406v112h-364v-112h364z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="619" 
d="M354 516q96 0 148 -59t52 -162v-295h-104v276q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v583h-68v66h68v81h105v-81h177v-66h-177v-155q62 88 173 88z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="250" 
d="M-13 789v77h275v-77h-275zM70 0v700h110v-700h-110z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="245" 
d="M-16 591v77h275v-77h-275zM70 0v503h105v-503h-105z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="250" 
d="M209 -119l19 -63q-32 -41 -97 -41q-45 0 -73.5 25.5t-28.5 68.5q0 64 69 129h-28v700h110v-700q-69 -64 -69 -111q0 -39 37 -39q35 0 61 31z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="245" 
d="M122 572q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM205 -119l19 -63q-32 -41 -97 -41q-45 0 -73.5 25.5t-28.5 68.5q0 64 69 129h-24v503h105v-503q-68 -65 -68 -111q0 -39 37 -39q35 0 61 31z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="250" 
d="M125 770q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM70 0v700h110v-700h-110z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="245" 
d="M70 0v503h105v-503h-105z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="691" 
d="M369 391l307 -391h-133l-245 312l-119 -130v-182h-109v700h109v-374l334 374h139zM283 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="542" 
d="M308 285l224 -285h-120l-170 213l-67 -72v-141h-105v730h105v-456l210 229h124zM231 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="572" 
d="M277 918l-102 -138h-93l78 138h117zM179 100h368v-100h-477v700h109v-600z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="245" 
d="M67 810l78 138h117l-102 -138h-93zM70 0v730h105v-730h-105z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="572" 
d="M179 100h368v-100h-477v700h109v-600zM248 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="245" 
d="M70 0v730h105v-730h-105zM61 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="577" 
d="M179 100h368v-100h-477v700h109v-600zM542 700l-29 -177h-82l11 177h100z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="349" 
d="M70 0v730h105v-730h-105zM238 538l13 192h98l-30 -192h-81z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="594" 
d="M200 100h369v-100h-477v263l-67 -29v93l67 29v344h108v-297l202 87v-93l-202 -87v-210z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="296" 
d="M286 516v-93l-85 -40v-380h-105v331l-86 -40v93l86 40v306h105v-257z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="728" 
d="M503 918l-102 -138h-93l78 138h117zM549 700h109v-700h-91l-388 507v-507h-109v700h91l388 -507v507z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M451 720l-102 -138h-93l78 138h117zM348 516q96 0 148 -59t52 -162v-295h-104v276q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v503h105v-75q62 88 173 88z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="728" 
d="M549 700h109v-700h-91l-388 507v-507h-109v700h91l388 -507v507zM307 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" 
d="M348 516q96 0 148 -59t52 -162v-295h-104v276q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v503h105v-75q62 88 173 88zM253 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="728" 
d="M313 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM549 700h109v-700h-91l-388 507v-507h-109v700h91l388 -507v507z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M260 578l-109 137h99l62 -85l62 85h99l-109 -137h-104zM348 516q96 0 148 -59t52 -162v-295h-104v276q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v503h105v-75q62 88 173 88z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="728" 
d="M549 700h109v-726q0 -87 -40.5 -136t-121.5 -49q-44 0 -88 20l24 84q22 -15 54 -15q31 0 47.5 24t16.5 66v54l-371 485v-507h-109v700h91l388 -507v507z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M348 516q96 0 148 -59t52 -162v-327q0 -91 -40 -141.5t-122 -50.5q-44 0 -88 21l24 84q23 -15 51 -15q71 0 71 95v315q0 67 -31.5 104.5t-92.5 37.5q-66 0 -105.5 -46t-39.5 -122v-250h-105v503h105v-75q62 88 173 88z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="807" 
d="M266 789v77h275v-77h-275zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t261 103.5q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5zM404 87q113 0 184 76.5t71 188.5q0 110 -71.5 185.5t-183.5 75.5q-113 0 -184.5 -75.5t-71.5 -185.5
q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="611" 
d="M169 591v77h275v-77h-275zM306 -13q-115 0 -191 75.5t-76 189.5q0 113 76 188.5t191 75.5t191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5zM306 82q72 0 118.5 48.5t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5t-46.5 -121.5q0 -72 46.5 -120.5
t118.5 -48.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="807" 
d="M264 780l79 138h99l-84 -138h-94zM429 780l88 138h99l-93 -138h-94zM404 -13q-156 0 -260.5 104.5t-104.5 259.5t104 258.5t261 103.5q156 0 260 -103.5t104 -258.5t-104 -259.5t-260 -104.5zM404 87q113 0 184 76.5t71 188.5q0 110 -71.5 185.5t-183.5 75.5
q-113 0 -184.5 -75.5t-71.5 -185.5q0 -112 71.5 -188.5t184.5 -76.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="611" 
d="M167 582l79 138h99l-84 -138h-94zM332 582l88 138h99l-93 -138h-94zM306 -13q-115 0 -191 75.5t-76 189.5q0 113 76 188.5t191 75.5t191 -75.5t76 -188.5q0 -114 -76 -189.5t-191 -75.5zM306 82q72 0 118.5 48.5t46.5 120.5q0 73 -46.5 121.5t-118.5 48.5t-118.5 -48.5
t-46.5 -121.5q0 -72 46.5 -120.5t118.5 -48.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1004" 
d="M954 602h-351v-199h300v-94h-300v-210h350v-99h-570q-157 0 -250.5 98t-93.5 252q0 155 93.5 252.5t250.5 97.5h571v-98zM383 99h113v503h-113q-108 0 -171.5 -70t-63.5 -180q0 -112 63.5 -182.5t171.5 -70.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1024" 
d="M986 259q0 -21 -3 -44h-408q8 -63 52.5 -101.5t110.5 -38.5q46 0 80.5 19.5t51.5 49.5l95 -29q-26 -54 -85.5 -91t-141.5 -37q-147 0 -218 114q-34 -53 -90 -83.5t-124 -30.5q-115 0 -191 76t-76 189t76 188.5t191 75.5q68 0 124 -30.5t90 -83.5q33 53 87.5 83.5
t123.5 30.5q119 0 187 -73t68 -184zM573 287l312 1q-5 62 -45.5 100t-107.5 38q-66 0 -109.5 -40.5t-49.5 -98.5zM306 83q71 0 118 48.5t47 120.5q0 71 -47 120t-118 49t-118 -49t-47 -120q0 -72 47 -120.5t118 -48.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="686" 
d="M466 918l-102 -138h-93l78 138h117zM526 0l-171 260h-177v-260h-108v700h333q105 0 167.5 -67t62.5 -154q0 -72 -43 -131t-120 -79l181 -269h-125zM178 603v-247h219q59 0 95 36t36 87t-35.5 87.5t-92.5 36.5h-222z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="370" 
d="M341 720l-102 -138h-93l78 138h117zM175 423q23 47 67.5 70.5t104.5 15.5v-98q-83 12 -127.5 -32.5t-44.5 -151.5v-227h-105v503h105v-80z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="686" 
d="M470 269l181 -269h-125l-171 260h-177v-260h-108v700h333q105 0 167.5 -67t62.5 -154q0 -72 -43 -131t-120 -79zM178 603v-247h219q59 0 95 36t36 87t-35.5 87.5t-92.5 36.5h-222zM284 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="370" 
d="M175 423q23 47 67.5 70.5t104.5 15.5v-98q-83 12 -127.5 -32.5t-44.5 -151.5v-227h-105v503h105v-80zM61 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="686" 
d="M276 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM526 0l-171 260h-177v-260h-108v700h333q105 0 167.5 -67t62.5 -154q0 -72 -43 -131t-120 -79l181 -269h-125zM178 603v-247h219q59 0 95 36t36 87t-35.5 87.5t-92.5 36.5h-222z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="370" 
d="M151 578l-109 137h99l62 -85l62 85h99l-109 -137h-104zM175 423q23 47 67.5 70.5t104.5 15.5v-98q-83 12 -127.5 -32.5t-44.5 -151.5v-227h-105v503h105v-80z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="665" 
d="M270 780l78 138h117l-102 -138h-93zM347 -13q-117 0 -201 61t-114 150l103 31q24 -64 81.5 -105t136.5 -41q70 0 114.5 33t44.5 76q0 35 -25.5 59t-85.5 43l-176 56q-157 49 -157 171q0 79 69 135.5t173 56.5q107 0 184.5 -49.5t109.5 -124.5l-101 -30q-25 49 -75.5 78.5
t-117.5 29.5q-58 0 -95.5 -27.5t-37.5 -65.5q0 -30 21 -49.5t68 -33.5l172 -55q91 -29 137 -70.5t46 -116.5q0 -90 -76.5 -151t-197.5 -61z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="515" 
d="M189 582l78 138h117l-102 -138h-93zM261 -13q-86 0 -151.5 44.5t-85.5 108.5l95 28q14 -41 53.5 -68t91.5 -27q47 0 76 20t29 48q0 23 -15 36.5t-50 23.5l-135 40q-120 36 -120 129q0 60 56 103t136 43q79 0 137 -35t81 -92l-93 -27q-15 30 -48 50t-78 20
q-38 0 -63 -18.5t-25 -42.5q0 -33 54 -50l134 -39q62 -18 97 -48.5t35 -88.5q0 -68 -59.5 -113t-151.5 -45z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="665" 
d="M621 199q0 -83 -66 -142t-173 -69l-14 -45q36 -3 58.5 -25.5t22.5 -52.5q0 -41 -31.5 -64.5t-80.5 -23.5q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l27 84q-101 11 -173 70t-99 139l103 31q24 -64 81.5 -105t136.5 -41q70 0 114.5 33
t44.5 76q0 35 -25.5 59t-85.5 43l-176 56q-157 49 -157 171q0 79 69 135.5t173 56.5q107 0 184.5 -49.5t109.5 -124.5l-101 -30q-25 49 -75.5 78.5t-117.5 29.5q-58 0 -95.5 -27.5t-37.5 -65.5q0 -30 21 -49.5t68 -33.5l172 -55q91 -29 137 -70.5t46 -116.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="515" 
d="M472 145q0 -62 -50 -106t-131 -51l-14 -45q36 -3 58.5 -25.5t22.5 -52.5q0 -41 -31.5 -64.5t-80.5 -23.5q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l27 86q-70 12 -121 53t-68 96l95 28q14 -41 53.5 -68t91.5 -27q47 0 76 20t29 48
q0 23 -15 36.5t-50 23.5l-135 40q-120 36 -120 129q0 60 56 103t136 43q79 0 137 -35t81 -92l-93 -27q-15 30 -48 50t-78 20q-38 0 -63 -18.5t-25 -42.5q0 -33 54 -50l134 -39q62 -18 97 -48.5t35 -88.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="665" 
d="M274 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM438 386q91 -29 137 -70.5t46 -116.5q0 -90 -76.5 -151t-197.5 -61q-117 0 -201 61t-114 150l103 31q24 -64 81.5 -105t136.5 -41q70 0 114.5 33t44.5 76q0 35 -25.5 59t-85.5 43l-176 56q-157 49 -157 171
q0 79 69 135.5t173 56.5q107 0 184.5 -49.5t109.5 -124.5l-101 -30q-25 49 -75.5 78.5t-117.5 29.5q-58 0 -95.5 -27.5t-37.5 -65.5q0 -30 21 -49.5t68 -33.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="515" 
d="M193 578l-109 137h99l62 -85l62 85h99l-109 -137h-104zM340 282q62 -18 97 -48.5t35 -88.5q0 -68 -59.5 -113t-151.5 -45q-86 0 -151.5 44.5t-85.5 108.5l95 28q14 -41 53.5 -68t91.5 -27q47 0 76 20t29 48q0 23 -15 36.5t-50 23.5l-135 40q-120 36 -120 129q0 60 56 103
t136 43q79 0 137 -35t81 -92l-93 -27q-15 30 -48 50t-78 20q-38 0 -63 -18.5t-25 -42.5q0 -33 54 -50z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="632" 
d="M371 602v-602l-17 -57q36 -3 58.5 -25.5t22.5 -52.5q0 -41 -31.5 -64.5t-80.5 -23.5q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l30 95h-31v602h-247v98h602v-98h-246z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="411" 
d="M281 -57q36 -3 58.5 -25.5t22.5 -52.5q0 -41 -31.5 -64.5t-80.5 -23.5q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l29 92q-109 26 -109 166v247h-92v93h92v168h104v-168h160v-93h-160v-247q0 -77 72 -77q33 0 66 16l26 -86q-41 -18 -82 -22
z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="632" 
d="M265 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM617 700v-98h-246v-602h-109v602h-247v98h602z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="425" 
d="M381 722l-28 -169h-80l11 169h97zM286 86q33 0 66 16l26 -86q-57 -24 -111 -24q-79 0 -118 45.5t-39 125.5v247h-92v93h92v168h104v-168h160v-93h-160v-247q0 -77 72 -77z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="632" 
d="M617 602h-246v-218h155v-87h-155v-297h-109v297h-166v87h166v218h-247v98h602v-98z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="412" 
d="M353 102l27 -86q-57 -24 -113 -24q-79 0 -118 45.5t-39 125.5v95h-85v71h85v96h-92v78h92v168h104v-168h162v-78h-162v-96h140v-71h-140v-95q0 -77 75 -77q32 0 64 16z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="716" 
d="M496 866v-77h-275v77h275zM544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M444 668v-77h-275v77h275zM439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="716" 
d="M359 747q-50 0 -84 32.5t-34 81.5t34 81.5t84 32.5t83.5 -32.5t33.5 -81.5t-33.5 -81.5t-83.5 -32.5zM359 912q-24 0 -38.5 -14t-14.5 -37t14 -37.5t39 -14.5q24 0 38 14.5t14 37.5t-14 37t-38 14zM544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5
v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M307 549q-50 0 -84 32.5t-34 81.5t34 81.5t84 32.5t83.5 -32.5t33.5 -81.5t-33.5 -81.5t-83.5 -32.5zM307 714q-24 0 -38.5 -14t-14.5 -37t14 -37.5t39 -14.5q24 0 38 14.5t14 37.5t-14 37t-38 14zM439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162
v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="716" 
d="M397 918l-84 -138h-94l79 138h99zM478 780h-94l88 138h99zM544 700h110v-417q0 -134 -78.5 -215t-217.5 -81t-217 80.5t-78 215.5v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M345 720l-84 -138h-94l79 138h99zM426 582h-94l88 138h99zM439 503h104v-503h-104v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="716" 
d="M544 700h110v-417q0 -168 -117 -247q-47 -36 -76.5 -75.5t-29.5 -67.5q0 -38 38 -38q34 0 60 31l19 -63q-32 -41 -96 -41q-46 0 -74.5 25.5t-28.5 69.5q0 55 60 113q-24 -3 -51 -3q-139 0 -217 80.5t-78 215.5v417h109v-415q0 -90 48 -144t138 -54t138 54t48 144v415z
" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M564 -119l19 -63q-32 -41 -96 -41q-46 0 -74.5 25.5t-28.5 69.5q0 63 78 128h-23v76q-61 -89 -174 -89q-95 0 -147.5 59t-52.5 162v295h105v-276q0 -67 31.5 -104.5t92.5 -37.5q65 0 105 46t40 122v250h104v-503q-77 -65 -77 -112q0 -38 38 -38q34 0 60 31z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="994" 
d="M498 861l-62 -85h-99l109 137h104l109 -137h-99zM870 700h114l-217 -700h-114l-156 520l-157 -520h-114l-216 700h114l162 -549l162 549h98l162 -548z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="790" 
d="M396 663l-62 -85h-99l109 137h104l109 -137h-99zM674 503h109l-173 -503h-100l-115 352l-115 -352h-99l-173 503h108l118 -372l117 372h89l116 -371z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="665" 
d="M334 861l-62 -85h-99l109 137h104l109 -137h-99zM536 700h122l-270 -437v-263h-110v263l-270 437h122l203 -336z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="566" 
d="M288 663l-62 -85h-99l109 137h104l109 -137h-99zM446 503h112l-306 -713h-108l94 221l-230 492h114l168 -374z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="665" 
d="M238 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM427 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM536 700h122l-270 -437v-263h-110v263l-270 437h122l203 -336z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="630" 
d="M457 918l-102 -138h-93l78 138h117zM188 97h407v-97h-557v73l398 530h-389v97h537v-73z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="523" 
d="M401 720l-102 -138h-93l78 138h117zM183 89h299v-89h-439v67l290 347h-284v89h424v-69z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="630" 
d="M318 770q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM188 97h407v-97h-557v73l398 530h-389v97h537v-73z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="523" 
d="M262 572q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM183 89h299v-89h-439v67l290 347h-284v89h424v-69z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="630" 
d="M267 776l-109 137h99l62 -85l62 85h99l-109 -137h-104zM188 97h407v-97h-557v73l398 530h-389v97h537v-73z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="523" 
d="M211 578l-109 137h99l62 -85l62 85h99l-109 -137h-104zM183 89h299v-89h-439v67l290 347h-284v89h424v-69z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="516" 
d="M388 744q71 0 113 -39l-39 -81q-27 29 -72 29q-65 0 -79 -103l-15 -117h138v-86h-149l-40 -308q-12 -95 -56 -145t-117 -50q-66 0 -107 42l38 77q27 -29 66 -29q66 0 78 105l40 308h-121v86h132l15 117q25 194 175 194z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="665" 
d="M347 -13q-117 0 -201 61t-114 150l103 31q24 -64 81.5 -105t136.5 -41q70 0 114.5 33t44.5 76q0 35 -25.5 59t-85.5 43l-176 56q-157 49 -157 171q0 79 69 135.5t173 56.5q107 0 184.5 -49.5t109.5 -124.5l-101 -30q-25 49 -75.5 78.5t-117.5 29.5q-58 0 -95.5 -27.5
t-37.5 -65.5q0 -30 21 -49.5t68 -33.5l172 -55q91 -29 137 -70.5t46 -116.5q0 -90 -76.5 -151t-197.5 -61zM281 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="515" 
d="M261 -13q-86 0 -151.5 44.5t-85.5 108.5l95 28q14 -41 53.5 -68t91.5 -27q47 0 76 20t29 48q0 23 -15 36.5t-50 23.5l-135 40q-120 36 -120 129q0 60 56 103t136 43q79 0 137 -35t81 -92l-93 -27q-15 30 -48 50t-78 20q-38 0 -63 -18.5t-25 -42.5q0 -33 54 -50l134 -39
q62 -18 97 -48.5t35 -88.5q0 -68 -59.5 -113t-151.5 -45zM190 -257l13 189h98l-30 -189h-81z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="632" 
d="M617 700v-98h-246v-602h-109v602h-247v98h602zM258 -258l13 190h98l-30 -190h-81z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="411" 
d="M378 16q-57 -24 -111 -24q-79 0 -118 45.5t-39 125.5v247h-92v93h92v168h104v-168h160v-93h-160v-247q0 -77 72 -77q33 0 66 16zM189 -258l13 190h97l-29 -190h-81z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="322" 
d="M322 578h-99l-62 85l-62 -85h-99l109 137h104z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="322" 
d="M223 715h99l-109 -137h-104l-109 137h99l62 -85z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="314" 
d="M157 566q-76 0 -116.5 39.5t-40.5 101.5h84q0 -33 18 -53t55 -20q38 0 56 20t18 53h83q0 -62 -40.5 -101.5t-116.5 -39.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="130" 
d="M65 583q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="235" 
d="M118 549q-50 0 -84 32.5t-34 81.5t34 81.5t84 32.5t83.5 -32.5t33.5 -81.5t-33.5 -81.5t-83.5 -32.5zM118 611q24 0 38 14.5t14 37.5t-14 37t-38 14t-38.5 -14t-14.5 -37t14.5 -37.5t38.5 -14.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="200" 
d="M104 -223q-46 0 -75 25.5t-29 69.5q0 64 79 128h81q-77 -64 -77 -112q0 -38 37 -38q36 0 60 31l20 -63q-32 -41 -96 -41z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="317" 
d="M229 577q-31 0 -74 28t-57 28q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="352" 
d="M0 582l79 138h99l-84 -138h-94zM165 582l88 138h99l-93 -138h-94z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M0 582h-92l-103 138h117z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M-195 582l78 138h117l-102 -138h-93z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M1 578h-100l-62 85l-62 -85h-99l109 137h104z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-88 577q-31 0 -74 28t-57 28q-25 0 -25 -52h-73q0 130 89 130q31 0 74 -27.5t57 -27.5q25 0 25 52h72q0 -131 -88 -131z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-275 591v77h275v-77h-275z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M-157 566q-76 0 -116.5 39.5t-40.5 101.5h84q0 -33 18 -53t55 -20q38 0 56 20t18 53h83q0 -62 -40.5 -101.5t-116.5 -39.5z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-65 572q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-247 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM-58 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 42 -16t17 -40t-17 -40t-42 -16z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M-117 549q-50 0 -84 32.5t-34 81.5t34 81.5t84 32.5t83.5 -32.5t33.5 -81.5t-33.5 -81.5t-83.5 -32.5zM-117 611q24 0 38 14.5t14 37.5t-14 37t-38 14t-38.5 -14t-14.5 -37t14 -37.5t39 -14.5z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-352 582l79 138h99l-84 -138h-94zM-187 582l88 138h99l-93 -138h-94z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-99 715h100l-110 -137h-104l-109 137h99l62 -85z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M1 775l-14 -189h-98l30 189h82z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-111 -257l13 189h99l-31 -189h-81z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-112 -223q-59 0 -104 33l14 49q40 -28 82.5 -23.5t42.5 34.5q0 24 -31 33.5t-64 1.5l30 95h78l-17 -57q36 -3 59 -25.5t23 -52.5q0 -41 -32 -64.5t-81 -23.5z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-96 -223q-46 0 -75 25.5t-29 69.5q0 64 79 128h81q-77 -64 -77 -112q0 -38 37 -38q36 0 60 31l21 -63q-34 -41 -97 -41z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="706" 
d="M422 700l276 -700h-690l276 700h138zM159 97h388l-194 498z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="816" 
d="M776 369q0 -86 -38.5 -156t-104.5 -117h138v-96h-288v96q81 30 133.5 102t52.5 158q0 108 -73.5 182.5t-187.5 74.5t-187 -74.5t-73 -182.5q0 -86 52.5 -158t133.5 -102v-96h-289v96h138q-66 47 -104.5 117t-38.5 156q0 144 105 244t263 100q159 0 263.5 -100t104.5 -244
z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="654" 
d="M624 84l23 -83q-31 -14 -69 -14q-97 0 -123 76q-61 -76 -165 -76q-65 0 -115 36v-233h-105v713h105v-276q0 -66 33 -104t91 -38q65 0 105 46t40 122v250h104v-365q0 -60 42 -60q18 0 34 6z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="649" 
d="M623 416h-103v-279q0 -58 40 -58q21 0 34 8l23 -81q-32 -16 -69 -16q-128 0 -128 147v279h-194v-416h-100v416h-101v93h598v-93z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="994" 
d="M553 780h-92l-103 138h117zM870 700h114l-217 -700h-114l-156 520l-157 -520h-114l-216 700h114l162 -549l162 549h98l162 -548z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="790" 
d="M451 582h-92l-103 138h117zM674 503h109l-173 -503h-100l-115 352l-115 -352h-99l-173 503h108l118 -372l117 372h89l116 -371z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="994" 
d="M636 918l-102 -138h-93l78 138h117zM870 700h114l-217 -700h-114l-156 520l-157 -520h-114l-216 700h114l162 -549l162 549h98l162 -548z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="790" 
d="M534 720l-102 -138h-93l78 138h117zM674 503h109l-173 -503h-100l-115 352l-115 -352h-99l-173 503h108l118 -372l117 372h89l116 -371z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="994" 
d="M402 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM591 769q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM870 700h114l-217 -700h-114l-156 520l-157 -520h-114l-216 700h114
l162 -549l162 549h98l162 -548z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="790" 
d="M300 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM489 571q-24 0 -40.5 16t-16.5 40t16.5 40t40.5 16q25 0 41.5 -16t16.5 -40t-16.5 -40t-41.5 -16zM674 503h109l-173 -503h-100l-115 352l-115 -352h-99l-173 503h108
l118 -372l117 372h89l116 -371z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="665" 
d="M389 780h-92l-103 138h117zM536 700h122l-270 -437v-263h-110v263l-270 437h122l203 -336z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="566" 
d="M344 582h-92l-103 138h117zM446 503h112l-306 -713h-108l94 221l-230 492h114l168 -374z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="620" 
d="M65 265v87h490v-87h-490z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="920" 
d="M65 265v87h790v-87h-790z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="257" 
d="M207 700l-43 -258h-114l72 258h85z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="257" 
d="M50 442l44 258h113l-72 -258h-85z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="257" 
d="M50 -137l44 259h113l-72 -259h-85z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="429" 
d="M207 700l-43 -258h-114l72 258h85zM379 700l-44 -258h-113l72 258h85z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="429" 
d="M50 442l44 258h113l-72 -258h-85zM222 442l43 258h114l-72 -258h-85z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="429" 
d="M50 -137l44 259h113l-72 -259h-85zM222 -137l43 259h114l-72 -259h-85z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="505" 
d="M200 0v465h-160v96h160v169h105v-169h160v-96h-160v-465h-105z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="505" 
d="M200 0v170h-160v96h160v199h-160v96h160v169h105v-169h160v-96h-160v-199h160v-96h-160v-170h-105z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="321" 
d="M162 181q-47 1 -79.5 33t-32.5 78q0 44 32 74t80 32q46 1 77.5 -30.5t31.5 -75.5q0 -47 -31.5 -79.5t-77.5 -31.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="668" 
d="M119 -7q-29 0 -49 19.5t-20 48.5q0 28 20 47.5t49 19.5t49 -19.5t20 -47.5q0 -29 -20 -48.5t-49 -19.5zM334 -7q-29 0 -49 19.5t-20 48.5q0 28 20 47.5t49 19.5t49 -19.5t20 -47.5q0 -29 -20 -48.5t-49 -19.5zM549 -7q-29 0 -49 19.5t-20 48.5q0 28 20 47.5t49 19.5
t49 -19.5t20 -47.5q0 -29 -20 -48.5t-49 -19.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1464" 
d="M243 322q-83 0 -140.5 56t-57.5 137t57.5 136.5t140.5 55.5t140 -55.5t57 -136.5t-57 -137t-140 -56zM240 0l454 700h89l-453 -700h-90zM243 402q49 0 81 33t32 80t-32 79.5t-81 32.5t-81.5 -32.5t-32.5 -79.5t32.5 -80t81.5 -33zM781 -8q-83 0 -140.5 56t-57.5 137
q0 82 57.5 137.5t140.5 55.5t140 -55.5t57 -137.5q0 -81 -57 -137t-140 -56zM1222 -8q-83 0 -140.5 56t-57.5 137q0 82 57.5 137.5t140.5 55.5t140 -55.5t57 -137.5q0 -81 -57 -137t-140 -56zM781 73q49 0 81 32.5t32 79.5q0 48 -32 80t-81 32t-81.5 -32t-32.5 -80
q0 -46 32.5 -79t81.5 -33zM1222 73q49 0 81 32.5t32 79.5q0 48 -32 80t-81 32t-81.5 -32t-32.5 -80q0 -46 32.5 -79t81.5 -33z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="331" 
d="M266 500l-100 -180l100 -180h-98l-103 180l103 180h98z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="331" 
d="M164 500l102 -180l-102 -180h-99l100 180l-100 180h99z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="184" 
d="M-165 0l436 700h78l-436 -700h-78z" />
    <glyph glyph-name="zero.sups" unicode="&#x2070;" horiz-adv-x="394" 
d="M198 440q-80 0 -126.5 57.5t-46.5 153.5q0 95 46.5 152t126.5 57q77 0 124 -57.5t47 -151.5q0 -96 -46 -153.5t-125 -57.5zM198 517q43 0 65 36t22 98q0 61 -22.5 96.5t-64.5 35.5q-44 0 -66 -35.5t-22 -96.5q0 -63 22 -98.5t66 -35.5z" />
    <glyph glyph-name="four.sups" unicode="&#x2074;" horiz-adv-x="349" 
d="M334 600v-71h-49v-79h-78v79h-197v62l139 259h86l-132 -250h104v78h78v-78h49z" />
    <glyph glyph-name="five.sups" unicode="&#x2075;" horiz-adv-x="346" 
d="M188 705q60 0 96.5 -36.5t36.5 -94.5t-40.5 -96t-103.5 -38q-64 0 -105 32t-52 79l72 20q22 -61 82 -61q32 0 51 18.5t19 46.5q0 29 -19 47.5t-52 18.5q-45 0 -79 -24l-58 19l18 214h248v-70h-181l-7 -89q31 14 74 14z" />
    <glyph glyph-name="six.sups" unicode="&#x2076;" horiz-adv-x="335" 
d="M182 719q60 -3 99 -41.5t39 -96.5q0 -60 -43 -100.5t-109 -40.5q-67 0 -110.5 40.5t-42.5 100.5q2 44 31 89l116 180h85l-84 -132q6 1 19 1zM168 510q33 0 53.5 20t20.5 51q0 30 -20.5 50t-54.5 20q-32 0 -52.5 -20t-20.5 -50q0 -31 20.5 -51t53.5 -20z" />
    <glyph glyph-name="seven.sups" unicode="&#x2077;" horiz-adv-x="308" 
d="M8 850h290v-57l-148 -343h-86l142 329h-198v71z" />
    <glyph glyph-name="eight.sups" unicode="&#x2078;" horiz-adv-x="354" 
d="M264 668q70 -31 70 -104q0 -53 -43 -88.5t-113 -35.5q-71 0 -114.5 35t-43.5 89q0 73 70 104q-52 29 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM178 793q-30 0 -47.5 -14.5t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5
t-17 36.5t-46 14.5zM178 509q34 0 54 17.5t20 44.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -27 20.5 -44.5t55.5 -17.5z" />
    <glyph glyph-name="nine.sups" unicode="&#x2079;" horiz-adv-x="365" 
d="M183 860q67 0 110 -40t42 -101q0 -41 -30 -89l-117 -180h-85l85 133q-6 -1 -20 -1q-60 2 -98.5 41t-38.5 97q0 60 43 100t109 40zM183 650q33 0 53.5 20t20.5 50t-20.5 50t-53.5 20t-53.5 -20t-20.5 -50t20.5 -50t53.5 -20z" />
    <glyph glyph-name="zero.sinf" unicode="&#x2080;" horiz-adv-x="394" 
d="M198 -160q-80 0 -126.5 57.5t-46.5 153.5q0 95 46.5 152t126.5 57q77 0 124 -57.5t47 -151.5q0 -96 -45.5 -153.5t-125.5 -57.5zM198 -83q43 0 65 36t22 98q0 61 -22.5 96.5t-64.5 35.5q-44 0 -66 -35.5t-22 -96.5q0 -63 22 -98.5t66 -35.5z" />
    <glyph glyph-name="one.sinf" unicode="&#x2081;" horiz-adv-x="258" 
d="M133 250h70v-400h-82v308l-96 -45l-20 70z" />
    <glyph glyph-name="two.sinf" unicode="&#x2082;" horiz-adv-x="345" 
d="M145 -79h175v-71h-299v53l159 150q50 48 50 79q0 25 -16.5 40.5t-45.5 15.5q-58 0 -73 -71l-74 21q6 50 46.5 86t99.5 36q66 0 106 -33t40 -92q0 -34 -16 -61t-51 -58z" />
    <glyph glyph-name="three.sinf" unicode="&#x2083;" horiz-adv-x="355" 
d="M227 97q47 -8 75 -40.5t28 -80.5q0 -58 -42 -97t-106 -39q-71 0 -111.5 36t-50.5 88l73 21q5 -33 28.5 -54t58.5 -21q33 0 52.5 19t19.5 47t-19.5 47.5t-56.5 19.5q-15 0 -30 -3l-13 46l84 95h-183v69h282v-57z" />
    <glyph glyph-name="four.sinf" unicode="&#x2084;" horiz-adv-x="349" 
d="M334 -1v-70h-49v-79h-78v79h-197v62l139 259h86l-133 -251h105v79h78v-79h49z" />
    <glyph glyph-name="five.sinf" unicode="&#x2085;" horiz-adv-x="346" 
d="M188 105q60 0 96.5 -36.5t36.5 -95.5q0 -57 -40.5 -95t-103.5 -38q-64 0 -104.5 32t-52.5 78l72 20q21 -60 82 -60q32 0 51 18t19 46q0 29 -19 48t-52 19q-45 0 -79 -24l-58 19l18 214h248v-70h-181l-7 -89q31 14 74 14z" />
    <glyph glyph-name="six.sinf" unicode="&#x2086;" horiz-adv-x="335" 
d="M182 119q60 -3 99 -41.5t39 -97.5q0 -60 -43 -100t-109 -40q-67 0 -110.5 40t-42.5 101q2 44 31 89l116 180h85l-84 -132q6 1 19 1zM168 -90q33 0 53.5 20t20.5 50q0 31 -20.5 51t-54.5 20q-33 0 -53 -20.5t-20 -50.5t20.5 -50t53.5 -20z" />
    <glyph glyph-name="seven.sinf" unicode="&#x2087;" horiz-adv-x="308" 
d="M8 250h290v-57l-148 -343h-86l142 329h-198v71z" />
    <glyph glyph-name="eight.sinf" unicode="&#x2088;" horiz-adv-x="354" 
d="M264 68q70 -31 70 -105q0 -53 -43 -88t-113 -35q-71 0 -114.5 35t-43.5 88q0 72 70 105q-52 28 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM178 193q-30 0 -47.5 -14.5t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5t-17 36.5
t-46 14.5zM178 -92q34 0 54 17.5t20 45.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -28 20.5 -45.5t55.5 -17.5z" />
    <glyph glyph-name="nine.sinf" unicode="&#x2089;" horiz-adv-x="335" 
d="M167 260q67 0 110.5 -40t42.5 -101q-2 -44 -31 -89l-116 -180h-85l84 132q-6 -1 -19 -1q-60 3 -99 42t-39 97q0 60 43 100t109 40zM168 50q32 0 52.5 20t20.5 50t-20.5 50t-53.5 20t-53.5 -20t-20.5 -50t20.5 -50t54.5 -20z" />
    <glyph glyph-name="franc" unicode="&#x20a3;" horiz-adv-x="619" 
d="M567 601h-363v-195h333v-96h-333v-115h179v-63h-179v-132h-108v132h-58v63h58v505h471v-99z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="643" 
d="M237 101h371v-101h-563v101h85v127h-69v63h69v63h-69v63h69v90q0 99 58.5 153.5t156.5 54.5q88 0 146.5 -51.5t71.5 -124.5l-97 -28q-27 106 -124 106q-50 0 -77.5 -30.5t-27.5 -85.5v-84h244v-63h-244v-63h244v-63h-244v-127z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="756" 
d="M651 147l65 -83q-95 -77 -232 -77q-129 0 -225.5 73.5t-127.5 192.5h-103v63h93q-2 22 -2 35t2 35h-93v63h103q31 118 127 191t225 73q134 0 231 -75l-65 -84q-68 58 -166 58q-85 0 -148 -45t-89 -118h290v-63h-305q-2 -22 -2 -34t2 -36h305v-63h-290q27 -74 89.5 -119
t147.5 -45q102 0 168 58z" />
    <glyph glyph-name="uni20BF" unicode="&#x20bf;" horiz-adv-x="693" 
d="M527 366q56 -22 87.5 -66t31.5 -104q0 -87 -62 -141.5t-168 -54.5h-21v-135h-80v135h-80v-135h-81v135h-129v96h81v509h-81v95h129v135h81v-135h80v135h80v-135q98 0 160 -51t63 -133q1 -103 -91 -150zM393 605h-180v-204h186q50 0 81.5 30t31.5 73q0 44 -33 73t-86 28z
M414 96q56 0 90 32t34 77t-33 77t-90 32h-202v-218h201z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1089" 
d="M895 392q-70 0 -117.5 46.5t-47.5 114.5t47.5 114t117.5 46q69 0 116.5 -46.5t47.5 -113.5q0 -68 -47.5 -114.5t-116.5 -46.5zM549 193v507h109v-700h-91l-388 507v-507h-109v700h91zM895 638q-38 0 -62.5 -24.5t-24.5 -60.5t24.5 -61t62.5 -25q36 0 60.5 25t24.5 61
q1 36 -23.5 60.5t-61.5 24.5zM747 256v77h295v-77h-295z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="719" 
d="M135 380v260h-105v60h277v-60h-106v-260h-66zM342 380v320h65l103 -149l102 149h67v-320h-64v217l-104 -146l-106 146v-217h-63z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="843" 
d="M431 -11q-168 0 -280 104.5t-112 258.5q0 149 112 254.5t271 105.5t270.5 -105.5t111.5 -254.5v-16h-615v-211q90 -106 242 -106q140 0 229 94h79q-127 -124 -308 -124zM189 366h465v210q-90 106 -232 106q-143 0 -233 -106v-210z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="796" 
d="M121 300v308l-96 -45l-20 70l128 67h70v-400h-82zM529 700h78l-435 -700h-79zM706 218q70 -31 70 -104q0 -53 -43 -88.5t-113 -35.5q-71 0 -114.5 35t-43.5 89q0 73 70 104q-52 29 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM620 343
q-30 0 -47.5 -14.5t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5t-17 36.5t-46 14.5zM620 59q34 0 54 17.5t20 44.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -27 20.5 -44.5t55.5 -17.5z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="893" 
d="M330 426q0 -58 -42 -97t-106 -39q-71 0 -111.5 36t-50.5 89l73 21q5 -34 28.5 -55t58.5 -21q33 0 52.5 19.5t19.5 47.5t-19.5 47t-56.5 19q-15 0 -30 -3l-13 46l84 95h-183v69h282v-57l-89 -96q47 -8 75 -40.5t28 -80.5zM626 700h78l-435 -700h-79zM803 218
q70 -31 70 -104q0 -53 -43 -88.5t-113 -35.5q-71 0 -114.5 35t-43.5 89q0 73 70 104q-52 29 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM717 343q-30 0 -47.5 -14.5t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5t-17 36.5t-46 14.5z
M717 59q34 0 54 17.5t20 44.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -27 20.5 -44.5t55.5 -17.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="884" 
d="M321 424q0 -58 -40.5 -96t-103.5 -38q-64 0 -105 32t-52 79l72 20q22 -61 82 -61q32 0 51 18.5t19 46.5q0 29 -19 47.5t-52 18.5q-45 0 -79 -24l-58 19l18 214h248v-70h-181l-7 -89q31 14 74 14q60 0 96.5 -36.5t36.5 -94.5zM617 700h78l-435 -700h-79zM794 218
q70 -31 70 -104q0 -53 -43 -88.5t-113 -35.5q-71 0 -114.5 35t-43.5 89q0 73 70 104q-52 29 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM708 343q-30 0 -47.5 -14.5t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5t-17 36.5t-46 14.5z
M708 59q34 0 54 17.5t20 44.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -27 20.5 -44.5t55.5 -17.5z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="846" 
d="M150 300h-86l142 329h-198v71h290v-57zM579 700h78l-435 -700h-79zM756 218q70 -31 70 -104q0 -53 -43 -88.5t-113 -35.5q-71 0 -114.5 35t-43.5 89q0 73 70 104q-52 29 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM670 343q-30 0 -47.5 -14.5
t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5t-17 36.5t-46 14.5zM670 59q34 0 54 17.5t20 44.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -27 20.5 -44.5t55.5 -17.5z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="844" 
d="M779 384v-92h-538l215 -212l-66 -66l-325 324l325 325l66 -66l-214 -212z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="779" 
d="M714 390l-66 -66l-208 210l-1 -534h-99v534l-209 -210l-66 66l325 324z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="844" 
d="M454 664l325 -324l-325 -325l-66 66l214 212l-537 1v92h538l-215 212z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="779" 
d="M648 377l66 -66l-324 -325l-325 325l66 66l209 -210v533h99l1 -533z" />
    <glyph glyph-name="arrowboth" unicode="&#x2194;" horiz-adv-x="1132" 
d="M743 664l324 -324l-324 -325l-66 66l213 212h-648l214 -212l-66 -66l-325 325l325 324l66 -66l-215 -212h650l-214 212z" />
    <glyph glyph-name="arrowupdn" unicode="&#x2195;" horiz-adv-x="779" 
d="M440 15l208 211l66 -66l-324 -325l-325 325l66 66l209 -211v632l-209 -210l-66 66l325 324l324 -324l-66 -66l-208 210l-1 -316z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="669" 
d="M604 125l-68 -68l-378 377l1 -297h-94v459h459v-94l-299 2z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="669" 
d="M145 609h459v-459h-93l1 298l-378 -378l-69 68l378 378l-297 -1z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="669" 
d="M510 536h94v-459h-459v94l299 -2l-379 379l68 68l378 -377z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="669" 
d="M604 531l-377 -378l297 1v-94h-459v459h94l-2 -299l379 379z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="640" 
d="M306 -15q-107 0 -181.5 72.5t-74.5 177.5q0 103 69.5 171t176.5 68q118 0 190 -74q-9 94 -62.5 159t-136.5 65q-96 0 -171 -70l-24 88q86 73 205 73q134 0 213.5 -103t79.5 -272q0 -162 -77.5 -258.5t-206.5 -96.5zM309 80q76 0 124.5 61t50.5 149q-26 38 -74 65.5
t-105 27.5q-65 0 -109 -43t-44 -104q0 -65 44.5 -110.5t112.5 -45.5z" />
    <glyph glyph-name="emptyset" unicode="&#x2205;" horiz-adv-x="520" 
d="M445 469q51 -62 51 -149q1 -99 -66.5 -167t-166.5 -68q-85 0 -149 53l-62 -62l-37 36l63 63q-48 64 -48 145q0 99 67 167.5t166 67.5q85 0 146 -50l60 60l36 -36zM107 320q0 -49 27 -89l218 217q-40 27 -90 27q-65 0 -110 -44.5t-45 -110.5zM262 165q66 0 111 44.5
t46 110.5q0 50 -30 93l-219 -219q42 -29 92 -29z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="646" 
d="M581 730v-940h-104v844h-307v-844h-105v940h516z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="657" 
d="M185 -105h422v-95h-557v83l296 390l-279 374v83h520v-95h-385l267 -363z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="555" 
d="M65 308v85h425v-85h-425z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="704" 
d="M598 790h106l-270 -881h-111l-152 402h-121v90h195l130 -368z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="882" 
d="M640 545q79 0 135.5 -57.5t56.5 -137.5q0 -81 -56 -138t-136 -57q-112 0 -199 119q-85 -119 -199 -119q-80 0 -136 57t-56 138q0 80 56.5 137.5t135.5 57.5q114 0 199 -121q87 121 199 121zM246 241q73 0 152 108q-78 108 -152 108q-43 0 -75 -31.5t-32 -76.5
q0 -44 31.5 -76t75.5 -32zM637 241q44 0 75.5 32t31.5 76q0 45 -32 76.5t-75 31.5q-74 0 -152 -108q79 -108 152 -108z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="563" 
d="M116 -156q-74 0 -116 38l40 85q29 -29 71 -29q68 0 82 101l77 557q26 193 177 193q74 0 116 -38l-39 -85q-29 29 -72 29q-70 0 -84 -100l-77 -557q-26 -194 -175 -194z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="608" 
d="M159 367l-94 27q0 55 31 93.5t93 38.5q49 0 117.5 -36t91.5 -36q52 0 52 68l93 -27q0 -56 -30 -95t-92 -39q-49 0 -117.5 36.5t-91.5 36.5q-53 0 -53 -67zM159 183l-94 27q0 55 31 93.5t93 38.5q49 0 117.5 -36t91.5 -36q52 0 52 68l93 -27q0 -56 -30 -95t-92 -39
q-49 0 -117.5 36.5t-91.5 36.5q-53 0 -53 -67z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="556" 
d="M491 297v-85h-235l-60 -137h-76l60 137h-114v85h151l47 107h-199v85h236l60 136h76l-60 -136h113v-85h-150l-47 -107h198z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="560" 
d="M490 641v-93l-323 -121l323 -122v-93l-425 166v97zM65 79v85h425v-85h-425z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="560" 
d="M70 641l425 -166v-97l-425 -166v93l323 122l-323 121v93zM70 79v85h425v-85h-425z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="603" 
d="M359 700l194 -350l-194 -350h-113l-196 350l196 350h113zM302 96l141 254l-141 254l-141 -254z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="245" 
d="M122 572q-27 0 -46 18.5t-19 45.5t19 45.5t46 18.5q28 0 46.5 -18.5t18.5 -45.5t-18.5 -45.5t-46.5 -18.5zM70 0v503h105v-503h-105z" />
    <glyph glyph-name="f_f_j" horiz-adv-x="925" 
d="M867 503v-535q0 -90 -40.5 -140.5t-121.5 -50.5q-44 0 -88 20l24 84q22 -15 51 -15q70 0 70 95v449h-215v-410h-104v410h-215v-410h-105v410h-100v93h100v56q0 87 45.5 135.5t132.5 48.5q62 0 107 -21l-25 -85q-33 15 -69 15q-41 0 -63.5 -25t-22.5 -70v-54h215v56
q0 87 48 135.5t142 48.5q98 0 168 -49l-25 -85q-64 43 -133 43q-45 0 -70.5 -25t-25.5 -70v-54h320z" />
    <glyph glyph-name="f_j" horiz-adv-x="606" 
d="M547 503v-535q0 -91 -40 -141t-122 -50q-43 0 -88 20l24 84q23 -15 51 -15q71 0 71 95v449h-215v-410h-105v410h-100v93h100v56q0 87 48.5 135.5t142.5 48.5q97 0 168 -49l-26 -85q-64 43 -132 43q-46 0 -71 -25t-25 -70v-54h319z" />
    <glyph glyph-name="f_t" horiz-adv-x="739" 
d="M680 102l26 -86q-57 -24 -111 -24q-79 0 -118 45.5t-39 125.5v247h-210v-410h-105v410h-100v93h100v56q0 87 44 135.5t129 48.5q50 0 88 -13l-26 -86q-24 8 -49 8q-81 0 -81 -95v-54h210v168h104v-168h160v-93h-160v-247q0 -77 72 -77q33 0 66 16z" />
    <glyph glyph-name="t_t" horiz-adv-x="738" 
d="M679 102l26 -86q-57 -24 -111 -24q-79 0 -118 45.5t-39 125.5v247h-223v-247q0 -77 72 -77q33 0 66 16l26 -86q-57 -24 -111 -24q-79 0 -118 45.5t-39 125.5v247h-92v93h92v168h104v-168h223v168h104v-168h160v-93h-160v-247q0 -77 72 -77q33 0 66 16z" />
    <glyph glyph-name="zero.tf" horiz-adv-x="593" 
d="M299 -13q-133 0 -207 101t-74 262q0 162 74 262.5t207 100.5t207 -100.5t74 -262.5q0 -161 -74 -262t-207 -101zM299 86q85 0 129 70.5t44 193.5t-44 193.5t-129 70.5q-84 0 -128.5 -71t-44.5 -193t44.5 -193t128.5 -71z" />
    <glyph glyph-name="one.tf" horiz-adv-x="593" 
d="M373 99h155v-99h-450v99h186v479l-167 -80l-26 94l210 108h92v-601z" />
    <glyph glyph-name="two.tf" horiz-adv-x="593" 
d="M220 99h329v-99h-492v77l276 263q50 47 73.5 81t23.5 72q0 57 -35 90t-96 33q-55 0 -93.5 -34.5t-51.5 -92.5l-97 28q11 80 77 138t163 58q108 0 174.5 -57t66.5 -158q0 -62 -32 -112t-99 -111z" />
    <glyph glyph-name="three.tf" horiz-adv-x="593" 
d="M357 430q90 -7 144.5 -64.5t54.5 -147.5q0 -98 -71.5 -165.5t-177.5 -67.5q-115 0 -183.5 60.5t-85.5 145.5l98 27q11 -58 56 -97.5t110 -39.5q68 0 108.5 40t40.5 97t-36.5 94.5t-98.5 37.5q-47 0 -82 -23l-19 68l173 208h-329v97h470v-76z" />
    <glyph glyph-name="four.tf" horiz-adv-x="593" 
d="M572 242v-96h-91v-146h-109v146h-350v80l251 474h117l-241 -458h223v160h109v-160h91z" />
    <glyph glyph-name="five.tf" horiz-adv-x="593" 
d="M323 451q103 0 169.5 -64.5t66.5 -164.5t-70.5 -167.5t-178.5 -67.5q-106 0 -172.5 54.5t-88.5 134.5l98 27q17 -53 58 -86.5t100 -33.5q68 0 108.5 40t40.5 100q0 62 -39.5 101t-110.5 39q-80 0 -144 -49l-84 25l31 361h418v-97h-323l-14 -184q58 32 135 32z" />
    <glyph glyph-name="six.tf" horiz-adv-x="593" 
d="M316 469q109 -4 177 -71t68 -168q0 -103 -73 -173t-185 -70q-113 0 -186 69.5t-73 173.5q0 73 55 156l206 314h123l-155 -235q17 4 43 4zM303 85q67 0 108.5 41t41.5 104q0 62 -42 103t-109 41q-66 0 -108 -41.5t-42 -102.5q0 -62 42 -103.5t109 -41.5z" />
    <glyph glyph-name="seven.tf" horiz-adv-x="593" 
d="M55 700h492v-77l-276 -623h-115l268 602h-369v98z" />
    <glyph glyph-name="eight.tf" horiz-adv-x="593" 
d="M442 380q60 -24 93 -70.5t33 -109.5q0 -92 -73 -152.5t-190 -60.5q-119 0 -191.5 60.5t-72.5 152.5q0 63 33 109.5t93 70.5q-96 48 -96 148q0 79 64 132t170 53q105 0 169 -53t64 -132q0 -100 -96 -148zM305 625q-60 0 -94.5 -29.5t-34.5 -74.5t34.5 -75t94.5 -30
q58 0 93 30t35 75t-34.5 74.5t-93.5 29.5zM305 80q70 0 112 36t42 91t-42 90.5t-112 35.5q-71 0 -113.5 -35.5t-42.5 -90.5t42.5 -91t113.5 -36z" />
    <glyph glyph-name="nine.tf" horiz-adv-x="593" 
d="M305 713q113 0 186 -69.5t73 -172.5q0 -72 -55 -157l-206 -314h-123l156 236q-23 -5 -44 -5q-109 4 -177 71.5t-68 168.5q0 103 73 172.5t185 69.5zM306 326q66 0 108 41.5t42 103.5t-42 103.5t-109 41.5t-108.5 -41t-41.5 -104q0 -62 42 -103.5t109 -41.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="394" 
d="M198 -10q-80 0 -126.5 57.5t-46.5 153.5q0 95 46.5 152t126.5 57q77 0 124 -57.5t47 -151.5q0 -96 -46 -153.5t-125 -57.5zM198 67q43 0 65 36t22 98q0 61 -22.5 96.5t-64.5 35.5q-44 0 -66 -35.5t-22 -96.5q0 -63 22 -98.5t66 -35.5z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="258" 
d="M133 400h70v-400h-82v308l-96 -45l-20 70z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="345" 
d="M145 72h175v-72h-299v53l159 150q50 48 50 79q0 25 -16.5 40.5t-45.5 15.5q-58 0 -73 -71l-74 21q6 50 46.5 86t99.5 36q66 0 106 -33t40 -92q0 -34 -16 -61t-51 -58z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="355" 
d="M227 247q47 -8 75 -40.5t28 -80.5q0 -58 -42 -97t-106 -39q-71 0 -111.5 36t-50.5 89l73 21q5 -34 28.5 -55t58.5 -21q33 0 52.5 19.5t19.5 47.5t-19.5 47t-56.5 19q-15 0 -30 -3l-13 46l84 95h-183v69h282v-57z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="349" 
d="M334 150v-71h-49v-79h-78v79h-197v62l139 259h86l-132 -250h104v78h78v-78h49z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="346" 
d="M188 255q60 0 96.5 -36.5t36.5 -94.5t-40.5 -96t-103.5 -38q-64 0 -105 32t-52 79l72 20q22 -61 82 -61q32 0 51 18.5t19 46.5q0 29 -19 47.5t-52 18.5q-45 0 -79 -24l-58 19l18 214h248v-70h-181l-7 -89q31 14 74 14z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="335" 
d="M182 269q60 -3 99 -41.5t39 -96.5q0 -60 -43 -100.5t-109 -40.5q-67 0 -110.5 40.5t-42.5 100.5q2 44 31 89l116 180h85l-84 -132q6 1 19 1zM168 60q33 0 53.5 20t20.5 51q0 30 -20.5 50t-54.5 20q-32 0 -52.5 -20t-20.5 -50q0 -31 20.5 -51t53.5 -20z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="308" 
d="M8 400h290v-57l-148 -343h-86l142 329h-198v71z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="354" 
d="M264 218q70 -31 70 -104q0 -53 -43 -88.5t-113 -35.5q-71 0 -114.5 35t-43.5 89q0 73 70 104q-52 29 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM178 343q-30 0 -47.5 -14.5t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5
t-17 36.5t-46 14.5zM178 59q34 0 54 17.5t20 44.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -27 20.5 -44.5t55.5 -17.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="365" 
d="M183 410q67 0 110 -40t42 -101q0 -41 -30 -89l-117 -180h-85l85 133q-6 -1 -20 -1q-60 2 -98.5 41t-38.5 97q0 60 43 100t109 40zM183 200q33 0 53.5 20t20.5 50t-20.5 50t-53.5 20t-53.5 -20t-20.5 -50t20.5 -50t53.5 -20z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="394" 
d="M198 290q-80 0 -126.5 57.5t-46.5 153.5q0 95 46.5 152t126.5 57q77 0 124 -57.5t47 -151.5q0 -96 -46 -153.5t-125 -57.5zM198 367q43 0 65 36t22 98q0 61 -22.5 96.5t-64.5 35.5q-44 0 -66 -35.5t-22 -96.5q0 -63 22 -98.5t66 -35.5z" />
    <glyph glyph-name="one.numr" horiz-adv-x="258" 
d="M133 700h70v-400h-82v308l-96 -45l-20 70z" />
    <glyph glyph-name="two.numr" horiz-adv-x="345" 
d="M145 372h175v-72h-299v53l159 150q50 48 50 79q0 25 -16.5 40.5t-45.5 15.5q-58 0 -73 -71l-74 21q6 50 46.5 86t99.5 36q66 0 106 -33t40 -92q0 -34 -16 -61t-51 -58z" />
    <glyph glyph-name="three.numr" horiz-adv-x="355" 
d="M227 547q47 -8 75 -40.5t28 -80.5q0 -58 -42 -97t-106 -39q-71 0 -111.5 36t-50.5 89l73 21q5 -34 28.5 -55t58.5 -21q33 0 52.5 19.5t19.5 47.5t-19.5 47t-56.5 19q-15 0 -30 -3l-13 46l84 95h-183v69h282v-57z" />
    <glyph glyph-name="four.numr" horiz-adv-x="349" 
d="M334 450v-71h-49v-79h-78v79h-197v62l139 259h86l-132 -250h104v78h78v-78h49z" />
    <glyph glyph-name="five.numr" horiz-adv-x="346" 
d="M188 555q60 0 96.5 -36.5t36.5 -94.5t-40.5 -96t-103.5 -38q-64 0 -105 32t-52 79l72 20q22 -61 82 -61q32 0 51 18.5t19 46.5q0 29 -19 47.5t-52 18.5q-45 0 -79 -24l-58 19l18 214h248v-70h-181l-7 -89q31 14 74 14z" />
    <glyph glyph-name="six.numr" horiz-adv-x="335" 
d="M182 569q60 -3 99 -41.5t39 -96.5q0 -60 -43 -100.5t-109 -40.5q-67 0 -110.5 40.5t-42.5 100.5q2 44 31 89l116 180h85l-84 -132q6 1 19 1zM168 360q33 0 53.5 20t20.5 51q0 30 -20.5 50t-54.5 20q-32 0 -52.5 -20t-20.5 -50q0 -31 20.5 -51t53.5 -20z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="308" 
d="M8 700h290v-57l-148 -343h-86l142 329h-198v71z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="354" 
d="M264 518q70 -31 70 -104q0 -53 -43 -88.5t-113 -35.5q-71 0 -114.5 35t-43.5 89q0 73 70 104q-52 29 -52 84q0 46 38.5 77t101.5 31q62 0 100 -31.5t38 -76.5q0 -55 -52 -84zM178 643q-30 0 -47.5 -14.5t-17.5 -36.5t17.5 -36.5t47.5 -14.5q29 0 46 14.5t17 36.5
t-17 36.5t-46 14.5zM178 359q34 0 54 17.5t20 44.5q0 26 -20 44t-54 18q-35 0 -55.5 -18t-20.5 -44q0 -27 20.5 -44.5t55.5 -17.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="365" 
d="M183 710q67 0 110 -40t42 -101q0 -41 -30 -89l-117 -180h-85l85 133q-6 -1 -20 -1q-60 2 -98.5 41t-38.5 97q0 60 43 100t109 40zM183 500q33 0 53.5 20t20.5 50t-20.5 50t-53.5 20t-53.5 -20t-20.5 -50t20.5 -50t53.5 -20z" />
    <hkern u1="A" u2="f" k="20" />
    <hkern u1="E" u2="&#x153;" k="15" />
    <hkern u1="E" u2="&#x151;" k="15" />
    <hkern u1="E" u2="&#x14d;" k="15" />
    <hkern u1="E" u2="&#x123;" k="15" />
    <hkern u1="E" u2="&#x121;" k="15" />
    <hkern u1="E" u2="&#x11f;" k="15" />
    <hkern u1="E" u2="&#x11b;" k="15" />
    <hkern u1="E" u2="&#x119;" k="15" />
    <hkern u1="E" u2="&#x117;" k="15" />
    <hkern u1="E" u2="&#x113;" k="15" />
    <hkern u1="E" u2="&#x111;" k="15" />
    <hkern u1="E" u2="&#x10f;" k="15" />
    <hkern u1="E" u2="&#x10d;" k="15" />
    <hkern u1="E" u2="&#x10b;" k="15" />
    <hkern u1="E" u2="&#x107;" k="15" />
    <hkern u1="E" u2="&#x105;" k="15" />
    <hkern u1="E" u2="&#x103;" k="15" />
    <hkern u1="E" u2="&#x101;" k="15" />
    <hkern u1="E" u2="&#xf8;" k="15" />
    <hkern u1="E" u2="&#xf6;" k="15" />
    <hkern u1="E" u2="&#xf5;" k="15" />
    <hkern u1="E" u2="&#xf4;" k="15" />
    <hkern u1="E" u2="&#xf3;" k="15" />
    <hkern u1="E" u2="&#xf2;" k="15" />
    <hkern u1="E" u2="&#xeb;" k="15" />
    <hkern u1="E" u2="&#xea;" k="15" />
    <hkern u1="E" u2="&#xe9;" k="15" />
    <hkern u1="E" u2="&#xe8;" k="15" />
    <hkern u1="E" u2="&#xe7;" k="15" />
    <hkern u1="E" u2="&#xe6;" k="15" />
    <hkern u1="E" u2="&#xe5;" k="15" />
    <hkern u1="E" u2="&#xe4;" k="15" />
    <hkern u1="E" u2="&#xe3;" k="15" />
    <hkern u1="E" u2="&#xe2;" k="15" />
    <hkern u1="E" u2="&#xe1;" k="15" />
    <hkern u1="E" u2="&#xe0;" k="15" />
    <hkern u1="E" u2="q" k="15" />
    <hkern u1="E" u2="o" k="15" />
    <hkern u1="E" u2="g" k="15" />
    <hkern u1="E" u2="e" k="15" />
    <hkern u1="E" u2="d" k="15" />
    <hkern u1="E" u2="c" k="15" />
    <hkern u1="E" u2="a" k="15" />
    <hkern u1="F" u2="&#x153;" k="25" />
    <hkern u1="F" u2="&#x151;" k="25" />
    <hkern u1="F" u2="&#x14d;" k="25" />
    <hkern u1="F" u2="&#x123;" k="25" />
    <hkern u1="F" u2="&#x121;" k="25" />
    <hkern u1="F" u2="&#x11f;" k="25" />
    <hkern u1="F" u2="&#x11b;" k="25" />
    <hkern u1="F" u2="&#x119;" k="25" />
    <hkern u1="F" u2="&#x117;" k="25" />
    <hkern u1="F" u2="&#x113;" k="25" />
    <hkern u1="F" u2="&#x111;" k="25" />
    <hkern u1="F" u2="&#x10f;" k="25" />
    <hkern u1="F" u2="&#x10d;" k="25" />
    <hkern u1="F" u2="&#x10b;" k="25" />
    <hkern u1="F" u2="&#x107;" k="25" />
    <hkern u1="F" u2="&#x105;" k="25" />
    <hkern u1="F" u2="&#x103;" k="25" />
    <hkern u1="F" u2="&#x101;" k="25" />
    <hkern u1="F" u2="&#xf8;" k="25" />
    <hkern u1="F" u2="&#xf6;" k="25" />
    <hkern u1="F" u2="&#xf5;" k="25" />
    <hkern u1="F" u2="&#xf4;" k="25" />
    <hkern u1="F" u2="&#xf3;" k="25" />
    <hkern u1="F" u2="&#xf2;" k="25" />
    <hkern u1="F" u2="&#xeb;" k="25" />
    <hkern u1="F" u2="&#xea;" k="25" />
    <hkern u1="F" u2="&#xe9;" k="25" />
    <hkern u1="F" u2="&#xe8;" k="25" />
    <hkern u1="F" u2="&#xe7;" k="25" />
    <hkern u1="F" u2="&#xe6;" k="25" />
    <hkern u1="F" u2="&#xe5;" k="25" />
    <hkern u1="F" u2="&#xe4;" k="25" />
    <hkern u1="F" u2="&#xe3;" k="25" />
    <hkern u1="F" u2="&#xe2;" k="25" />
    <hkern u1="F" u2="&#xe1;" k="25" />
    <hkern u1="F" u2="&#xe0;" k="25" />
    <hkern u1="F" u2="q" k="25" />
    <hkern u1="F" u2="o" k="25" />
    <hkern u1="F" u2="g" k="25" />
    <hkern u1="F" u2="e" k="25" />
    <hkern u1="F" u2="d" k="25" />
    <hkern u1="F" u2="c" k="25" />
    <hkern u1="F" u2="a" k="25" />
    <hkern u1="K" u2="&#x153;" k="50" />
    <hkern u1="K" u2="&#x151;" k="50" />
    <hkern u1="K" u2="&#x14d;" k="50" />
    <hkern u1="K" u2="&#x123;" k="50" />
    <hkern u1="K" u2="&#x121;" k="50" />
    <hkern u1="K" u2="&#x11f;" k="50" />
    <hkern u1="K" u2="&#x11b;" k="50" />
    <hkern u1="K" u2="&#x119;" k="50" />
    <hkern u1="K" u2="&#x117;" k="50" />
    <hkern u1="K" u2="&#x113;" k="50" />
    <hkern u1="K" u2="&#x111;" k="50" />
    <hkern u1="K" u2="&#x10f;" k="50" />
    <hkern u1="K" u2="&#x10d;" k="50" />
    <hkern u1="K" u2="&#x10b;" k="50" />
    <hkern u1="K" u2="&#x107;" k="50" />
    <hkern u1="K" u2="&#x105;" k="50" />
    <hkern u1="K" u2="&#x103;" k="50" />
    <hkern u1="K" u2="&#x101;" k="50" />
    <hkern u1="K" u2="&#xf8;" k="50" />
    <hkern u1="K" u2="&#xf6;" k="50" />
    <hkern u1="K" u2="&#xf5;" k="50" />
    <hkern u1="K" u2="&#xf4;" k="50" />
    <hkern u1="K" u2="&#xf3;" k="50" />
    <hkern u1="K" u2="&#xf2;" k="50" />
    <hkern u1="K" u2="&#xeb;" k="50" />
    <hkern u1="K" u2="&#xea;" k="50" />
    <hkern u1="K" u2="&#xe9;" k="50" />
    <hkern u1="K" u2="&#xe8;" k="50" />
    <hkern u1="K" u2="&#xe7;" k="50" />
    <hkern u1="K" u2="&#xe6;" k="50" />
    <hkern u1="K" u2="&#xe5;" k="50" />
    <hkern u1="K" u2="&#xe4;" k="50" />
    <hkern u1="K" u2="&#xe3;" k="50" />
    <hkern u1="K" u2="&#xe2;" k="50" />
    <hkern u1="K" u2="&#xe1;" k="50" />
    <hkern u1="K" u2="&#xe0;" k="50" />
    <hkern u1="K" u2="q" k="50" />
    <hkern u1="K" u2="o" k="50" />
    <hkern u1="K" u2="g" k="50" />
    <hkern u1="K" u2="e" k="50" />
    <hkern u1="K" u2="d" k="50" />
    <hkern u1="K" u2="c" k="50" />
    <hkern u1="K" u2="a" k="50" />
    <hkern u1="L" u2="&#x153;" k="17" />
    <hkern u1="L" u2="&#x151;" k="17" />
    <hkern u1="L" u2="&#x14d;" k="17" />
    <hkern u1="L" u2="&#x123;" k="17" />
    <hkern u1="L" u2="&#x121;" k="17" />
    <hkern u1="L" u2="&#x11f;" k="17" />
    <hkern u1="L" u2="&#x11b;" k="17" />
    <hkern u1="L" u2="&#x119;" k="17" />
    <hkern u1="L" u2="&#x117;" k="17" />
    <hkern u1="L" u2="&#x113;" k="17" />
    <hkern u1="L" u2="&#x111;" k="17" />
    <hkern u1="L" u2="&#x10f;" k="17" />
    <hkern u1="L" u2="&#x10d;" k="17" />
    <hkern u1="L" u2="&#x10b;" k="17" />
    <hkern u1="L" u2="&#x107;" k="17" />
    <hkern u1="L" u2="&#x105;" k="17" />
    <hkern u1="L" u2="&#x103;" k="17" />
    <hkern u1="L" u2="&#x101;" k="17" />
    <hkern u1="L" u2="&#xf8;" k="17" />
    <hkern u1="L" u2="&#xf6;" k="17" />
    <hkern u1="L" u2="&#xf5;" k="17" />
    <hkern u1="L" u2="&#xf4;" k="17" />
    <hkern u1="L" u2="&#xf3;" k="17" />
    <hkern u1="L" u2="&#xf2;" k="17" />
    <hkern u1="L" u2="&#xeb;" k="17" />
    <hkern u1="L" u2="&#xea;" k="17" />
    <hkern u1="L" u2="&#xe9;" k="17" />
    <hkern u1="L" u2="&#xe8;" k="17" />
    <hkern u1="L" u2="&#xe7;" k="17" />
    <hkern u1="L" u2="&#xe6;" k="17" />
    <hkern u1="L" u2="&#xe5;" k="17" />
    <hkern u1="L" u2="&#xe4;" k="17" />
    <hkern u1="L" u2="&#xe3;" k="17" />
    <hkern u1="L" u2="&#xe2;" k="17" />
    <hkern u1="L" u2="&#xe1;" k="17" />
    <hkern u1="L" u2="&#xe0;" k="17" />
    <hkern u1="L" u2="q" k="17" />
    <hkern u1="L" u2="o" k="17" />
    <hkern u1="L" u2="g" k="17" />
    <hkern u1="L" u2="e" k="17" />
    <hkern u1="L" u2="d" k="17" />
    <hkern u1="L" u2="c" k="17" />
    <hkern u1="L" u2="a" k="17" />
    <hkern u1="P" u2="&#x153;" k="23" />
    <hkern u1="P" u2="&#x151;" k="23" />
    <hkern u1="P" u2="&#x14d;" k="23" />
    <hkern u1="P" u2="&#x123;" k="23" />
    <hkern u1="P" u2="&#x121;" k="23" />
    <hkern u1="P" u2="&#x11f;" k="23" />
    <hkern u1="P" u2="&#x11b;" k="23" />
    <hkern u1="P" u2="&#x119;" k="23" />
    <hkern u1="P" u2="&#x117;" k="23" />
    <hkern u1="P" u2="&#x113;" k="23" />
    <hkern u1="P" u2="&#x111;" k="23" />
    <hkern u1="P" u2="&#x10f;" k="23" />
    <hkern u1="P" u2="&#x10d;" k="23" />
    <hkern u1="P" u2="&#x10b;" k="23" />
    <hkern u1="P" u2="&#x107;" k="23" />
    <hkern u1="P" u2="&#x105;" k="23" />
    <hkern u1="P" u2="&#x103;" k="23" />
    <hkern u1="P" u2="&#x101;" k="23" />
    <hkern u1="P" u2="&#xf8;" k="23" />
    <hkern u1="P" u2="&#xf6;" k="23" />
    <hkern u1="P" u2="&#xf5;" k="23" />
    <hkern u1="P" u2="&#xf4;" k="23" />
    <hkern u1="P" u2="&#xf3;" k="23" />
    <hkern u1="P" u2="&#xf2;" k="23" />
    <hkern u1="P" u2="&#xeb;" k="23" />
    <hkern u1="P" u2="&#xea;" k="23" />
    <hkern u1="P" u2="&#xe9;" k="23" />
    <hkern u1="P" u2="&#xe8;" k="23" />
    <hkern u1="P" u2="&#xe7;" k="23" />
    <hkern u1="P" u2="&#xe6;" k="23" />
    <hkern u1="P" u2="&#xe5;" k="23" />
    <hkern u1="P" u2="&#xe4;" k="23" />
    <hkern u1="P" u2="&#xe3;" k="23" />
    <hkern u1="P" u2="&#xe2;" k="23" />
    <hkern u1="P" u2="&#xe1;" k="23" />
    <hkern u1="P" u2="&#xe0;" k="23" />
    <hkern u1="P" u2="q" k="23" />
    <hkern u1="P" u2="o" k="23" />
    <hkern u1="P" u2="g" k="23" />
    <hkern u1="P" u2="e" k="23" />
    <hkern u1="P" u2="d" k="23" />
    <hkern u1="P" u2="c" k="23" />
    <hkern u1="P" u2="a" k="23" />
    <hkern u1="Q" u2="&#x104;" k="15" />
    <hkern u1="Q" u2="&#x102;" k="15" />
    <hkern u1="Q" u2="&#x100;" k="15" />
    <hkern u1="Q" u2="&#xc6;" k="15" />
    <hkern u1="Q" u2="&#xc5;" k="15" />
    <hkern u1="Q" u2="&#xc4;" k="15" />
    <hkern u1="Q" u2="&#xc3;" k="15" />
    <hkern u1="Q" u2="&#xc2;" k="15" />
    <hkern u1="Q" u2="&#xc1;" k="15" />
    <hkern u1="Q" u2="&#xc0;" k="15" />
    <hkern u1="Q" u2="A" k="15" />
    <hkern u1="R" u2="&#x153;" k="20" />
    <hkern u1="R" u2="&#x151;" k="20" />
    <hkern u1="R" u2="&#x14d;" k="20" />
    <hkern u1="R" u2="&#x123;" k="20" />
    <hkern u1="R" u2="&#x121;" k="20" />
    <hkern u1="R" u2="&#x11f;" k="20" />
    <hkern u1="R" u2="&#x11b;" k="20" />
    <hkern u1="R" u2="&#x119;" k="20" />
    <hkern u1="R" u2="&#x117;" k="20" />
    <hkern u1="R" u2="&#x113;" k="20" />
    <hkern u1="R" u2="&#x111;" k="20" />
    <hkern u1="R" u2="&#x10f;" k="20" />
    <hkern u1="R" u2="&#x10d;" k="20" />
    <hkern u1="R" u2="&#x10b;" k="20" />
    <hkern u1="R" u2="&#x107;" k="20" />
    <hkern u1="R" u2="&#x105;" k="20" />
    <hkern u1="R" u2="&#x103;" k="20" />
    <hkern u1="R" u2="&#x101;" k="20" />
    <hkern u1="R" u2="&#xf8;" k="20" />
    <hkern u1="R" u2="&#xf6;" k="20" />
    <hkern u1="R" u2="&#xf5;" k="20" />
    <hkern u1="R" u2="&#xf4;" k="20" />
    <hkern u1="R" u2="&#xf3;" k="20" />
    <hkern u1="R" u2="&#xf2;" k="20" />
    <hkern u1="R" u2="&#xeb;" k="20" />
    <hkern u1="R" u2="&#xea;" k="20" />
    <hkern u1="R" u2="&#xe9;" k="20" />
    <hkern u1="R" u2="&#xe8;" k="20" />
    <hkern u1="R" u2="&#xe7;" k="20" />
    <hkern u1="R" u2="&#xe6;" k="20" />
    <hkern u1="R" u2="&#xe5;" k="20" />
    <hkern u1="R" u2="&#xe4;" k="20" />
    <hkern u1="R" u2="&#xe3;" k="20" />
    <hkern u1="R" u2="&#xe2;" k="20" />
    <hkern u1="R" u2="&#xe1;" k="20" />
    <hkern u1="R" u2="&#xe0;" k="20" />
    <hkern u1="R" u2="q" k="20" />
    <hkern u1="R" u2="o" k="20" />
    <hkern u1="R" u2="g" k="20" />
    <hkern u1="R" u2="e" k="20" />
    <hkern u1="R" u2="d" k="20" />
    <hkern u1="R" u2="c" k="20" />
    <hkern u1="R" u2="a" k="20" />
    <hkern u1="T" u2="&#x153;" k="105" />
    <hkern u1="T" u2="&#x151;" k="105" />
    <hkern u1="T" u2="&#x14d;" k="105" />
    <hkern u1="T" u2="&#x123;" k="105" />
    <hkern u1="T" u2="&#x121;" k="105" />
    <hkern u1="T" u2="&#x11f;" k="105" />
    <hkern u1="T" u2="&#x11b;" k="105" />
    <hkern u1="T" u2="&#x119;" k="105" />
    <hkern u1="T" u2="&#x117;" k="105" />
    <hkern u1="T" u2="&#x113;" k="105" />
    <hkern u1="T" u2="&#x111;" k="105" />
    <hkern u1="T" u2="&#x10f;" k="105" />
    <hkern u1="T" u2="&#x10d;" k="105" />
    <hkern u1="T" u2="&#x10b;" k="105" />
    <hkern u1="T" u2="&#x107;" k="105" />
    <hkern u1="T" u2="&#x105;" k="105" />
    <hkern u1="T" u2="&#x103;" k="105" />
    <hkern u1="T" u2="&#x101;" k="105" />
    <hkern u1="T" u2="&#xf8;" k="105" />
    <hkern u1="T" u2="&#xf6;" k="105" />
    <hkern u1="T" u2="&#xf5;" k="105" />
    <hkern u1="T" u2="&#xf4;" k="105" />
    <hkern u1="T" u2="&#xf3;" k="105" />
    <hkern u1="T" u2="&#xf2;" k="105" />
    <hkern u1="T" u2="&#xeb;" k="105" />
    <hkern u1="T" u2="&#xea;" k="105" />
    <hkern u1="T" u2="&#xe9;" k="105" />
    <hkern u1="T" u2="&#xe8;" k="105" />
    <hkern u1="T" u2="&#xe7;" k="105" />
    <hkern u1="T" u2="&#xe6;" k="105" />
    <hkern u1="T" u2="&#xe5;" k="105" />
    <hkern u1="T" u2="&#xe4;" k="105" />
    <hkern u1="T" u2="&#xe3;" k="105" />
    <hkern u1="T" u2="&#xe2;" k="105" />
    <hkern u1="T" u2="&#xe1;" k="105" />
    <hkern u1="T" u2="&#xe0;" k="105" />
    <hkern u1="T" u2="q" k="105" />
    <hkern u1="T" u2="o" k="105" />
    <hkern u1="T" u2="g" k="105" />
    <hkern u1="T" u2="e" k="105" />
    <hkern u1="T" u2="d" k="105" />
    <hkern u1="T" u2="c" k="105" />
    <hkern u1="T" u2="a" k="105" />
    <hkern u1="W" u2="&#x153;" k="53" />
    <hkern u1="W" u2="&#x151;" k="53" />
    <hkern u1="W" u2="&#x14d;" k="53" />
    <hkern u1="W" u2="&#x123;" k="53" />
    <hkern u1="W" u2="&#x121;" k="53" />
    <hkern u1="W" u2="&#x11f;" k="53" />
    <hkern u1="W" u2="&#x11b;" k="53" />
    <hkern u1="W" u2="&#x119;" k="53" />
    <hkern u1="W" u2="&#x117;" k="53" />
    <hkern u1="W" u2="&#x113;" k="53" />
    <hkern u1="W" u2="&#x111;" k="53" />
    <hkern u1="W" u2="&#x10f;" k="53" />
    <hkern u1="W" u2="&#x10d;" k="53" />
    <hkern u1="W" u2="&#x10b;" k="53" />
    <hkern u1="W" u2="&#x107;" k="53" />
    <hkern u1="W" u2="&#x105;" k="53" />
    <hkern u1="W" u2="&#x103;" k="53" />
    <hkern u1="W" u2="&#x101;" k="53" />
    <hkern u1="W" u2="&#xf8;" k="53" />
    <hkern u1="W" u2="&#xf6;" k="53" />
    <hkern u1="W" u2="&#xf5;" k="53" />
    <hkern u1="W" u2="&#xf4;" k="53" />
    <hkern u1="W" u2="&#xf3;" k="53" />
    <hkern u1="W" u2="&#xf2;" k="53" />
    <hkern u1="W" u2="&#xeb;" k="53" />
    <hkern u1="W" u2="&#xea;" k="53" />
    <hkern u1="W" u2="&#xe9;" k="53" />
    <hkern u1="W" u2="&#xe8;" k="53" />
    <hkern u1="W" u2="&#xe7;" k="53" />
    <hkern u1="W" u2="&#xe6;" k="53" />
    <hkern u1="W" u2="&#xe5;" k="53" />
    <hkern u1="W" u2="&#xe4;" k="53" />
    <hkern u1="W" u2="&#xe3;" k="53" />
    <hkern u1="W" u2="&#xe2;" k="53" />
    <hkern u1="W" u2="&#xe1;" k="53" />
    <hkern u1="W" u2="&#xe0;" k="53" />
    <hkern u1="W" u2="q" k="53" />
    <hkern u1="W" u2="o" k="53" />
    <hkern u1="W" u2="g" k="53" />
    <hkern u1="W" u2="e" k="53" />
    <hkern u1="W" u2="d" k="53" />
    <hkern u1="W" u2="c" k="53" />
    <hkern u1="W" u2="a" k="53" />
    <hkern u1="X" u2="&#x153;" k="35" />
    <hkern u1="X" u2="&#x151;" k="35" />
    <hkern u1="X" u2="&#x14d;" k="35" />
    <hkern u1="X" u2="&#x123;" k="35" />
    <hkern u1="X" u2="&#x121;" k="35" />
    <hkern u1="X" u2="&#x11f;" k="35" />
    <hkern u1="X" u2="&#x11b;" k="35" />
    <hkern u1="X" u2="&#x119;" k="35" />
    <hkern u1="X" u2="&#x117;" k="35" />
    <hkern u1="X" u2="&#x113;" k="35" />
    <hkern u1="X" u2="&#x111;" k="35" />
    <hkern u1="X" u2="&#x10f;" k="35" />
    <hkern u1="X" u2="&#x10d;" k="35" />
    <hkern u1="X" u2="&#x10b;" k="35" />
    <hkern u1="X" u2="&#x107;" k="35" />
    <hkern u1="X" u2="&#x105;" k="35" />
    <hkern u1="X" u2="&#x103;" k="35" />
    <hkern u1="X" u2="&#x101;" k="35" />
    <hkern u1="X" u2="&#xf8;" k="35" />
    <hkern u1="X" u2="&#xf6;" k="35" />
    <hkern u1="X" u2="&#xf5;" k="35" />
    <hkern u1="X" u2="&#xf4;" k="35" />
    <hkern u1="X" u2="&#xf3;" k="35" />
    <hkern u1="X" u2="&#xf2;" k="35" />
    <hkern u1="X" u2="&#xeb;" k="35" />
    <hkern u1="X" u2="&#xea;" k="35" />
    <hkern u1="X" u2="&#xe9;" k="35" />
    <hkern u1="X" u2="&#xe8;" k="35" />
    <hkern u1="X" u2="&#xe7;" k="35" />
    <hkern u1="X" u2="&#xe6;" k="35" />
    <hkern u1="X" u2="&#xe5;" k="35" />
    <hkern u1="X" u2="&#xe4;" k="35" />
    <hkern u1="X" u2="&#xe3;" k="35" />
    <hkern u1="X" u2="&#xe2;" k="35" />
    <hkern u1="X" u2="&#xe1;" k="35" />
    <hkern u1="X" u2="&#xe0;" k="35" />
    <hkern u1="X" u2="q" k="35" />
    <hkern u1="X" u2="o" k="35" />
    <hkern u1="X" u2="g" k="35" />
    <hkern u1="X" u2="e" k="35" />
    <hkern u1="X" u2="d" k="35" />
    <hkern u1="X" u2="c" k="35" />
    <hkern u1="X" u2="a" k="35" />
    <hkern u1="b" u2="Y" k="95" />
    <hkern u1="b" u2="X" k="35" />
    <hkern u1="b" u2="W" k="53" />
    <hkern u1="e" u2="Y" k="95" />
    <hkern u1="e" u2="X" k="35" />
    <hkern u1="e" u2="W" k="53" />
    <hkern u1="k" u2="&#x153;" k="30" />
    <hkern u1="k" u2="&#x151;" k="30" />
    <hkern u1="k" u2="&#x14d;" k="30" />
    <hkern u1="k" u2="&#x123;" k="30" />
    <hkern u1="k" u2="&#x121;" k="30" />
    <hkern u1="k" u2="&#x11f;" k="30" />
    <hkern u1="k" u2="&#x11b;" k="30" />
    <hkern u1="k" u2="&#x119;" k="30" />
    <hkern u1="k" u2="&#x117;" k="30" />
    <hkern u1="k" u2="&#x113;" k="30" />
    <hkern u1="k" u2="&#x111;" k="30" />
    <hkern u1="k" u2="&#x10f;" k="30" />
    <hkern u1="k" u2="&#x10d;" k="30" />
    <hkern u1="k" u2="&#x10b;" k="30" />
    <hkern u1="k" u2="&#x107;" k="30" />
    <hkern u1="k" u2="&#x105;" k="30" />
    <hkern u1="k" u2="&#x103;" k="30" />
    <hkern u1="k" u2="&#x101;" k="30" />
    <hkern u1="k" u2="&#xf8;" k="30" />
    <hkern u1="k" u2="&#xf6;" k="30" />
    <hkern u1="k" u2="&#xf5;" k="30" />
    <hkern u1="k" u2="&#xf4;" k="30" />
    <hkern u1="k" u2="&#xf3;" k="30" />
    <hkern u1="k" u2="&#xf2;" k="30" />
    <hkern u1="k" u2="&#xeb;" k="30" />
    <hkern u1="k" u2="&#xea;" k="30" />
    <hkern u1="k" u2="&#xe9;" k="30" />
    <hkern u1="k" u2="&#xe8;" k="30" />
    <hkern u1="k" u2="&#xe7;" k="30" />
    <hkern u1="k" u2="&#xe6;" k="30" />
    <hkern u1="k" u2="&#xe5;" k="30" />
    <hkern u1="k" u2="&#xe4;" k="30" />
    <hkern u1="k" u2="&#xe3;" k="30" />
    <hkern u1="k" u2="&#xe2;" k="30" />
    <hkern u1="k" u2="&#xe1;" k="30" />
    <hkern u1="k" u2="&#xe0;" k="30" />
    <hkern u1="k" u2="q" k="30" />
    <hkern u1="k" u2="o" k="30" />
    <hkern u1="k" u2="g" k="30" />
    <hkern u1="k" u2="e" k="30" />
    <hkern u1="k" u2="d" k="30" />
    <hkern u1="k" u2="c" k="30" />
    <hkern u1="k" u2="a" k="30" />
    <hkern u1="o" u2="Y" k="95" />
    <hkern u1="o" u2="X" k="35" />
    <hkern u1="o" u2="W" k="53" />
    <hkern u1="p" u2="Y" k="95" />
    <hkern u1="p" u2="X" k="35" />
    <hkern u1="p" u2="W" k="53" />
    <hkern u1="&#xc0;" u2="f" k="20" />
    <hkern u1="&#xc1;" u2="f" k="20" />
    <hkern u1="&#xc2;" u2="f" k="20" />
    <hkern u1="&#xc3;" u2="f" k="20" />
    <hkern u1="&#xc4;" u2="f" k="20" />
    <hkern u1="&#xc5;" u2="f" k="20" />
    <hkern u1="&#xe6;" u2="Y" k="95" />
    <hkern u1="&#xe6;" u2="X" k="35" />
    <hkern u1="&#xe6;" u2="W" k="53" />
    <hkern u1="&#xe8;" u2="Y" k="95" />
    <hkern u1="&#xe8;" u2="X" k="35" />
    <hkern u1="&#xe8;" u2="W" k="53" />
    <hkern u1="&#xe9;" u2="Y" k="95" />
    <hkern u1="&#xe9;" u2="X" k="35" />
    <hkern u1="&#xe9;" u2="W" k="53" />
    <hkern u1="&#xea;" u2="Y" k="95" />
    <hkern u1="&#xea;" u2="X" k="35" />
    <hkern u1="&#xea;" u2="W" k="53" />
    <hkern u1="&#xeb;" u2="Y" k="95" />
    <hkern u1="&#xeb;" u2="X" k="35" />
    <hkern u1="&#xeb;" u2="W" k="53" />
    <hkern u1="&#xf2;" u2="Y" k="95" />
    <hkern u1="&#xf2;" u2="X" k="35" />
    <hkern u1="&#xf2;" u2="W" k="53" />
    <hkern u1="&#xf3;" u2="Y" k="95" />
    <hkern u1="&#xf3;" u2="X" k="35" />
    <hkern u1="&#xf3;" u2="W" k="53" />
    <hkern u1="&#xf4;" u2="Y" k="95" />
    <hkern u1="&#xf4;" u2="X" k="35" />
    <hkern u1="&#xf4;" u2="W" k="53" />
    <hkern u1="&#xf5;" u2="Y" k="95" />
    <hkern u1="&#xf5;" u2="X" k="35" />
    <hkern u1="&#xf5;" u2="W" k="53" />
    <hkern u1="&#xf6;" u2="Y" k="95" />
    <hkern u1="&#xf6;" u2="X" k="35" />
    <hkern u1="&#xf6;" u2="W" k="53" />
    <hkern u1="&#xf8;" u2="Y" k="95" />
    <hkern u1="&#xf8;" u2="X" k="35" />
    <hkern u1="&#xf8;" u2="W" k="53" />
    <hkern u1="&#xfe;" u2="Y" k="95" />
    <hkern u1="&#xfe;" u2="X" k="35" />
    <hkern u1="&#xfe;" u2="W" k="53" />
    <hkern u1="&#x100;" u2="f" k="20" />
    <hkern u1="&#x102;" u2="f" k="20" />
    <hkern u1="&#x104;" u2="f" k="20" />
    <hkern u1="&#x113;" u2="Y" k="95" />
    <hkern u1="&#x113;" u2="X" k="35" />
    <hkern u1="&#x113;" u2="W" k="53" />
    <hkern u1="&#x117;" u2="Y" k="95" />
    <hkern u1="&#x117;" u2="X" k="35" />
    <hkern u1="&#x117;" u2="W" k="53" />
    <hkern u1="&#x119;" u2="Y" k="95" />
    <hkern u1="&#x119;" u2="X" k="35" />
    <hkern u1="&#x119;" u2="W" k="53" />
    <hkern u1="&#x11b;" u2="Y" k="95" />
    <hkern u1="&#x11b;" u2="X" k="35" />
    <hkern u1="&#x11b;" u2="W" k="53" />
    <hkern u1="&#x14d;" u2="Y" k="95" />
    <hkern u1="&#x14d;" u2="X" k="35" />
    <hkern u1="&#x14d;" u2="W" k="53" />
    <hkern u1="&#x151;" u2="Y" k="95" />
    <hkern u1="&#x151;" u2="X" k="35" />
    <hkern u1="&#x151;" u2="W" k="53" />
    <hkern u1="&#x153;" u2="Y" k="95" />
    <hkern u1="&#x153;" u2="X" k="35" />
    <hkern u1="&#x153;" u2="W" k="53" />
    <hkern g1="at"
	g2="four"
	k="10" />
    <hkern g1="at"
	g2="six"
	k="20" />
    <hkern g1="at"
	g2="two"
	k="10" />
    <hkern g1="at"
	g2="zero"
	k="40" />
    <hkern g1="at"
	g2="eight"
	k="20" />
    <hkern g1="uni20BF"
	g2="nine"
	k="10" />
    <hkern g1="uni20BF"
	g2="one"
	k="10" />
    <hkern g1="uni20BF"
	g2="three"
	k="10" />
    <hkern g1="cent"
	g2="five"
	k="20" />
    <hkern g1="cent"
	g2="two"
	k="20" />
    <hkern g1="copyright,registered"
	g2="five"
	k="10" />
    <hkern g1="copyright,registered"
	g2="four"
	k="20" />
    <hkern g1="copyright,registered"
	g2="six"
	k="10" />
    <hkern g1="copyright,registered"
	g2="three"
	k="10" />
    <hkern g1="copyright,registered"
	g2="eight"
	k="10" />
    <hkern g1="currency,emptyset"
	g2="five"
	k="30" />
    <hkern g1="currency,emptyset"
	g2="seven"
	k="25" />
    <hkern g1="currency,emptyset"
	g2="six"
	k="5" />
    <hkern g1="currency,emptyset"
	g2="zero"
	k="35" />
    <hkern g1="currency,emptyset"
	g2="eight"
	k="5" />
    <hkern g1="dollar"
	g2="five"
	k="10" />
    <hkern g1="dollar"
	g2="nine"
	k="10" />
    <hkern g1="dollar"
	g2="one"
	k="5" />
    <hkern g1="dollar"
	g2="six"
	k="10" />
    <hkern g1="dollar"
	g2="eight"
	k="20" />
    <hkern g1="Euro"
	g2="five"
	k="10" />
    <hkern g1="Euro"
	g2="seven"
	k="10" />
    <hkern g1="franc"
	g2="five"
	k="50" />
    <hkern g1="franc"
	g2="nine"
	k="20" />
    <hkern g1="franc"
	g2="one"
	k="15" />
    <hkern g1="franc"
	g2="zero"
	k="40" />
    <hkern g1="franc"
	g2="eight"
	k="-20" />
    <hkern g1="lira"
	g2="seven"
	k="20" />
    <hkern g1="lira"
	g2="six"
	k="10" />
    <hkern g1="lira"
	g2="zero"
	k="10" />
    <hkern g1="paragraph"
	g2="five"
	k="10" />
    <hkern g1="paragraph"
	g2="four"
	k="5" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="two"
	k="10" />
    <hkern g1="paragraph"
	g2="zero"
	k="5" />
    <hkern g1="paragraph"
	g2="eight"
	k="20" />
    <hkern g1="percent,perthousand"
	g2="five"
	k="-3009" />
    <hkern g1="backslash"
	g2="eight"
	k="20" />
    <hkern g1="backslash"
	g2="seven"
	k="30" />
    <hkern g1="backslash"
	g2="six"
	k="20" />
    <hkern g1="backslash"
	g2="three"
	k="80" />
    <hkern g1="backslash"
	g2="five"
	k="40" />
    <hkern g1="braceleft"
	g2="four"
	k="10" />
    <hkern g1="braceleft"
	g2="two"
	k="20" />
    <hkern g1="braceright"
	g2="one"
	k="10" />
    <hkern g1="braceright"
	g2="zero"
	k="-25" />
    <hkern g1="braceright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="bracketleft"
	g2="five"
	k="20" />
    <hkern g1="bracketleft"
	g2="nine"
	k="10" />
    <hkern g1="bracketleft"
	g2="asterisk,degree,trademark"
	k="60" />
    <hkern g1="bracketleft"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="80" />
    <hkern g1="bracketleft"
	g2="two"
	k="20" />
    <hkern g1="bracketleft"
	g2="space"
	k="60" />
    <hkern g1="bracketleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="four"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="three"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="two"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="four"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="three"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="zero"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="eight"
	k="5" />
    <hkern g1="guillemotright,guilsinglright"
	g2="four"
	k="35" />
    <hkern g1="guillemotright,guilsinglright"
	g2="five"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="one"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="zero"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="asterisk,degree,trademark"
	k="29" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="numbersign"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="parenleft"
	g2="eight"
	k="-20" />
    <hkern g1="parenleft"
	g2="four"
	k="-20" />
    <hkern g1="parenleft"
	g2="seven"
	k="30" />
    <hkern g1="parenleft"
	g2="six"
	k="-20" />
    <hkern g1="parenleft"
	g2="five"
	k="-20" />
    <hkern g1="parenleft"
	g2="nine"
	k="-20" />
    <hkern g1="parenleft"
	g2="one"
	k="60" />
    <hkern g1="parenleft"
	g2="two"
	k="10" />
    <hkern g1="parenright"
	g2="eight"
	k="30" />
    <hkern g1="parenright"
	g2="four"
	k="20" />
    <hkern g1="parenright"
	g2="asterisk,degree,trademark"
	k="35" />
    <hkern g1="parenright"
	g2="space"
	k="20" />
    <hkern g1="parenright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="eight"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="four"
	k="100" />
    <hkern g1="periodcentered,bullet"
	g2="seven"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="two"
	k="-60" />
    <hkern g1="periodcentered,bullet"
	g2="space"
	k="50" />
    <hkern g1="periodcentered,bullet"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="question"
	g2="three"
	k="80" />
    <hkern g1="question"
	g2="nine"
	k="30" />
    <hkern g1="question"
	g2="one"
	k="100" />
    <hkern g1="question"
	g2="zero"
	k="-10" />
    <hkern g1="question"
	g2="asterisk,degree,trademark"
	k="80" />
    <hkern g1="question"
	g2="space"
	k="10" />
    <hkern g1="question"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="questiondown"
	g2="six"
	k="30" />
    <hkern g1="questiondown"
	g2="three"
	k="30" />
    <hkern g1="questiondown"
	g2="five"
	k="10" />
    <hkern g1="questiondown"
	g2="nine"
	k="40" />
    <hkern g1="questiondown"
	g2="one"
	k="40" />
    <hkern g1="questiondown"
	g2="zero"
	k="40" />
    <hkern g1="questiondown"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="questiondown"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="100" />
    <hkern g1="questiondown"
	g2="two"
	k="50" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five"
	k="-4337" />
    <hkern g1="five"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-20" />
    <hkern g1="five"
	g2="V"
	k="15" />
    <hkern g1="five"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="five"
	g2="X"
	k="5" />
    <hkern g1="five"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="5" />
    <hkern g1="five"
	g2="asterisk,degree,trademark"
	k="30" />
    <hkern g1="five"
	g2="backslash"
	k="10" />
    <hkern g1="five"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="five"
	g2="five"
	k="10" />
    <hkern g1="five"
	g2="Euro"
	k="15" />
    <hkern g1="five"
	g2="hbar"
	k="10" />
    <hkern g1="five"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="five"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="five"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="five"
	g2="copyright,registered"
	k="5" />
    <hkern g1="four"
	g2="parenright"
	k="15" />
    <hkern g1="four"
	g2="Hbar"
	k="10" />
    <hkern g1="four"
	g2="Tbar"
	k="10" />
    <hkern g1="four"
	g2="at"
	k="10" />
    <hkern g1="four"
	g2="uni20BF"
	k="15" />
    <hkern g1="four"
	g2="braceright"
	k="5" />
    <hkern g1="four"
	g2="cent"
	k="10" />
    <hkern g1="four"
	g2="Euro"
	k="-20" />
    <hkern g1="four"
	g2="hbar"
	k="20" />
    <hkern g1="four"
	g2="lira"
	k="10" />
    <hkern g1="four"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="four"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="four"
	g2="zero"
	k="10" />
    <hkern g1="four"
	g2="dollar"
	k="10" />
    <hkern g1="nine"
	g2="ampersand"
	k="15" />
    <hkern g1="nine"
	g2="eight"
	k="10" />
    <hkern g1="nine"
	g2="florin"
	k="10" />
    <hkern g1="nine"
	g2="four"
	k="10" />
    <hkern g1="nine"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="15" />
    <hkern g1="nine"
	g2="radical"
	k="5" />
    <hkern g1="nine"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="nine"
	g2="parenright"
	k="-20" />
    <hkern g1="nine"
	g2="Hbar"
	k="20" />
    <hkern g1="nine"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="nine"
	g2="at"
	k="10" />
    <hkern g1="nine"
	g2="uni20BF"
	k="30" />
    <hkern g1="nine"
	g2="braceleft"
	k="10" />
    <hkern g1="nine"
	g2="bracketright"
	k="10" />
    <hkern g1="nine"
	g2="cent"
	k="10" />
    <hkern g1="nine"
	g2="Euro"
	k="15" />
    <hkern g1="nine"
	g2="hbar"
	k="-10" />
    <hkern g1="nine"
	g2="lira"
	k="10" />
    <hkern g1="nine"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="nine"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="nine"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="one"
	g2="x"
	k="15" />
    <hkern g1="one"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="one"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="one"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="one"
	g2="paragraph"
	k="15" />
    <hkern g1="one"
	g2="J"
	k="5" />
    <hkern g1="one"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="one"
	g2="ampersand"
	k="-20" />
    <hkern g1="one"
	g2="eight"
	k="20" />
    <hkern g1="one"
	g2="eth"
	k="10" />
    <hkern g1="one"
	g2="four"
	k="10" />
    <hkern g1="one"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="one"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="one"
	g2="six"
	k="10" />
    <hkern g1="one"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="one"
	g2="parenright"
	k="15" />
    <hkern g1="one"
	g2="Hbar"
	k="-10" />
    <hkern g1="one"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="one"
	g2="Tbar"
	k="10" />
    <hkern g1="one"
	g2="at"
	k="30" />
    <hkern g1="one"
	g2="uni20BF"
	k="10" />
    <hkern g1="seven"
	g2="underscore"
	k="15" />
    <hkern g1="seven"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="seven"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="seven"
	g2="nine"
	k="15" />
    <hkern g1="seven"
	g2="percent,perthousand"
	k="5" />
    <hkern g1="seven"
	g2="slash"
	k="10" />
    <hkern g1="seven"
	g2="x"
	k="-20" />
    <hkern g1="seven"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="seven"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="seven"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="seven"
	g2="paragraph"
	k="30" />
    <hkern g1="seven"
	g2="questiondown"
	k="10" />
    <hkern g1="seven"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="seven"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="seven"
	g2="ampersand"
	k="15" />
    <hkern g1="seven"
	g2="eight"
	k="-10" />
    <hkern g1="seven"
	g2="eth"
	k="10" />
    <hkern g1="seven"
	g2="florin"
	k="10" />
    <hkern g1="seven"
	g2="four"
	k="30" />
    <hkern g1="seven"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="six"
	g2="asterisk,degree,trademark"
	k="15" />
    <hkern g1="six"
	g2="backslash"
	k="10" />
    <hkern g1="six"
	g2="five"
	k="10" />
    <hkern g1="six"
	g2="one"
	k="10" />
    <hkern g1="six"
	g2="periodcentered,bullet"
	k="15" />
    <hkern g1="six"
	g2="seven"
	k="5" />
    <hkern g1="six"
	g2="two"
	k="10" />
    <hkern g1="six"
	g2="underscore"
	k="-20" />
    <hkern g1="six"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="six"
	g2="yen"
	k="10" />
    <hkern g1="six"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="six"
	g2="nine"
	k="30" />
    <hkern g1="six"
	g2="numbersign"
	k="10" />
    <hkern g1="six"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="six"
	g2="slash"
	k="10" />
    <hkern g1="six"
	g2="x"
	k="15" />
    <hkern g1="six"
	g2="T,uni0162,Tcaron,uni021A"
	k="-10" />
    <hkern g1="six"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="six"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="six"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="six"
	g2="paragraph"
	k="10" />
    <hkern g1="three"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="three"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="three"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="three"
	g2="asterisk,degree,trademark"
	k="-20" />
    <hkern g1="three"
	g2="backslash"
	k="20" />
    <hkern g1="three"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="three"
	g2="one"
	k="10" />
    <hkern g1="three"
	g2="periodcentered,bullet"
	k="30" />
    <hkern g1="three"
	g2="question"
	k="10" />
    <hkern g1="three"
	g2="three"
	k="10" />
    <hkern g1="three"
	g2="two"
	k="10" />
    <hkern g1="three"
	g2="underscore"
	k="15" />
    <hkern g1="three"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="three"
	g2="yen"
	k="10" />
    <hkern g1="three"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="three"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="three"
	g2="nine"
	k="10" />
    <hkern g1="three"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="5" />
    <hkern g1="three"
	g2="zero"
	k="50" />
    <hkern g1="three"
	g2="copyright,registered"
	k="30" />
    <hkern g1="three"
	g2="dollar"
	k="10" />
    <hkern g1="two"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="two"
	g2="V"
	k="10" />
    <hkern g1="two"
	g2="X"
	k="10" />
    <hkern g1="two"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="two"
	g2="asterisk,degree,trademark"
	k="15" />
    <hkern g1="two"
	g2="backslash"
	k="-10" />
    <hkern g1="two"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="two"
	g2="five"
	k="10" />
    <hkern g1="two"
	g2="one"
	k="30" />
    <hkern g1="two"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="two"
	g2="uni20BF"
	k="5" />
    <hkern g1="two"
	g2="braceleft"
	k="50" />
    <hkern g1="two"
	g2="braceright"
	k="30" />
    <hkern g1="two"
	g2="bracketright"
	k="10" />
    <hkern g1="two"
	g2="cent"
	k="70" />
    <hkern g1="two"
	g2="Euro"
	k="90" />
    <hkern g1="two"
	g2="hbar"
	k="80" />
    <hkern g1="two"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="two"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="-20" />
    <hkern g1="two"
	g2="zero"
	k="55" />
    <hkern g1="two"
	g2="copyright,registered"
	k="40" />
    <hkern g1="zero"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="zero"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="5" />
    <hkern g1="zero"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="50" />
    <hkern g1="zero"
	g2="radical"
	k="30" />
    <hkern g1="zero"
	g2="six"
	k="10" />
    <hkern g1="zero"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="70" />
    <hkern g1="zero"
	g2="parenright"
	k="90" />
    <hkern g1="zero"
	g2="Hbar"
	k="80" />
    <hkern g1="zero"
	g2="at"
	k="40" />
    <hkern g1="zero"
	g2="uni20BF"
	k="-20" />
    <hkern g1="zero"
	g2="braceleft"
	k="55" />
    <hkern g1="zero"
	g2="braceright"
	k="40" />
    <hkern g1="zero"
	g2="hbar"
	k="20" />
    <hkern g1="zero"
	g2="lira"
	k="10" />
    <hkern g1="zero"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="zero"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="25" />
    <hkern g1="zero"
	g2="zero"
	k="-10" />
    <hkern g1="zero"
	g2="copyright,registered"
	k="50" />
    <hkern g1="zero"
	g2="dollar"
	k="40" />
    <hkern g1="B,germandbls"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="B,germandbls"
	g2="J"
	k="30" />
    <hkern g1="B,germandbls"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="35" />
    <hkern g1="B,germandbls"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="50" />
    <hkern g1="B,germandbls"
	g2="T,uni0162,Tcaron,uni021A"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="Tbar"
	k="13" />
    <hkern g1="B,germandbls"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="65" />
    <hkern g1="B,germandbls"
	g2="V"
	k="35" />
    <hkern g1="B,germandbls"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="B,germandbls"
	g2="X"
	k="60" />
    <hkern g1="B,germandbls"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="85" />
    <hkern g1="B,germandbls"
	g2="ampersand"
	k="-20" />
    <hkern g1="B,germandbls"
	g2="asterisk,degree,trademark"
	k="80" />
    <hkern g1="B,germandbls"
	g2="at"
	k="20" />
    <hkern g1="B,germandbls"
	g2="backslash"
	k="35" />
    <hkern g1="B,germandbls"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="B,germandbls"
	g2="copyright,registered"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eight"
	k="20" />
    <hkern g1="B,germandbls"
	g2="eth"
	k="10" />
    <hkern g1="B,germandbls"
	g2="five"
	k="35" />
    <hkern g1="B,germandbls"
	g2="four"
	k="-5" />
    <hkern g1="B,germandbls"
	g2="guillemotleft,guilsinglleft"
	k="27" />
    <hkern g1="B,germandbls"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="B,germandbls"
	g2="underscore"
	k="10" />
    <hkern g1="B,germandbls"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="B,germandbls"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="8" />
    <hkern g1="B,germandbls"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="B,germandbls"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="15" />
    <hkern g1="B,germandbls"
	g2="braceright"
	k="22" />
    <hkern g1="B,germandbls"
	g2="exclam,exclamdown"
	k="20" />
    <hkern g1="B,germandbls"
	g2="parenright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="30" />
    <hkern g1="B,germandbls"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="3" />
    <hkern g1="B,germandbls"
	g2="Hbar"
	k="20" />
    <hkern g1="B,germandbls"
	g2="bracketright"
	k="20" />
    <hkern g1="B,germandbls"
	g2="hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Eth,Dcroat"
	k="27" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="nine"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="numbersign"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="one"
	k="8" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="paragraph"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="periodcentered,bullet"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="question"
	k="22" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="questiondown"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="section"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="seven"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="six"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="three"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="slash"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="20" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="Hbar"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="parenleft"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="Tbar"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="V"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="22" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="ampersand"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="asterisk,degree,trademark"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="at"
	k="30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="backslash"
	k="3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eight"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="eth"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="section"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="seven"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="six"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="underscore"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Edotaccent,Eogonek,Ecaron,OE"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="F"
	g2="Tbar"
	k="10" />
    <hkern g1="F"
	g2="X"
	k="10" />
    <hkern g1="F"
	g2="at"
	k="20" />
    <hkern g1="F"
	g2="backslash"
	k="20" />
    <hkern g1="F"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="F"
	g2="five"
	k="5" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="F"
	g2="numbersign"
	k="10" />
    <hkern g1="F"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="5" />
    <hkern g1="F"
	g2="one"
	k="10" />
    <hkern g1="F"
	g2="paragraph"
	k="40" />
    <hkern g1="F"
	g2="periodcentered,bullet"
	k="20" />
    <hkern g1="F"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="F"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="F"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="F"
	g2="slash"
	k="10" />
    <hkern g1="F"
	g2="braceright"
	k="30" />
    <hkern g1="F"
	g2="exclam,exclamdown"
	k="20" />
    <hkern g1="F"
	g2="parenright"
	k="5" />
    <hkern g1="F"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="18" />
    <hkern g1="F"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="15" />
    <hkern g1="F"
	g2="Hbar"
	k="25" />
    <hkern g1="F"
	g2="two"
	k="40" />
    <hkern g1="F"
	g2="bracketright"
	k="5" />
    <hkern g1="F"
	g2="hbar"
	k="10" />
    <hkern g1="F"
	g2="j"
	k="40" />
    <hkern g1="F"
	g2="braceleft"
	k="25" />
    <hkern g1="Hbar"
	g2="J"
	k="15" />
    <hkern g1="Hbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Hbar"
	g2="Tbar"
	k="5" />
    <hkern g1="Hbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Hbar"
	g2="V"
	k="40" />
    <hkern g1="Hbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="Hbar"
	g2="one"
	k="10" />
    <hkern g1="Hbar"
	g2="paragraph"
	k="20" />
    <hkern g1="Hbar"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="Hbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="Hbar"
	g2="question"
	k="30" />
    <hkern g1="Hbar"
	g2="questiondown"
	k="20" />
    <hkern g1="Hbar"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="Hbar"
	g2="section"
	k="18" />
    <hkern g1="Hbar"
	g2="seven"
	k="15" />
    <hkern g1="Hbar"
	g2="six"
	k="25" />
    <hkern g1="Hbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="Hbar"
	g2="three"
	k="5" />
    <hkern g1="Hbar"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="Hbar"
	g2="x"
	k="40" />
    <hkern g1="Hbar"
	g2="zero"
	k="25" />
    <hkern g1="Hbar"
	g2="exclam,exclamdown"
	k="9" />
    <hkern g1="Hbar"
	g2="two"
	k="10" />
    <hkern g1="Hbar"
	g2="bracketright"
	k="40" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="K,uni0136"
	g2="V"
	k="20" />
    <hkern g1="K,uni0136"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="K,uni0136"
	g2="X"
	k="10" />
    <hkern g1="K,uni0136"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="K,uni0136"
	g2="ampersand"
	k="20" />
    <hkern g1="K,uni0136"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="K,uni0136"
	g2="at"
	k="18" />
    <hkern g1="K,uni0136"
	g2="backslash"
	k="15" />
    <hkern g1="K,uni0136"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="25" />
    <hkern g1="K,uni0136"
	g2="copyright,registered"
	k="40" />
    <hkern g1="K,uni0136"
	g2="eight"
	k="5" />
    <hkern g1="K,uni0136"
	g2="eth"
	k="10" />
    <hkern g1="K,uni0136"
	g2="four"
	k="40" />
    <hkern g1="K,uni0136"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="K,uni0136"
	g2="questiondown"
	k="9" />
    <hkern g1="K,uni0136"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="K,uni0136"
	g2="three"
	k="40" />
    <hkern g1="K,uni0136"
	g2="underscore"
	k="10" />
    <hkern g1="K,uni0136"
	g2="colon,semicolon"
	k="25" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="13" />
    <hkern g1="K,uni0136"
	g2="braceright"
	k="30" />
    <hkern g1="K,uni0136"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="K,uni0136"
	g2="parenright"
	k="5" />
    <hkern g1="K,uni0136"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="K,uni0136"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="5" />
    <hkern g1="K,uni0136"
	g2="Hbar"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="Eth,Dcroat"
	k="25" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="ampersand"
	k="9" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="copyright,registered"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="eight"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="numbersign"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="paragraph"
	k="25" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="periodcentered,bullet"
	k="13" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="question"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="questiondown"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="section"
	k="10" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="seven"
	k="5" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="six"
	k="30" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="exclam,exclamdown"
	k="15" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="parenright"
	k="23" />
    <hkern g1="L,Lacute,uni013B,Lcaron,Lslash"
	g2="j"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="V"
	k="25" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="13" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="ampersand"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="at"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="backslash"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="questiondown"
	k="15" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="23" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="x"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="5" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="hbar"
	k="20" />
    <hkern g1="D,G,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="15" />
    <hkern g1="P"
	g2="ampersand"
	k="15" />
    <hkern g1="P"
	g2="asterisk,degree,trademark"
	k="23" />
    <hkern g1="P"
	g2="four"
	k="10" />
    <hkern g1="P"
	g2="nine"
	k="-10" />
    <hkern g1="P"
	g2="one"
	k="5" />
    <hkern g1="P"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="P"
	g2="questiondown"
	k="10" />
    <hkern g1="P"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="P"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="15" />
    <hkern g1="P"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="P"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="P"
	g2="slash"
	k="10" />
    <hkern g1="P"
	g2="parenright"
	k="23" />
    <hkern g1="P"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="P"
	g2="bracketright"
	k="10" />
    <hkern g1="P"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="-10" />
    <hkern g1="P"
	g2="j"
	k="-10" />
    <hkern g1="P"
	g2="braceleft"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="ampersand"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="eth"
	k="20" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="five"
	k="15" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="nine"
	k="5" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="paragraph"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="23" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="section"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="three"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="x"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="zero"
	k="-10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Hbar"
	k="15" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="two"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="V"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="X"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="asterisk,degree,trademark"
	k="23" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="at"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="eight"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="five"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="four"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="guillemotleft,guilsinglleft"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="section"
	k="40" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="seven"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="six"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="underscore"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="100" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="exclam,exclamdown"
	k="25" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="two"
	k="20" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="braceleft"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,uni0218"
	g2="parenleft"
	k="5" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="at"
	k="40" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="backslash"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="copyright,registered"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="numbersign"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="one"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="periodcentered,bullet"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="questiondown"
	k="25" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="zero"
	k="5" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="5" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="60" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="slash"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="braceright"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="Hbar"
	k="20" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="two"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="bracketright"
	k="15" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="j"
	k="18" />
    <hkern g1="T,uni0162,Tcaron,uni021A"
	g2="braceleft"
	k="90" />
    <hkern g1="Tbar"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="Tbar"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="Tbar"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="100" />
    <hkern g1="Tbar"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Tbar"
	g2="ampersand"
	k="25" />
    <hkern g1="Tbar"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Tbar"
	g2="five"
	k="-10" />
    <hkern g1="Tbar"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="Tbar"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="Tbar"
	g2="hyphen,endash,emdash"
	k="60" />
    <hkern g1="Tbar"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-20" />
    <hkern g1="Tbar"
	g2="one"
	k="15" />
    <hkern g1="Tbar"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="15" />
    <hkern g1="Tbar"
	g2="question"
	k="10" />
    <hkern g1="Tbar"
	g2="questiondown"
	k="10" />
    <hkern g1="Tbar"
	g2="section"
	k="50" />
    <hkern g1="Tbar"
	g2="six"
	k="20" />
    <hkern g1="Tbar"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="80" />
    <hkern g1="Tbar"
	g2="three"
	k="15" />
    <hkern g1="Tbar"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="30" />
    <hkern g1="Tbar"
	g2="x"
	k="18" />
    <hkern g1="Tbar"
	g2="zero"
	k="90" />
    <hkern g1="Tbar"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="Tbar"
	g2="underscore"
	k="5" />
    <hkern g1="Tbar"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="Tbar"
	g2="two"
	k="5" />
    <hkern g1="Thorn"
	g2="Eth,Dcroat"
	k="5" />
    <hkern g1="Thorn"
	g2="J"
	k="5" />
    <hkern g1="Thorn"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="60" />
    <hkern g1="Thorn"
	g2="Tbar"
	k="-20" />
    <hkern g1="Thorn"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="Thorn"
	g2="X"
	k="15" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="Thorn"
	g2="ampersand"
	k="10" />
    <hkern g1="Thorn"
	g2="at"
	k="50" />
    <hkern g1="Thorn"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="Thorn"
	g2="copyright,registered"
	k="80" />
    <hkern g1="Thorn"
	g2="eight"
	k="15" />
    <hkern g1="Thorn"
	g2="five"
	k="30" />
    <hkern g1="Thorn"
	g2="four"
	k="18" />
    <hkern g1="Thorn"
	g2="guillemotleft,guilsinglleft"
	k="90" />
    <hkern g1="Thorn"
	g2="hyphen,endash,emdash"
	k="-10" />
    <hkern g1="Thorn"
	g2="numbersign"
	k="5" />
    <hkern g1="Thorn"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="Thorn"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Eth,Dcroat"
	k="90" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="Tbar"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="copyright,registered"
	k="5" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="seven"
	k="10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-10" />
    <hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="V"
	g2="backslash"
	k="10" />
    <hkern g1="V"
	g2="periodcentered,bullet"
	k="-10" />
    <hkern g1="V"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="V"
	g2="hbar"
	k="20" />
    <hkern g1="V"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="33" />
    <hkern g1="V"
	g2="j"
	k="54" />
    <hkern g1="V"
	g2="braceleft"
	k="38" />
    <hkern g1="V"
	g2="parenleft"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="five"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="54" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="zero"
	k="38" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="underscore"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceright"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright"
	k="-25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="80" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Hbar"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="two"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="bracketright"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hbar"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="braceleft"
	k="40" />
    <hkern g1="X"
	g2="eth"
	k="20" />
    <hkern g1="X"
	g2="five"
	k="33" />
    <hkern g1="X"
	g2="four"
	k="54" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="38" />
    <hkern g1="X"
	g2="guillemotright,guilsinglright"
	k="20" />
    <hkern g1="X"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="X"
	g2="nine"
	k="40" />
    <hkern g1="X"
	g2="numbersign"
	k="30" />
    <hkern g1="X"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="X"
	g2="one"
	k="10" />
    <hkern g1="X"
	g2="paragraph"
	k="30" />
    <hkern g1="X"
	g2="periodcentered,bullet"
	k="40" />
    <hkern g1="X"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="10" />
    <hkern g1="X"
	g2="question"
	k="30" />
    <hkern g1="X"
	g2="questiondown"
	k="10" />
    <hkern g1="X"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-25" />
    <hkern g1="X"
	g2="section"
	k="80" />
    <hkern g1="X"
	g2="seven"
	k="20" />
    <hkern g1="X"
	g2="six"
	k="40" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="X"
	g2="three"
	k="40" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="60" />
    <hkern g1="X"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="X"
	g2="x"
	k="60" />
    <hkern g1="X"
	g2="zero"
	k="40" />
    <hkern g1="X"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="X"
	g2="underscore"
	k="30" />
    <hkern g1="X"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="X"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="50" />
    <hkern g1="X"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="X"
	g2="slash"
	k="30" />
    <hkern g1="X"
	g2="braceright"
	k="20" />
    <hkern g1="X"
	g2="parenright"
	k="40" />
    <hkern g1="X"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="X"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="X"
	g2="Hbar"
	k="40" />
    <hkern g1="X"
	g2="two"
	k="65" />
    <hkern g1="X"
	g2="hbar"
	k="50" />
    <hkern g1="X"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="10" />
    <hkern g1="X"
	g2="braceleft"
	k="50" />
    <hkern g1="X"
	g2="parenleft"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Eth,Dcroat"
	k="38" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Tbar"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="X"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ampersand"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk,degree,trademark"
	k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="backslash"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="copyright,registered"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eight"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="eth"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="five"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="four"
	k="60" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="nine"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="numbersign"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="one"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="paragraph"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="question"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="section"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="seven"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="six"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="zero"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="parenright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Eth,Dcroat"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Tbar"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="V"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="X"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="asterisk,degree,trademark"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="at"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="backslash"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="copyright,registered"
	k="65" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="eth"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="five"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="questiondown"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="40" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="underscore"
	k="105" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="95" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="colon,semicolon"
	k="105" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="90" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceright"
	k="110" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="exclam,exclamdown"
	k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="parenright"
	k="160" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="150" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Hbar"
	k="-30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="two"
	k="30" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hbar"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="braceleft"
	k="70" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="75" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="three"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="zero"
	k="65" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="Hbar"
	k="90" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="braceright"
	k="30" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="bracketright"
	k="80" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="numbersign"
	k="40" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="parenright"
	k="40" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="quotedbl,quotesingle"
	k="40" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="yen"
	k="15" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="20" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="j"
	k="3" />
    <hkern g1="dcroat"
	g2="J"
	k="75" />
    <hkern g1="dcroat"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="dcroat"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="65" />
    <hkern g1="dcroat"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="dcroat"
	g2="at"
	k="90" />
    <hkern g1="dcroat"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="dcroat"
	g2="hyphen,endash,emdash"
	k="80" />
    <hkern g1="dcroat"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="40" />
    <hkern g1="dcroat"
	g2="periodcentered,bullet"
	k="40" />
    <hkern g1="dcroat"
	g2="section"
	k="40" />
    <hkern g1="dcroat"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="15" />
    <hkern g1="dcroat"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="dcroat"
	g2="copyright,registered"
	k="3" />
    <hkern g1="dcroat"
	g2="eight"
	k="10" />
    <hkern g1="dcroat"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="dcroat"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="5" />
    <hkern g1="dcroat"
	g2="three"
	k="20" />
    <hkern g1="dcroat"
	g2="zero"
	k="15" />
    <hkern g1="dcroat"
	g2="Eth,Dcroat"
	k="-10" />
    <hkern g1="dcroat"
	g2="Hbar"
	k="20" />
    <hkern g1="dcroat"
	g2="braceright"
	k="5" />
    <hkern g1="dcroat"
	g2="bracketright"
	k="30" />
    <hkern g1="dcroat"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="dcroat"
	g2="hbar"
	k="10" />
    <hkern g1="eth"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="75" />
    <hkern g1="eth"
	g2="X"
	k="30" />
    <hkern g1="eth"
	g2="ampersand"
	k="65" />
    <hkern g1="eth"
	g2="colon,semicolon"
	k="30" />
    <hkern g1="eth"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="eth"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="30" />
    <hkern g1="eth"
	g2="questiondown"
	k="80" />
    <hkern g1="eth"
	g2="slash"
	k="40" />
    <hkern g1="eth"
	g2="two"
	k="40" />
    <hkern g1="eth"
	g2="underscore"
	k="40" />
    <hkern g1="eth"
	g2="x"
	k="15" />
    <hkern g1="eth"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="eth"
	g2="four"
	k="3" />
    <hkern g1="eth"
	g2="six"
	k="10" />
    <hkern g1="eth"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="eth"
	g2="J"
	k="5" />
    <hkern g1="eth"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="eth"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="15" />
    <hkern g1="eth"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="-10" />
    <hkern g1="eth"
	g2="at"
	k="20" />
    <hkern g1="eth"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="eth"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="eth"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="eth"
	g2="nine"
	k="10" />
    <hkern g1="f,f_f"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="f,f_f"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="f,f_f"
	g2="Tbar"
	k="80" />
    <hkern g1="f,f_f"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="f,f_f"
	g2="asterisk,degree,trademark"
	k="40" />
    <hkern g1="f,f_f"
	g2="backslash"
	k="40" />
    <hkern g1="f,f_f"
	g2="eth"
	k="15" />
    <hkern g1="f,f_f"
	g2="five"
	k="20" />
    <hkern g1="f,f_f"
	g2="one"
	k="3" />
    <hkern g1="f,f_f"
	g2="question"
	k="10" />
    <hkern g1="f,f_f"
	g2="seven"
	k="10" />
    <hkern g1="f,f_f"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="5" />
    <hkern g1="f,f_f"
	g2="X"
	k="20" />
    <hkern g1="f,f_f"
	g2="ampersand"
	k="15" />
    <hkern g1="f,f_f"
	g2="colon,semicolon"
	k="-10" />
    <hkern g1="f,f_f"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="f,f_f"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="f,f_f"
	g2="questiondown"
	k="30" />
    <hkern g1="f,f_f"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="f,f_f"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="f,f_f"
	g2="yen"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="20" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="Tbar"
	k="30" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="V"
	k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="-10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="braceright"
	k="5" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,fi,i.loclTRK,f_f_i,f_f_j,f_j"
	g2="bracketright"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="x"
	k="-10" />
    <hkern g1="k,uni0137"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="k,uni0137"
	g2="hyphen,endash,emdash"
	k="-20" />
    <hkern g1="k,uni0137"
	g2="Hbar"
	k="10" />
    <hkern g1="k,uni0137"
	g2="braceright"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="eth"
	k="-10" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="dcaron,lcaron"
	g2="questiondown"
	k="-20" />
    <hkern g1="dcaron,lcaron"
	g2="at"
	k="10" />
    <hkern g1="dcaron,lcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="Tbar"
	k="-20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="exclam,exclamdown"
	k="50" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="hbar"
	k="30" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="numbersign"
	k="35" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="parenright"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="quotedbl,quotesingle"
	k="55" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="yen"
	k="20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,uni0146,ncaron,eng"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="nine"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="35" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="section"
	k="55" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="three"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="zero"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="bracketright"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="hbar"
	k="25" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="numbersign"
	k="30" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="parenright"
	k="5" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="yen"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="50" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,edotaccent,eogonek,ecaron,omacron,ohungarumlaut,oe"
	g2="j"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="slash"
	k="35" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="two"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="underscore"
	k="55" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="x"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="nine"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="periodcentered,bullet"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="section"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="copyright,registered"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="eight"
	k="25" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="three"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="zero"
	k="15" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Hbar"
	k="5" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="braceright"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="V"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="backslash"
	k="55" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="eth"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="five"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="X"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="ampersand"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="colon,semicolon"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="questiondown"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="slash"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="two"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="underscore"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="x"
	k="20" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="z,zacute,zdotaccent,zcaron"
	k="50" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="four"
	k="30" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="six"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="15" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="at"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,uni0219"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Tbar"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="asterisk,degree,trademark"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="backslash"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="eth"
	k="20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="five"
	k="50" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="one"
	k="30" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="question"
	k="25" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="X"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="ampersand"
	k="15" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="three"
	k="5" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="exclam,exclamdown"
	k="-20" />
    <hkern g1="t,uni0163,tbar,uni021B,f_t,t_t"
	g2="numbersign"
	k="23" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="T,uni0162,Tcaron,uni021A"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="23" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="three"
	k="85" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Eth,Dcroat"
	k="35" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="Hbar"
	k="-21" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="braceright"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="bracketright"
	k="30" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="hbar"
	k="50" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="parenright"
	k="15" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="quotedbl,quotesingle"
	k="35" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="yen"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="i,igrave,iacute,icircumflex,idieresis,imacron,iogonek,dotlessi,i.loclTRK"
	k="65" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	g2="j"
	k="20" />
    <hkern g1="x"
	g2="X"
	k="5" />
    <hkern g1="x"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="-20" />
    <hkern g1="x"
	g2="slash"
	k="23" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="x"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="85" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="35" />
    <hkern g1="x"
	g2="at"
	k="-21" />
    <hkern g1="x"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="x"
	g2="nine"
	k="50" />
    <hkern g1="x"
	g2="periodcentered,bullet"
	k="15" />
    <hkern g1="x"
	g2="section"
	k="35" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="x"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="65" />
    <hkern g1="x"
	g2="copyright,registered"
	k="20" />
    <hkern g1="x"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="5" />
    <hkern g1="x"
	g2="three"
	k="15" />
    <hkern g1="x"
	g2="zero"
	k="5" />
    <hkern g1="x"
	g2="Eth,Dcroat"
	k="15" />
    <hkern g1="x"
	g2="Hbar"
	k="10" />
    <hkern g1="x"
	g2="braceright"
	k="-10" />
    <hkern g1="x"
	g2="bracketright"
	k="38" />
    <hkern g1="x"
	g2="exclam,exclamdown"
	k="10" />
    <hkern g1="x"
	g2="hbar"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="23" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="seven"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="X"
	k="85" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="colon,semicolon"
	k="35" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-21" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="questiondown"
	k="30" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="50" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="two"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="underscore"
	k="35" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="x"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="65" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="four"
	k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="J"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="at"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="38" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="nine"
	k="5" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="j"
	k="30" />
    <hkern g1="tcaron"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-21" />
    <hkern g1="tcaron"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="tcaron"
	g2="Tbar"
	k="30" />
    <hkern g1="tcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="50" />
    <hkern g1="tcaron"
	g2="asterisk,degree,trademark"
	k="15" />
    <hkern g1="tcaron"
	g2="backslash"
	k="35" />
    <hkern g1="tcaron"
	g2="eth"
	k="20" />
    <hkern g1="tcaron"
	g2="five"
	k="65" />
    <hkern g1="tcaron"
	g2="one"
	k="20" />
    <hkern g1="tcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="5" />
    <hkern g1="tcaron"
	g2="X"
	k="15" />
    <hkern g1="tcaron"
	g2="ampersand"
	k="5" />
    <hkern g1="tcaron"
	g2="colon,semicolon"
	k="15" />
    <hkern g1="tcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="tcaron"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="-10" />
    <hkern g1="tcaron"
	g2="questiondown"
	k="38" />
    <hkern g1="tcaron"
	g2="quoteleft,quoteright,quotedblleft,quotedblright"
	k="10" />
    <hkern g1="tcaron"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="tcaron"
	g2="copyright,registered"
	k="30" />
    <hkern g1="tcaron"
	g2="eight"
	k="10" />
    <hkern g1="tcaron"
	g2="guillemotright,guilsinglright"
	k="25" />
    <hkern g1="tcaron"
	g2="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	k="15" />
    <hkern g1="tcaron"
	g2="three"
	k="20" />
    <hkern g1="tcaron"
	g2="bracketright"
	k="10" />
    <hkern g1="tcaron"
	g2="exclam,exclamdown"
	k="5" />
    <hkern g1="at"
	g2="J"
	k="40" />
    <hkern g1="at"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="at"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="90" />
    <hkern g1="at"
	g2="T,uni0162,Tcaron,uni021A"
	k="10" />
    <hkern g1="at"
	g2="Tbar"
	k="-2" />
    <hkern g1="at"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="at"
	g2="V"
	k="35" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="at"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="at"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="at"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="70" />
    <hkern g1="at"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="copyright,registered"
	g2="J"
	k="10" />
    <hkern g1="copyright,registered"
	g2="four"
	k="10" />
    <hkern g1="copyright,registered"
	g2="one"
	k="10" />
    <hkern g1="copyright,registered"
	g2="seven"
	k="70" />
    <hkern g1="copyright,registered"
	g2="six"
	k="30" />
    <hkern g1="copyright,registered"
	g2="zero"
	k="60" />
    <hkern g1="copyright,registered"
	g2="Eth,Dcroat"
	k="30" />
    <hkern g1="copyright,registered"
	g2="eth"
	k="30" />
    <hkern g1="copyright,registered"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="80" />
    <hkern g1="copyright,registered"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="florin"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="florin"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="florin"
	g2="z,zacute,zdotaccent,zcaron"
	k="70" />
    <hkern g1="florin"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="30" />
    <hkern g1="florin"
	g2="five"
	k="60" />
    <hkern g1="florin"
	g2="four"
	k="30" />
    <hkern g1="florin"
	g2="nine"
	k="30" />
    <hkern g1="florin"
	g2="one"
	k="80" />
    <hkern g1="florin"
	g2="seven"
	k="10" />
    <hkern g1="florin"
	g2="zero"
	k="20" />
    <hkern g1="florin"
	g2="Eth,Dcroat"
	k="20" />
    <hkern g1="florin"
	g2="eth"
	k="15" />
    <hkern g1="florin"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="florin"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="5" />
    <hkern g1="paragraph"
	g2="V"
	k="10" />
    <hkern g1="paragraph"
	g2="X"
	k="10" />
    <hkern g1="paragraph"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="paragraph"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="paragraph"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="60" />
    <hkern g1="paragraph"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="30" />
    <hkern g1="paragraph"
	g2="x"
	k="30" />
    <hkern g1="paragraph"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="80" />
    <hkern g1="paragraph"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="paragraph"
	g2="five"
	k="20" />
    <hkern g1="paragraph"
	g2="four"
	k="20" />
    <hkern g1="paragraph"
	g2="nine"
	k="15" />
    <hkern g1="paragraph"
	g2="one"
	k="10" />
    <hkern g1="paragraph"
	g2="seven"
	k="5" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="70" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="60" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="V"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="X"
	k="80" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="x"
	k="15" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="plus,equal,asciitilde,logicalnot,plusminus,multiply,divide,minus,approxequal,notequal"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="section"
	g2="J"
	k="30" />
    <hkern g1="section"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="80" />
    <hkern g1="section"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="section"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="section"
	g2="V"
	k="20" />
    <hkern g1="section"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="section"
	g2="X"
	k="10" />
    <hkern g1="section"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="5" />
    <hkern g1="section"
	g2="zero"
	k="20" />
    <hkern g1="section"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="30" />
    <hkern g1="section"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="section"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="backslash"
	g2="T,uni0162,Tcaron,uni021A"
	k="30" />
    <hkern g1="backslash"
	g2="Tbar"
	k="60" />
    <hkern g1="backslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="backslash"
	g2="V"
	k="20" />
    <hkern g1="backslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="backslash"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="backslash"
	g2="j"
	k="10" />
    <hkern g1="backslash"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="100" />
    <hkern g1="backslash"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="100" />
    <hkern g1="backslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="60" />
    <hkern g1="backslash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="120" />
    <hkern g1="backslash"
	g2="hbar"
	k="80" />
    <hkern g1="backslash"
	g2="X"
	k="50" />
    <hkern g1="backslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="backslash"
	g2="x"
	k="-70" />
    <hkern g1="backslash"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="10" />
    <hkern g1="backslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="backslash"
	g2="Hbar"
	k="20" />
    <hkern g1="braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="80" />
    <hkern g1="braceleft"
	g2="J"
	k="50" />
    <hkern g1="braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="20" />
    <hkern g1="braceleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="-70" />
    <hkern g1="braceleft"
	g2="eth"
	k="10" />
    <hkern g1="braceleft"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="30" />
    <hkern g1="braceleft"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="20" />
    <hkern g1="braceleft"
	g2="Eth,Dcroat"
	k="70" />
    <hkern g1="braceleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="20" />
    <hkern g1="braceleft"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-10" />
    <hkern g1="braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="braceright"
	g2="eth"
	k="-10" />
    <hkern g1="braceright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-10" />
    <hkern g1="braceright"
	g2="T,uni0162,Tcaron,uni021A"
	k="-10" />
    <hkern g1="braceright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-70" />
    <hkern g1="braceright"
	g2="j"
	k="-10" />
    <hkern g1="bracketleft"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="20" />
    <hkern g1="bracketleft"
	g2="j"
	k="10" />
    <hkern g1="bracketleft"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="40" />
    <hkern g1="bracketright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="bracketright"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="-10" />
    <hkern g1="bracketright"
	g2="hbar"
	k="-10" />
    <hkern g1="bracketright"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="-10" />
    <hkern g1="bracketright"
	g2="Hbar"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="eth"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="-10" />
    <hkern g1="exclam,exclamdown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-30" />
    <hkern g1="exclam,exclamdown"
	g2="Hbar"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Eth,Dcroat"
	k="-5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="-5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="110" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="j"
	k="90" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="100" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="45" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="130" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="X"
	k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="x"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J"
	k="-10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="15" />
    <hkern g1="guillemotright,guilsinglright"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="70" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Eth,Dcroat"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="-30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="hyphen,endash,emdash"
	g2="X"
	k="30" />
    <hkern g1="numbersign"
	g2="J"
	k="30" />
    <hkern g1="numbersign"
	g2="Tbar"
	k="40" />
    <hkern g1="numbersign"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="30" />
    <hkern g1="numbersign"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="numbersign"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="numbersign"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="50" />
    <hkern g1="numbersign"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="30" />
    <hkern g1="numbersign"
	g2="x"
	k="10" />
    <hkern g1="parenleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="30" />
    <hkern g1="parenleft"
	g2="S,Sacute,Scedilla,Scaron,uni0218"
	k="10" />
    <hkern g1="parenleft"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="parenleft"
	g2="V"
	k="10" />
    <hkern g1="parenleft"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="40" />
    <hkern g1="parenleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="parenleft"
	g2="hbar"
	k="30" />
    <hkern g1="parenleft"
	g2="X"
	k="20" />
    <hkern g1="parenleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="50" />
    <hkern g1="parenright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="30" />
    <hkern g1="parenright"
	g2="J"
	k="20" />
    <hkern g1="parenright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,uni0122,Omacron,Ohungarumlaut,OE"
	k="50" />
    <hkern g1="parenright"
	g2="Tbar"
	k="30" />
    <hkern g1="parenright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="parenright"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="35" />
    <hkern g1="parenright"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="parenright"
	g2="z,zacute,zdotaccent,zcaron"
	k="60" />
    <hkern g1="parenright"
	g2="Hbar"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="60" />
    <hkern g1="periodcentered,bullet"
	g2="s,sacute,scedilla,scaron,uni0219"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="T,uni0162,Tcaron,uni021A"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="Tbar"
	k="50" />
    <hkern g1="periodcentered,bullet"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="30" />
    <hkern g1="periodcentered,bullet"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="periodcentered,bullet"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="20" />
    <hkern g1="periodcentered,bullet"
	g2="m,n,p,r,ntilde,nacute,uni0146,ncaron,eng,racute,uni0157,rcaron"
	k="35" />
    <hkern g1="periodcentered,bullet"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="question"
	g2="eth"
	k="35" />
    <hkern g1="question"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="10" />
    <hkern g1="question"
	g2="j"
	k="20" />
    <hkern g1="question"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="10" />
    <hkern g1="question"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="30" />
    <hkern g1="question"
	g2="X"
	k="-10" />
    <hkern g1="question"
	g2="z,zacute,zdotaccent,zcaron"
	k="20" />
    <hkern g1="questiondown"
	g2="J"
	k="-10" />
    <hkern g1="questiondown"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="20" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="questiondown"
	g2="j"
	k="5" />
    <hkern g1="questiondown"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="12" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,cdotaccent,ccaron,dcaron,dcroat,emacron,edotaccent,eogonek,ecaron,gbreve,gdotaccent,uni0123,omacron,ohungarumlaut,oe"
	k="-10" />
    <hkern g1="quotedbl,quotesingle"
	g2="Eth,Dcroat"
	k="-70" />
    <hkern g1="quotedbl,quotesingle"
	g2="V"
	k="-10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Uring,Uhungarumlaut,Uogonek"
	k="20" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="10" />
    <hkern g1="quoteleft,quoteright,quotedblleft,quotedblright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="slash"
	g2="Eth,Dcroat"
	k="10" />
    <hkern g1="slash"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="10" />
    <hkern g1="slash"
	g2="j"
	k="60" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="slash"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="10" />
    <hkern g1="slash"
	g2="hbar"
	k="40" />
    <hkern g1="underscore"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek"
	k="40" />
    <hkern g1="underscore"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Thorn,germandbls,Dcaron,Emacron,Edotaccent,Eogonek,Ecaron,Imacron,Iogonek,Idotaccent,uni0136,Lacute,uni013B,Lcaron,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron,product"
	k="20" />
    <hkern g1="underscore"
	g2="f,fi,fl,f_f,f_f_i,f_f_j,f_f_l,f_j,f_t"
	k="80" />
    <hkern g1="underscore"
	g2="j"
	k="40" />
    <hkern g1="underscore"
	g2="b,h,k,l,thorn,uni0137,lacute,uni013C,lcaron"
	k="20" />
    <hkern g1="underscore"
	g2="t,uni0163,tcaron,tbar,uni021B,t_t"
	k="10" />
    <hkern g1="underscore"
	g2="v,w,y,yacute,ydieresis,wcircumflex,ycircumflex,wgrave,wacute,wdieresis,ygrave"
	k="40" />
    <hkern g1="underscore"
	g2="hbar"
	k="30" />
    <hkern g1="underscore"
	g2="Hbar"
	k="30" />
  </font>
</defs></svg>
