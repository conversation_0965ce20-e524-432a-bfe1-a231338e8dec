<?php

use App\Http\Controllers\InstallmentsCalendarController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\EarlyRepaymentController;

Route::middleware([\App\Http\Middleware\CheckLoanStats::class])->group(function () {
    Route::get('/profile/kalendar-vnoski', [InstallmentsCalendarController::class, 'installmentCalendar'])
        ->name('installment.calendar');

    Route::get('/profile/predsrochno-pogasyavane', [EarlyRepaymentController::class, 'index'])
        ->name('early.repayment');

    Route::get('/predsrochno-pogasyavane-smetki/{loanId}/{date?}', [EarlyRepaymentController::class, 'earlyRepaymentData'])
        ->name('earlyRepaymentData');
});
