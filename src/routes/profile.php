<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ActiveLoanController;
use App\Http\Controllers\ProfileSettingsController;
use App\Http\Controllers\IdCardDataController;

Route::middleware([\App\Http\Middleware\UserIsVerifiedMiddleware::class])->group(function () {
    Route::get('/profile/aktiven-kredit', [ActiveLoanController::class, 'index'])
        ->name('active.loan');

    Route::get('/profile/profile-settings', [ProfileSettingsController::class, 'index'])
        ->name('profileSettings');

    Route::post('/profile/profile-settings', [ProfileSettingsController::class, 'store'])
        ->name('profileSettingsUpdate');

    Route::get('/refresh-client-data', [ProfileSettingsController::class, 'refreshData'])->name('refreshClientData');

    Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
});

Route::get('/profile/lichni-danni-full', [IdCardDataController::class, 'index'])->name('id-card-data.index');
Route::post('/profile/lichni-danni-full', [IdCardDataController::class, 'store'])->name('id-card-data.store');
