<?php

use App\Http\Controllers\ContractController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\NewLoanController;

Route::post('/new-loan', NewLoanController::class)
    ->name('newloan')
    ->middleware(\App\Http\Middleware\UserIsVerifiedMiddleware::class);

Route::controller(ContractController::class)->group(function () {
    Route::get('/uspeh', 'getSigned')->name('signed');
    Route::post('/uspeh', 'postSigned')->name('signed.post');

    Route::get('/dogovor', 'index')->name('contract');
    Route::post('/dogovor', 'post')->name('contract.post');

    Route::get('/get-loan-stats', 'getLoanStats')->name('contract.getLoanStats');
});
