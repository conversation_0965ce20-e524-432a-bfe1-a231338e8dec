<?php

use App\Http\Controllers\StaticPagesController;
use App\Http\Controllers\VerifController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\SurveyController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\PosPaymentController;
use App\Http\Controllers\ClientRateUsController;
use App\Http\Controllers\UploadDocumentsController;

Route::get('/get_ip_info/{ip}', function ($ip) {
    return response()->json(getMainLocationDataByIp($ip));
});

Route::get('/', [HomeController::class, 'index'])->name('home.index');
Route::get('/client-rate-us/{clientRateUsId}/show', [ClientRateUsController::class, 'show'])->name('client-rate-us.show');
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{slug}/{parent?}', [BlogController::class, 'show'])->name('blog.show');
Route::post('/document-download', [DocumentController::class, 'store'])->name('document.store');
Route::get('survey/{hash}/{rate}', [SurveyController::class, 'index'])->name('survey');

Route::resource('pos-payment', PosPaymentController::class)->only(['index', 'store']);
Route::any('pos-payment-result', [PosPaymentController::class, 'result'])->name('pos-payment.result');
Route::any('/payments/success', [PosPaymentController::class, 'result'])->name('pos-payment.result.old');

Route::get('/new-docs-upload/{hash}/{success?}', [UploadDocumentsController::class, 'index'])
    ->name('upload-documents.index');

Route::post('/new-docs-upload/{hash}/upload', [UploadDocumentsController::class, 'upload'])
    ->name('upload-documents.upload');

/// VerifController

Route::middleware('auth')->group(function () {
    Route::get('/verification', [VerifController::class, 'index'])->name('verification.index');
    Route::get('/verification/veriff-provider-url', [VerifController::class, 'getVeriffProviderUrl'])->name('verification.getVeriffProviderUrl');
    Route::get('/verification/will-wait', [VerifController::class, 'skipVerification'])->name('verification.skipVerification');
});

//// webhooks
Route::post('/verification/start', [VerifController::class, 'start'])->name('verification.start');
Route::post('/verification/finish', [VerifController::class, 'finish'])->name('verification.finish');

Route::post('/refer-a-friend', [\App\Http\Controllers\ReferFriendController::class, 'store'])
    ->name('refer-a-friend');
Route::get('/preporychai-oshte', [\App\Http\Controllers\ReferFriendController::class, 'referMore'])
    ->name('refer-more');

Route::get('/preporychai-uspeh', [\App\Http\Controllers\ReferFriendController::class, 'success'])
    ->name('refer-success');

Route::get('/preporychai', [\App\Http\Controllers\ReferFriendController::class, 'referFromProfile'])
    ->name('refer-from-profile');

/// END ./VerifController

Route::controller(StaticPagesController::class)->group(function () {

    // ---------- MENU ------------
    // Как работи
    Route::get('/kak-raboti', 'howItWorks')
        ->name('how-it-works');

    // Как да платя
    Route::get('/kak-da-platya', 'howToPay')
        ->name('how-to-pay');

    // Новини
    Route::get('/novini', 'blog')
        ->name('blog');

    // Промоции
    Route::get('/promotions', 'promotions')
        ->name('promotions');

    // Контакти
    Route::get('/contact-us', 'contacts')->name('contacts');
    Route::post('/contact-us', 'sendContactForm')->name('sendContactForm');
    // --------------------------------


    Route::get('/iframe-calculator', 'iframeCalcultor')
        ->name('iframe-calculator');


    Route::get('/chesto-zadavani-vyprosi', 'questions')
        ->name('questions');

    Route::get('/faq', 'faq')
        ->name('faq');

    Route::get('/about-us', 'aboutUs')
        ->name('about-us');

    Route::get('/careers', 'careers')
        ->name('careers');

    Route::get('/careers/{jobId}', 'job')
        ->name('job');

    Route::get('/terms-and-conditions', 'termsAndConditions')
        ->name('terms-and-conditions');

    Route::get('/politika-za-zashtita-na-lichnite-danni', 'privateDataPolicy')
        ->name('politika-za-zashtita-na-lichnite-danni');

    Route::get('/tarifa', 'tarifa')
        ->name('tarifa');

    Route::get('/politika-cookies', 'cookiePolicy')
        ->name('politika-cookies');

    Route::get('/deklaracii-obrabotka-danni', 'declarationsDataUsage')
        ->name('declarations-data-usage');

    Route::get('/deklaraciya-za-obrabotvane-i-sahranyavane-na-lichni-danni-za-celite-na-sklyuchvane-i-izpalnenie-na-dogovor-za-kredit', 'declarationsDataUsagePdf')
        ->name('declarations-data-usage-pdf');

    Route::get('/deklaraciya-e-mail', 'declarationEmail')
        ->name('deklaraciya-e-mail');

    Route::get('/deklaraciya-chl-13', 'declarationSign')
        ->name('declaration-sign');

    Route::get('/deklaraciya-ikt', 'declarationIkt')
        ->name('declaration-ikt');

    Route::get('/success', 'success')
        ->name('success');

    Route::get('/failed', 'failed')
        ->name('failed');

    Route::get('/preporychai-terms-and-conditions', 'referralsTermsAndConditions')
        ->name('referrals.terms');
});
