<?php

use App\Http\Controllers\LoginController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ValidateSmsController;

Route::controller(ValidateSmsController::class)->group(function () {
    Route::get('vhod-sms','index')->name('login.sms');
    Route::post('vhod-sms', 'store')->name('login.sms.post');
});

Route::controller(LoginController::class)->group(function () {
    Route::get('vhod-v-profil', 'index')->name('login');
    Route::post('vhod-v-profil', 'post')->name('login.post');

    Route::get('logout', 'logout')->name('logout');

    Route::get('vhod-hash/{hash}', 'loginByHash')->name('login.by-hash');
});
