<?php

if (!function_exists('floatToInt')) {
    function floatToInt($amount): int
    {
        $amount = strval(number_format(floatval(trim($amount)), 2, '.', ''));
        $amount = $amount * 100;

        return $amount;
    }
}

if (!function_exists('intToFloat')) {
    function intToFloat($amount): string
    {
        return number_format(($amount / 100), 2, '.', '');
    }
}

/*
 * If you want this service work, please create account in: https://www.geoplugin.com
 * Register domain.
 * Validate domain.
 */
if (!function_exists('getLocationDataByIp')) {
    function getLocationDataByIp($ip)
    {
        $url = "http://www.geoplugin.net/php.gp?ip=" . $ip;
        $data = unserialize(file_get_contents($url));
        return $data;
    }
}
if (!function_exists('getMainLocationDataByIp')) {
    function getMainLocationDataByIp($ip)
    {
        $fullData = getLocationDataByIp($ip);

        return [
            'continent' => $fullData['geoplugin_continentName'] ?? '',
            'country' => $fullData['geoplugin_countryName'] ?? '',
            'region' => $fullData['geoplugin_regionName'] ?? '',
            'city' => $fullData['geoplugin_city'] ?? '',
            'latitude' => $fullData['geoplugin_latitude'] ?? '',
            'longitude' => $fullData['geoplugin_longitude'] ?? '',
            'radius' => $fullData['geoplugin_locationAccuracyRadius'] ?? '',
        ];
    }
}

// !!! search also for: const convertBgnToEur = (bgnAmount) => {
if (!function_exists('amountEur')) {
    function amountEur(float $amount, string $currencySign = '€'): string
    {
        $amoutNew = number_format(($amount / 1.95583), 2, '.', ' ');

        if (empty($currencySign)) {
            return $amoutNew;
        }

        return $currencySign . $amoutNew;
    }
}

/**
 * Normalize a currency string by removing space‐based thousands separators.
 * Used at slider:
 *
 * @param  string  $input  e.g. " €3 067.75"
 * @return string          e.g. " €3067.75"
 */
if (!function_exists('normalizeCurrency')) {
    function normalizeCurrency(string $input): string
    {
        return preg_replace('/(?<=\d)\s(?=\d)/', '', $input);
    }
}

