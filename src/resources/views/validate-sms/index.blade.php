@extends('layout.master')
@section('content')
    <div class="row mt-lg-5 mb-5">
        <div class="col-12 col-lg-7 mx-lg-auto">
            <div class="d-block shadow-thick rounded-15 py-4">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h1 class="text-center text-middle-regular text-black fw-bold my-0">
                                Изпратихме ти СМС на телефонен номер {{ $phone }}. <br>
                                Въведи кода, за да влезеш в профила си.
                            </h1>

                            <div class="response-message text-center mt-3"></div>

                            <div class="row mt-4">
                                <div class="col-12 col-lg-6 offset-lg-3">
                                    <form action="{{ route('login.sms.post') }}"
                                          method="POST"
                                          class="text-center"
                                          id="code-form"
                                          data-parsley-validate="true"
                                    >
                                        @csrf
                                        <input type="hidden" name="loginApiUrl" value="{{$loginApiUrl}}"/>
                                        <input type="hidden" name="pin" value="{{$pin}}"/>
                                        <input type="text" name="code"
                                               class="form-control text-center"
                                               required="required"
                                               data-parsley-required-message=""
                                        />
                                        <p class="text-danger mt-2 mb-0 visually-hidden">
                                            {{__('Въвели сте изтекъл код!')}}
                                        </p>

                                        <div class="d-block w-100 mt-4">
                                            <button type="submit"
                                                    class="btn btn-primary w-100 mw-250 rounded-pill btn-pink">
                                                {{__('Влез')}}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="d-block text-center mt-4">
                                <a href="#"
                                   class="d-inline-block text-lendivo-secondary p-1 text-decoration-gray js-resend-code">
                                    {{__('Изпрати ми нов СМС код')}}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row my-4">
                <div class="col-12 col-lg-7">
                    <div>
                        <h2 class="text-middle text-black fw-bold my-0">
                            {{__('Не получи СМС код?')}}
                        </h2>
                        <p class="text-dark-gray text-middle mt-2">
                            {{__('Ако телефонният ти номер е сменен и не получаваш СМС код, свържи се с наш оператор на')}}
                            <b>070010514</b>.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript">
        $(document).ready(function () {
            $('form#code-form').submit(function (event) {
                event.preventDefault();

                let $form = $(this);
                let $formData = $form.serialize();
                const submitBtn = $(this).find('button[type="submit"]');

                submitBtn.prop('disabled', true);
                axios
                    .post($form.attr('action'), $formData)
                    .then(resp => {
                        if (resp.data.success === true) {
                            window.location.replace(resp.data.redirectTo);
                            return;
                        }

                        if (resp.data?.error) {
                            let $message = $('<h5 class="text-danger">').text(resp.data.error);
                            $('div.response-message').html('');
                            $('div.response-message').html($message);
                        }

                        if (resp.data?.message) {
                            let $message = $('<h5 class="text-danger">').text(resp.data.message);
                            $('div.response-message').html('');
                            $('div.response-message').html($message);
                        }

                        submitBtn.prop('disabled', false);
                    })
                    .catch(error => {
                        let $message = $('<h5 class="text-danger">').text(error.response.data.message);
                        $('div.response-message').html('');
                        $('div.response-message').html($message);
                        submitBtn.prop('disabled', false);
                    });
            });
        });
    </script>
@endpush
