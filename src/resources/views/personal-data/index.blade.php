@extends('layout.master')
@section('content')
    <div class="row mt-lg-5">
        <div class="col-12 col-lg-6 offset-lg-3">
            <div class="d-block shadow-thick rounded-15 py-4">
                <div class="container">

                    <div class="row">
                        <div class="col-12">
                            <h1 class="fw-semibold fs-20 text-center text-steady-title text-black my-0">
                                {{__('Твоите данни')}}
                            </h1>

                            <x-flash-messages/>

                            <form action="{{ route('personal.data.post') }}" method="POST"
                                  class="mt-3"
                                  data-parsley-validate="true"
                                  onsubmit="disableSubmitButton(this)"
                            >
                                @csrf

                                <input type="hidden" name="request_id" value="{{ $loan_data['request_id'] }}">

                                <label class="w-100 mb-2">
                                    <input class="form-control rounded-4"
                                           type="text"
                                           placeholder="{{__('Имейл')}}"
                                           name="email"
                                           value="{{old('email')}}"
                                           required="required"
                                    />
                                </label>
                                <label class="w-100 mb-2">
                                    <input class="form-control rounded-4 @if($errors->has('pin')) border-danger @endif"
                                           type="text"
                                           placeholder="{{__('ЕГН')}}"
                                           name="pin"
                                           value="{{old('pin')}}"
                                           required="required"
                                           maxlength="10"
                                    />
                                </label>
                                <label class="w-100 mb-2">
                                    <input class="form-control rounded-4"
                                           type="text"
                                           placeholder="{{__('Номер на лична карта')}}"
                                           name="idcard_number"
                                           value="{{old('idcard_number')}}"
                                           required="required"
                                           maxlength="9"
                                           data-parsley-pattern="^[A-Z0-9]+$"
                                           data-parsley-pattern-message="{{__('Номер на лична карта, приема само букви на латиница и цифри')}}"
                                    />
                                </label>
                                <label class="w-100 d-flex justify-content-center align-items-center mt-30px">
                                    @include('partial.payment')
                                </label>

                                <div class="w-100 d-flex justify-content-center">
                                    <button type="submit"
                                            class="btn btn-primary d-block mw-250 w-100 rounded-pill btn-pink mt-2 mb-3">
                                        {{__('Вземи парите')}}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{--    <x-credit-info :creditInfo="$creditInfo" :header="$header"/>--}}

    <div class="d-block mt-4 mb-5">
        <x-document-download :templateType="'sef'"
                             :templateText="'Изтегли СЕФ'"
                             :loanData="$loan_data"
        />
    </div>
@endsection
