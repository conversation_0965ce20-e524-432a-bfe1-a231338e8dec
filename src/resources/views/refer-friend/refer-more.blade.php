@extends('layout.master')
@section('content')
    <div class="row mt-lg-5">
        <div class="col-12 col-lg-6 mx-lg-auto">
            <p class="text-center">Успешно препоръча приятел!</p>

            <h5 class="mt-4 mb-4 text-center fw-bold">Трябват ли ти още пари?</h5>
            <p class="text-center mt-4 mb-4">
                Може да препоръчаш <strong>неограничен</strong> брой приятели - за всеки от тях получаваш 100 лева / €{{amountEur(100, '')}}!
            </p>

            <div class="row text-center mt-3">
                <div class="col-10 mx-auto">

                    <form action="{{route('refer-a-friend')}}" method="POST">
                        @csrf
                        <input type="hidden" name="refer_more" value="1"/>
                        <div id="phone-container">
                            <div class="mb-3 phone-row">
                                <input type="text"
                                       name="phones[]"
                                       class="form-control"
                                       placeholder="Мобилен номер на приятел"
                                       maxlength="10"
                                />
                                <div class="invalid-feedback text-left" style="display: none;">
                                    Този номер вече е добавен.
                                </div>
                            </div>

                            <div class="mb-3 phone-row">
                                <input type="text"
                                       name="phones[]"
                                       class="form-control"
                                       placeholder="Мобилен номер на приятел"
                                       maxlength="10"
                                />
                                <div class="invalid-feedback text-left" style="display: none;">
                                    Този номер вече е добавен.
                                </div>
                            </div>

                            <div class="mb-3 phone-row">
                                <input type="text"
                                       name="phones[]"
                                       class="form-control"
                                       placeholder="Мобилен номер на приятел"
                                       maxlength="10"
                                />
                                <div class="invalid-feedback text-left" style="display: none;">
                                    Този номер вече е добавен.
                                </div>
                            </div>
                        </div>
                        <!-- End ./phone-container -->

                        <div class="col-lg-4 mx-auto">
                            <a href="javascript:void(0);" class="mt-5 add-phone-btn"
                               style="color: #8463C9 !important; text-decoration: none;">
                                <img src="{{asset('/images/refer/add_icon.svg')}}" alt="Add more" height="30"/>
                                Добави приятел
                            </a>
                            <p id="phone-limit-warning mb-5" style="color: red; display: none;">
                                Можете да добавите максимум 10 приятели.
                            </p>

                            <button type="submit" class="btn btn-primary w-100 rounded-pill btn-pink mt-3 mb-3">
                                Препоръчай
                            </button>
                        </div>

                    </form>
                    <div class="mt-5 mb-5">
                        <a href="{{route('referrals.terms')}}"
                           target="_blank"
                           style="color: #8463C9 !important; text-decoration: none;">
                            👉 Прочети пълните условия на програмата.
                        </a>
                    </div>
                </div>
            </div>
            <!-- End ./row -->
        </div>
        <!-- End ./col -->
    </div>
    <!-- End ./row -->

    @push('scripts')
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                const phoneContainer = document.getElementById("phone-container");
                const addPhoneBtn = document.querySelector(".add-phone-btn");

                function clearErrorOnChange(input) {
                    input.addEventListener("input", function () {
                        const errorMessage = input.nextElementSibling;
                        const allInputs = phoneContainer.querySelectorAll("input");
                        let isDuplicate = false;

                        // Check for duplicates
                        allInputs.forEach(otherInput => {
                            if (otherInput !== input && otherInput.value.trim() === input.value.trim()) {
                                isDuplicate = true;
                            }
                        });

                        if (isDuplicate) {
                            errorMessage.style.display = "block";
                            input.classList.add("is-invalid");
                        } else {
                            errorMessage.style.display = "none";
                            input.classList.remove("is-invalid");
                        }
                    });
                }

                addPhoneBtn.addEventListener("click", function () {
                    const allInputs = phoneContainer.querySelectorAll("input");
                    let allFilled = true;
                    let numbers = new Set();

                    allInputs.forEach(input => {
                        const value = input.value.trim();
                        const errorMessage = input.nextElementSibling;

                        // Check for empty input
                        if (value === "") {
                            allFilled = false;
                            input.focus();
                            return;
                        }

                        // Check for duplicate numbers
                        if (numbers.has(value)) {
                            errorMessage.style.display = "block";
                            input.classList.add("is-invalid");
                            input.focus();
                            allFilled = false;
                        } else {
                            errorMessage.style.display = "none";
                            input.classList.remove("is-invalid");
                            numbers.add(value);
                        }
                    });

                    if (allFilled) {
                        const phoneRow = document.querySelector(".phone-row");
                        const newPhoneRow = phoneRow.cloneNode(true);

                        // Clear the input value for the new row
                        const newInput = newPhoneRow.querySelector("input");
                        newInput.value = "";
                        newInput.classList.remove("is-invalid");
                        newPhoneRow.querySelector(".invalid-feedback").style.display = "none";

                        // Append the cloned row to the container
                        phoneContainer.appendChild(newPhoneRow);

                        // Automatically focus on the newly added input
                        newInput.focus();

                        // Attach the change event listener
                        clearErrorOnChange(newInput);
                    }
                });

                // Attach the change event listener to the initial input
                clearErrorOnChange(phoneContainer.querySelector("input"));
            });
        </script>
    @endpush
@endsection
