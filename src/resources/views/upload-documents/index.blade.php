@extends('layout.master')
@section('content')
    <style>
        .file-input {
            display: none;
        }

        .custom-file-label {
            display: inline-block;
            padding: 10px 20px;
            background-color: #e855d8;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .file-name {
            margin-left: 10px;
            font-size: 16px;
        }
    </style>
    <div class="py-5">
        <x-flash-messages/>

        <div class="row">
            <div class="col-lg-6">
                @if(!$success)
                    <form action="{{route('upload-documents.upload',$loanHash)}}"
                          method="POST"
                          enctype="multipart/form-data"
                          onsubmit="disableSubmitButton(this)"
                    >
                        @csrf
                        <div class="form-group">
                            <label class="custom-file-label" for="file">Селфи с лична карта</label>
                            <input type="file" name="selfie" id="file" class="file-input">
                            <span class="file-name">Няма избран файл</span>

                            @push('scripts')
                                <script>
                                    document.getElementById("file").addEventListener("change", function () {
                                        let fileName = this.files.length > 0 ? this.files[0].name : "No file chosen";
                                        document.querySelector(".file-name").textContent = fileName;
                                    });

                                    function disableSubmitButton(form) {
                                        const button = form.querySelector('button[type="submit"]');
                                        if (button) {
                                            button.disabled = true;
                                            button.innerHTML = "Обработва се...";
                                        }
                                    }
                                </script>
                            @endpush
                        </div>
                        <!-- End ./form-group -->

                        <div class="form-group">
                            <button type="submit"
                                    class="btn btn-primary w-100 rounded-pill btn-pink mt-4 mb-3"
                            >
                                {{__('Качи')}}
                            </button>
                        </div>
                        <!-- End ./form-group -->
                    </form>
                @else
                    <h4>Благодарим, документи са качени и чакат обработка.</h4>
                @endif
            </div>
            <!-- End ./col -->
        </div>
        <!-- End ./row -->
    </div>
@endsection
