@extends('layout.master')
@section('content')
    <div class="row mt-5">
        <section class="col-12 col-lg-7 mx-lg-auto">
            <div class="d-block shadow-thick rounded-15 py-4 mb-5">
                <div class="container">
                    <h1 class="text-center text-middle-regular text-black fw-bold my-0">
                        {{__('Изпрати ти ни своя телефон и нашия агент ще се свърже с теб.')}}
                    </h1>
                    <div class="response-message text-center mt-3 mb-2"></div>
                    <div class="row">
                        <div class="col-12 col-lg-8 offset-lg-2">
                            <form action="{{ route('register-phone-request.store') }}"
                                  method="POST"
                                  class="mt-4"
                                  data-parsley-validate="true"
                                  id="register-phone-request"
                            >
                                @csrf
                                <label class="w-100 mb-0">
                                    <input type="text"
                                           class="form-control rounded-4 border-dark"
                                           name="phone"
                                           value="{{old('phone')}}"
                                           placeholder="{{__('Телефон')}}"
                                           required="required"
                                           data-parsley-pattern="^[0-9]*$"
                                           data-parsley-pattern-message="{{__('Телефония номер може да съдържа само цифри')}}"
                                           minlength="10"
                                           maxlength="10"
                                    />
                                    <x-backend-errors name="phone"/>
                                </label>
                                <div class="d-block w-100 mt-4 text-center">
                                    <button type="submit"
                                            class="btn btn-primary w-100 mw-250 rounded-pill btn-pink mt-2 mb-3">
                                        {{__('Заяви обаждането')}}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript">
        $(document).ready(function () {
            $('form#register-phone-request').submit(function (event) {
                event.preventDefault();

                let $form = $(this);
                let $formData = $form.serialize();

                axios
                    .post($(this).attr('action'), $formData)
                    .then(resp => {
                        $('div.response-message').html($('<h5 class="text-success">').text(resp.data.message));

                        $('input[name="phone"]').removeClass('parsley-success');
                        $(this)[0].reset();
                    })
                    .catch(error => {
                        console.error(error);
                    });
            });
        });
    </script>
@endpush
