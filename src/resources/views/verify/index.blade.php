@extends('layout.master')
@section('content')
    <div class="row mt-lg-5 pb-5">
        <div class="col-12 col-lg-6 offset-lg-3">
            <div class="d-block shadow-thick rounded-15 py-4">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h1 class="text-dark-gray text-center text-middle my-0">
                                Прегледай информацията, която имаме за теб. Ако има промяна се свържи с нас на
                                <span class="fw-semibold">070010514</span>
                            </h1>

                            <form class="mt-3 d-flex flex-column justify-content-center align-items-center"
                                  method="POST"
                                  action="{{ route('verify.post') }}"
                                  data-parsley-validate="true"
                            >
                                {{ csrf_field() }}
                                <input type="hidden" name="client_id" value="{{ $client['client_id'] }}">

                                <div class="fw-semibold w-100 fs-16">
                                    Имена
                                    <p type="text"
                                       class="border-0 w-100 border-bottom border-gray fw-normal text-dark-gray fs-14">
                                        {{ $client['first_name'] . ' ' . ($client['middle_name'] ?? '') . ' ' . $client['last_name']  }}
                                    </p>
                                </div>

                                <div class="fw-semibold w-100 fs-16">
                                    ЕГН
                                    <p type="text"
                                       class="border-0 w-100 border-bottom border-gray fw-normal text-dark-gray fs-14">
                                        {{ $client['pin'] }}
                                    </p>
                                </div>

                                <div class="fw-semibold w-100 fs-16">
                                    Номер на лична карта
                                    <p type="text"
                                       class="border-0 w-100 border-bottom border-gray fw-normal text-dark-gray fs-14">
                                        @if($showIdCardNumber)
                                            <input type="text"
                                                   name="idcard_number"
                                                   class="form-control mb-2"
                                                   required="required"
                                                   maxlength="9"
                                                   data-parsley-pattern="^[A-Z0-9]+$"
                                                   data-parsley-pattern-message="{{__('Номер на лична карта, приема само букви на латиница и цифри')}}"
                                            />
                                        @else
                                            {{ $client['idcard_number'] }}
                                        @endif
                                    </p>
                                </div>

                                <div class="fw-semibold w-100 fs-16">
                                    Валидност на лична карта
                                    <p type="text"
                                       class="border-0 w-100 border-bottom border-gray fw-normal text-dark-gray fs-14">
                                        {{ \Carbon\Carbon::parse($client['idcard']['valid_date'])->format('d.m.Y') }}
                                    </p>
                                </div>

                                <div class="fw-semibold w-100 fs-16">
                                    Имейл адрес
                                    <p type="text"
                                       class="border-0 w-100 border-bottom border-gray fw-normal text-dark-gray fs-14">
                                        @if(empty($client['email']))
                                            <input type="email"
                                                   name="email"
                                                   class="form-control mb-2"
                                                   required="required"
                                                   maxlength="40"
                                            />
                                        @else
                                            {{ $client['email'] }}
                                        @endif
                                    </p>
                                </div>

                                <div class="fw-semibold w-100 fs-16">
                                    Адрес по лична карта
                                    <p type="text"
                                       class="border-0 w-100 border-bottom border-gray fw-normal text-dark-gray fs-14">
                                        {{ $client['idcard']['address'] }}
                                    </p>
                                </div>

                                <div class="d-block w-100 text-center mt-2">
                                    <button type="submit" class="btn btn-primary w-100 mw-250 rounded-pill btn-pink">
                                        {{__('Данните ми са верни')}}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
