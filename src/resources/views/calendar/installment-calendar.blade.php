@extends('layout.master')
@section('content')
    <div id="workaround"></div>
    <div class="row pb-5">
        <div class="col-12 col-md-6 offset-md-3 col-lg-3 offset-lg-4">
            <h1 class="text-black text-steady-title text-center fw-bold my-0">
                {{__('Календар на вноски')}}
            </h1>

            <div id="calendar" class="calendar js-installments-calendar shadow-thick mt-4 rounded-4"></div>

            <div class="d-block mt-4">
                <p class="text-lendivo-primary text-center visually-hidden fw-semibold">
                    {{__('Предстояща вноска:')}} <span id="current-installment-amount"></span> {{__('лв.')}} / €<span id="current-installment-amount-eur"></span>
                </p>

                <p class="bg-transparent border text-dark border-gray rounded-4 mw-136 pe-3 ps-3 pb-2 pt-2 border-2 fw-semibold mx-auto text-center visually-hidden">
                    <span id="current-installment-date"></span>
                </p>

                <div class="w-100 d-flex justify-content-center mt-4">
                    <a href="{{ route('early.repayment') }}" class="btn btn-primary w-100 mw-250 rounded-pill btn-pink">
                        {{__('Виж сума при предсрочно погасяване')}}
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script>
        window.calendarArrows = {
            left: '{{ asset("static/images/left.svg") }}',
            right: '{{ asset("static/images/right.svg") }}'
        };

        document.querySelector('#workaround').parentNode.classList.remove('shadow-thick');

        !function () {
            new Calendar(
                '#calendar',
                    {{ \Illuminate\Support\Js::from($loan_data['installments']) }},
                'DD/MM/YYYY'
            );
        }();
    </script>
@endpush
