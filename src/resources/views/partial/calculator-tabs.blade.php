<!-- Calculator Tabs -->
<ul class="nav nav-tabs justify-content-between nav-justified">
    @if(isset($products))
        @foreach($products as $productId => $productData)
            <li class="nav-item">
                <a class="nav-link nav-tab-rounded @if($loop->first) active @endif"
                   data-bs-toggle="tab"
                   data-bs-target="#product-{{ $productId }}"
                   href="#product-{{ $productId }}"
                >
                    @if($loop->first)
                        <div class="mb-0 fw-bold fs-6 text-dark">{{ $productData['trade_name'] }}</div>
                    @else
                        <div class="mb-0 fw-bold fs-6 text-dark">{{ $productData['trade_name'] }}</div>
                    @endif

                    <small class="text-dark">{{$productData['description']}}</small>
                </a>
            </li>
        @endforeach
    @endif
</ul>
<div class="tab-content">
    @php
        $isLoggedIn = !empty(Auth::user()?->get('client_id'));
    @endphp
    @if(isset($products))
        @foreach($products as $productId => $productData)
            @if(!$isLoggedIn && $productData['productGroup'] == 'payday')
                <div class="tab-pane calculator-layout container nav-content-rounded @if($loop->first) active @endif"
                     id="product-{{ $productId }}">
                    @include('home.slider-tabs.slider-salary-day')
                </div>
            @else
                <div class="tab-pane calculator-layout container nav-content-rounded @if($loop->first) active @endif"
                     id="product-{{ $productId }}">
                    @include('home.slider-tabs.slider-regular')
                </div>
            @endif
        @endforeach
    @endif
</div>

@push('scripts')
    <script type="text/javascript">
        $(() => {
            $('form#new-app-form').submit(function () {
                $(this).find(":submit").prop("disabled", true);
            });
        });
    </script>
@endpush
