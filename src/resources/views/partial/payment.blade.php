<div class="w-100 text-center mx-auto">
    <h2 class="my-0 fw-bold text-steady-title text-black">
        {{__('Искам да получа парите по:')}}
    </h2>
    <div class="payment-method-container d-block mt-3 mx-auto">
        @foreach($payment_methods as $paymentMethodId => $paymentMethodName)
            <div class="form-check text-left text-steady text-dark-gray p-0 pb-3">
                <input class="payment-method-checkbox js-payment-method check-{{ $paymentMethodId }}"
                       type="radio"
                       name="payment_method_id"
                       value="{{ $paymentMethodId }}"
                       id="check-{{ $paymentMethodId }}"
                       @if(empty($payment_method_id))
                           @checked($loop->first)
                       @else
                           @checked($payment_method_id == $paymentMethodId)
                       @endif
                />

                <label class="form-check-label payment-method-label" for="check-{{ $paymentMethodId }}">
                    <img src="{{ asset('static/images/' . mb_strtolower($paymentMethodName) . '.png') }}"
                         class="d-inline-block ms-2"
                    />
                    <span class="ms-2">{{ __('translations.' . $paymentMethodName) }}</span>
                </label>

                @if (strtolower($paymentMethodName) === 'bank')
                    <label class="ps-4 mt-2 js-iban-container">
                        <input class="form-control rounded-4"
                               type="text"
                               placeholder="IBAN"
                               id="iban"
                               name="iban"
                               value="{{ old('iban', $iban ?? '') }}"
                               data-default-value="{{ (!empty($iban) ? $iban : '0000000000000000000000') }}"
                               data-parsley-minlength="16"
                               data-parsley-maxlength="34"
                               maxlength="34"
                        />
                    </label>
                @endif
            </div>
        @endforeach
    </div>
</div>

@push('scripts')
    <script type="text/javascript">
        $(document).ready(function () {
            @if(empty($iban))
                $('#check-1').prop('checked', false);
                $('#check-2').prop('checked', true);
            @endif

            function toggleIbanVisibility() {

                let tmpVal = $('input[name="payment_method_id"]:checked').val();

                // Check if the payment method with value "1" (Банков път) is selected
                if (tmpVal === "1") {
                    // Show the IBAN input
                    $('input[name="iban"]').removeClass('d-none');
                    $('input[name="iban"]').attr('required', 'required');
                    $('#check-1').prop('checked', true);
                    $('#check-2').prop('checked', false);
                } else {
                    // Hide the IBAN input
                    $('input[name="iban"]').addClass('d-none');
                    $('input[name="iban"]').removeAttr('required');
                    $('#check-1').prop('checked', false);
                    $('#check-2').prop('checked', true);
                }
            }

            // Initial check in case the selected payment method is pre-selected on page load
            toggleIbanVisibility();

            // Event listener for changes on any payment method radio button
            $('.js-payment-method').on('change', function () {
                // Get the value of the selected input
                let inputValue = $(this).val();
                // Construct the ID of the related input if needed
                let inputId = 'check-' + inputValue;

                // Check if an element with the constructed ID exists
                if ($('.' + inputId).length > 0) {
                    // If you're dealing with checkboxes or need to force radio behavior:
                    // Uncheck all .js-payment-method inputs
                    $('.js-payment-method').prop('checked', false);
                    // Check the selected one
                    $('.' + inputId).prop('checked', true);
                }

                toggleIbanVisibility();
            });
        });
    </script>
@endpush
