@extends('layout.master')
@section('content')
    <div class="mb-5 row row-cols-1 row-cols-lg-2 g-3">
        @foreach($posts['data'] as $post)

            <div>
                <div class="shadow-light p-2 rounded h-100">
                    @if(isset($post['postImage']) && $post['postImage'])
                        <img src="{{$post['postImage']}}"
                             alt="primer-bulgarski-iban"
                             class="img-fluid"
                        />
                    @endif
                    <h2 class="mt-4 h5 fw-bold">{{$post['title']}}</h2>
                    <div>
                        {!! $post['small_description'] !!}
                    </div>
                    <p class="read-more mt-3">
                        <a href="{{route('blog.show', $post['slug'])}}">{{__('Read More »')}}</a>
                    </p>
                </div>
            </div>
        @endforeach
    </div>
    {!! $posts['links'] ?? '' !!}
@endsection
