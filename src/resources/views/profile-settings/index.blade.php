@extends('layout.master')
@section('content')
    <div class="row pb-5 js-profile-settings">
        <div class="col-12 col-lg-6 offset-lg-3">
            <x-flash-messages/>

            <h1 class="text-black fw-bold text-center text-steady-title my-0">
                {{__('Клиентси настройки')}}
            </h1>

            <form action="{{route('profileSettingsUpdate')}}" method="POST">
                @csrf

                <div class="d-block mt-5">
                    <div class="border-bottom border-gray text-dark-gray w-100 d-flex justify-content-between align-items-start pb-2">
                        <div class="d-inline-block flex-grow-1 text-left">
                            <div class="text-black text-middle d-block">
                                {{__('Имейл адрес')}}
                            </div>
                            <div class="text-dark-gray text-default d-block" id="email_addrr_val">
                                {{ $email }}
                            </div>
                        </div>

                        <div class="d-inline-block flex-grow-1">
                            <a href="#"
                               class="btn btn-purple-outline d-block text-steady-middle"
                               data-bs-toggle="modal"
                               data-bs-target="#update-email-modal"
                            >
                                {{__('Промени')}}
                            </a>
                        </div>
                    </div>
                    <!-- End ./border-bottom -->

                    <div class="border-bottom border-gray text-dark-gray w-100 d-flex justify-content-between align-items-start pb-2 mt-3">
                        <div class="d-inline-block flex-grow-1 text-left">
                            <div class="text-black text-middle d-block">
                                {{__('Имена')}}
                            </div>
                            <div class="text-dark-gray text-default d-block">
                                {{ $first_name }} {{ $middle_name }} {{ $last_name }}
                            </div>
                        </div>
                    </div>
                    <!-- End ./border-bottom -->

                    <div class="border-bottom border-gray text-dark-gray w-100 d-flex justify-content-between align-items-start pb-2 mt-3">
                        <div class="d-inline-block flex-grow-1 text-left">
                            <div class="text-black text-middle d-block">
                                {{__('ЕГН')}}
                            </div>
                            <div class="text-dark-gray text-default d-block">
                                {{ $pin }}
                            </div>
                        </div>
                    </div>
                    <!-- End ./border-bottom -->

                    <div class="border-bottom border-gray text-dark-gray w-100 d-flex justify-content-between align-items-start pb-2 mt-3">
                        <div class="d-inline-block flex-grow-1 text-left">
                            <div class="text-black text-middle d-block">
                                {{__('Номер на лична карта')}}
                            </div>
                            <div class="text-dark-gray text-default d-block">
                                {{ $idcard_number }}
                            </div>
                        </div>
                    </div>
                    <!-- End ./border-bottom -->

                    <div class="border-bottom border-gray text-dark-gray w-100 d-flex justify-content-between align-items-start pb-2 mt-3">
                        <div class="d-inline-block flex-grow-1 text-left">
                            <div class="text-black text-middle d-block">
                                {{__('Валидност на лична карта')}}
                            </div>
                            <div class="text-dark-gray text-default d-block">
                                {{ \Carbon\Carbon::parse($idcard['valid_date'])->format('d.m.Y') }}
                            </div>
                        </div>
                    </div>
                    <!-- End ./border-bottom -->

                    <div class="border-bottom border-gray text-dark-gray w-100 d-flex justify-content-between align-items-start pb-2 mt-3">
                        <div class="d-inline-block flex-grow-1 text-left">
                            <div class="text-black text-middle d-block">
                                {{__('Адрес по лична карта')}}
                            </div>
                            <div class="text-dark-gray text-default d-block">
                                {{ $idcard['address'] }}
                            </div>
                        </div>
                    </div>
                    <!-- End ./border-bottom -->

                    <div class="d-block mt-5 pb-5">
                        <p class="text-default text-dark text-lef m-0 mb-1">
                            Съгласен съм да получавам известия за отстъпките,
                            промоциите и продуктите на Stikcredit.bg чрез:
                        </p>
                        <div class="text-default lendivo-notifications js-lendivo-notifications">

                            <div class="lendivo-notification border-bottom all js-lendivo-notification">
                                <label for="all">
                                    <input type="hidden" name="notifications[all]" value="no"/>
                                    <input type="checkbox"
                                           id="all"
                                           name="notifications[all]"
                                           value="yes"
                                    />
                                    <span>{{__('Всички')}}</span>
                                </label>
                            </div>
                            <!-- End ./lendivo-notification -->

                            <div class="lendivo-notification">
                                <label for="call">
                                    <input type="hidden" name="notifications[call]" value="no"/>
                                    <input type="checkbox"
                                           id="call"
                                           name="notifications[call]"
                                           value="yes"
                                           class="checkbox"
                                            @checked((isset($notifications) && isset($notifications['marketing']['call']['value']) && $notifications['marketing']['call']['value']) ?? false)
                                    />
                                    <span>{{__('Телефонно обаждане')}}</span>
                                </label>
                            </div>
                            <!-- End ./lendivo-notification -->

                            <div class="lendivo-notification">
                                <label for="sms">
                                    <input type="hidden" name="notifications[sms]" value="no"/>
                                    <input type="checkbox"
                                           id="sms"
                                           name="notifications[sms]"
                                           value="yes"
                                           class="checkbox"
                                            @checked((isset($notifications) && isset($notifications['marketing']['sms']['value']) && $notifications['marketing']['sms']['value']) ?? false)
                                    />
                                    <span>{{__('SMS')}}</span>
                                </label>
                            </div>
                            <!-- End ./lendivo-notification -->

                            <div class="lendivo-notification">
                                <label for="email">
                                    <input type="hidden" name="notifications[email]" value="no"/>
                                    <input type="checkbox"
                                           id="email"
                                           name="notifications[email]"
                                           value="yes"
                                           class="checkbox"
                                            @checked((isset($notifications) && isset($notifications['marketing']['email']['value']) && $notifications['marketing']['email']['value']) ?? false)
                                    />
                                    <span>{{__('Имейл')}}</span>
                                </label>
                            </div>
                            <!-- End ./lendivo-notification -->

                            <div class="lendivo-notification">
                                <label for="viber">
                                    <input type="hidden" name="notifications[viber]" value="no"/>
                                    <input type="checkbox"
                                           id="viber"
                                           name="notifications[viber]"
                                           value="yes"
                                           class="checkbox"
                                            @checked((isset($notifications) && isset($notifications['marketing']['viber']['value']) && $notifications['marketing']['viber']['value']) ?? false)
                                    />
                                    <span>{{__('Вайбър')}}</span>
                                </label>
                            </div>
                            <!-- End ./lendivo-notification -->

                        </div>
                        <!-- End ./text-default -->

                    </div>
                    <!-- End ./d-block -->
                </div>
                <!-- End ./d-block -->
            </form>
        </div>
        <!-- End ./col -->
    </div>
    <!-- End ./row -->
@endsection
@push('scripts')
    <script>
        $(function () {
            $('input[name="notifications[all]"]').click(function () {
                if ($(this).is(':checked')) {
                    $('input.checkbox').prop('checked', true);
                } else {
                    $('input.checkbox').prop('checked', false);
                }

                let $reqRoute = $(this).parents('form').first().attr('action');
                let $formData = $(this).parents('form').first().serialize();
                axios
                    .post($reqRoute, $formData)
                    .then(resp => {
                        console.log(resp);
                    })
            });

            let $allBoxes = $('input.checkbox').length;
            let $checkedCount = $('input.checkbox:checked').length;
            if ($checkedCount === $allBoxes) {
                $('input[name="notifications[all]"]').prop('checked', true);
            }

            $('input.checkbox').click(function () {
                $allBoxes = $('input.checkbox').length;
                $checkedCount = $('input.checkbox:checked').length;

                if ($checkedCount < $allBoxes) {
                    $('input[name="notifications[all]"]').prop('checked', false);
                }

                if ($checkedCount === $allBoxes) {
                    $('input[name="notifications[all]"]').prop('checked', true);
                }

                //// submit form
                let $reqRoute = $(this).parents('form').first().attr('action');
                let $formData = $(this).parents('form').first().serialize();
                axios
                    .post($reqRoute, $formData)
                    .then(resp => {
                        console.log(resp);
                    })
            });
        })
    </script>
@endpush

<div class="modal modal-lg fade" id="update-email-modal"
     tabindex="-1"
     aria-hidden="true"
>
    <div class="modal-dialog">
        <div class="modal-content rounded-30">
            <div class="modal-body">
                <h5 class="text-center mb-4">{{__('Промени своя имейл адрес')}}</h5>
                <form action="{{route('profileSettingsUpdate')}}" method="POST" data-parsley-validate="true">
                    @csrf

                    <input type="email"
                           name="email"
                           class="form-control"
                           required="required"
                           placeholder="{{__('Имейл')}}"
                    />

                    <div class="d-block m-100 mt-4 text-center">
                        <button type="submit"
                                class="btn w-100 mw-250 text-white mx-auto rounded-pill btn-pink">
                            {{__('Запази')}}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

