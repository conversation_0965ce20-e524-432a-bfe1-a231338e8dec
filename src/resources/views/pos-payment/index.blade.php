@extends('layout.master')
@section('content')
    <div class="page-container pb-5 pt-5">
        <div class="row">
            <div class="col-lg-12 mb-5">
                <h3 class="text-center">Плащане с карта</h3>
                @if(!empty($error))
                    <ul class="parsley-errors-list filled">
                        <li class="mb-2" style="text-align: center;">{{ $error }}</li>
                    </ul>
                @endif
            </div>
            <div class="col-lg-4 offset-lg-2">
                <form action="{{route('pos-payment.store')}}" method="POST" data-parsley-validate="true">
                    @csrf

                    <div class="form-group mb-3">
                        <label for="pin">ЕГН</label>
                        <input type="text" id="pin" name="pin" maxlength="10" minlength="10" class="form-control"
                               required="required"
                               placeholder="Пример: 6708121004"
                               value="{{$user?->get('pin') ?? ''}}"
                               data-parsley-type="number"
                               data-parsley-type-message="Полето може да съдържа само цифри"
                        />
                    </div>
                    <!-- End ./form-group -->

                    <div class="form-group mb-3">
                        <label for="name">Имена на картодържател (на латиница)</label>
                        <input type="text" pattern="[A-Za-z\s\-]*" id="name" name="name" class="form-control"
                               required="required"
                               placeholder="Пример: Maria Milkova"
                               value="{{$user?->fullNameLatin() ?? ''}}"
                        />
                    </div>
                    <!-- End ./form-group -->

                    <div class="form-group mb-3">
                        <label for="amount">Сума</label>
                        <input type="text" id="amount" name="amount" class="form-control" required="required"
                               placeholder="Пример: 420"
                               data-parsley-type="number"
                               data-parsley-type-message="Полето може да съдържа само цифри"
                               maxlength="7"
                        />
                    </div>
                    <!-- End ./form-group -->

                    <div class="form-group mb-3">
                        <label for="amount">Телефон</label>
                        <input type="text" id="phone" name="phone" class="form-control" required="required"
                               placeholder="Пример: 0896851123"
                               value="{{$user?->get('phone') ?? ''}}"
                               data-parsley-type="number"
                               data-parsley-type-message="Полето може да съдържа само цифри"
                               maxlength="15"
                        />
                    </div>
                    <!-- End ./form-group -->

                    <!--
                    <div class="form-group mb-3">
                        <label for="description">Емейл адрес</label>
                        <input type="text" id="email" name="email" class="form-control"/>
                    </div>
                    -->
                    <!-- End ./form-group -->

                    <div class="form-group mb-3">
                        <label for="description">Описание</label>
                        <input type="text" id="description" name="description" class="form-control"
                               placeholder="Пример: плащане на вноска по кредит #100012"
                               @if(isset($user?->get('loan_data')['loan_id']))
                                   value="{{'Кредит '.$user?->get('loan_data')['loan_id']}}"
                               @endif
                        />
                    </div>
                    <!-- End ./form-group -->

                    <div class="form-group mb-3">
                        <button type="submit" class="btn btn-pink text-center px-4 w-100">Плати</button>
                    </div>
                    <!-- End ./form-group -->

                    <div class="form-group mb-3">
                        Свържете се с нас, ако правите предсрочно погасяване или удължавате кредита.
                    </div>
                    <!-- End ./form-group -->

                </form>
            </div>
            <div class="col-lg-3">
                <div class="form-group mb-3">
                    <h5>Плати със следните карти:</h5>
                </div>

                <img src="/static/images/credit-cards.png" class="img-fluid mb-3"/>

                <div class="form-group">
                    Ако картата няма 3-цифрен CVC код въведи 000 или 999.
                </div>

            </div>
        </div>
    </div>
    <!-- End ./page-container -->
@endsection
