@extends('layout.master')
@section('content')
    <div class="row">
        <div class="col-12">
            <h1 class="text-black text-steady-title text-center fw-bold my-0">
                {{__('Предсрочно погасяване')}}
            </h1>
            <p class="text-dark-gray text-steady-middle text-center my-0 py-3">
                {{__('Избери датата, към която да се изчисли дължимата сума.')}}
            </p>
        </div>

        <div class="d-grid justify-content-center">
            <div class="inline-datepicker mb-4"></div>
        </div>
    </div>
    @push('scripts')
        <script>
            $(function () {
                $.fn.datepicker.dates['bg'] = {
                    days: ["Неделя", "Понеделник", "Вторник", "Сряда", "Четвъртак", "Петък", "Събота"],
                    daysShort: ["Н", "П", "В", "С", "Ч", "П", "С"],
                    daysMin: ["Н", "П", "В", "С", "Ч", "П", "С"],
                    months: ["Януари", "Февруари", "Март", "Април", "Май", "Юни", "Юли", "Август", "Септември", "Октомври", "Ноември", "Декември"],
                    monthsShort: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                    today: "Днес",
                    clear: "Изчисти",
                    format: "mm/dd/yyyy",
                    titleFormat: "MM yyyy", /* Leverages same syntax as 'format' */
                };

                let $installments = {{ \Illuminate\Support\Js::from($loan_data['installments']) }};
                let $installmentDates = [];
                $installments.forEach(function ($row) {
                    $installmentDates.push($row.due_date);
                });

                $('div.inline-datepicker').datepicker({
                    todayHighlight: true,
                    weekStart: 1,
                    language: 'bg',
                    maxViewMode: 0,
                    startDate: '{{$loan_data['activated_at']}}',
                    endDate: '{{$last_installment_date}}',
                    beforeShowDay: function (date) {
                        let dmy = moment(date).format('DD-MM-YYYY');
                        if ($installmentDates.indexOf(dmy) !== -1) {
                            return {
                                classes: 'installment-day',
                                content: '<span>' + date.getDate() + '</span>'
                            };
                        }
                        return {
                            content: '<span>' + date.getDate() + '</span>'
                        };
                    }
                });

                let $repaymentRoute = '{{ route('earlyRepaymentData', $loan_data['loan_id']) }}';

                $('div.inline-datepicker').on("changeDate", function (event) {
                    let selectedDate = moment(event.date).format('YYYY-MM-DD');
                    axios
                        .get($repaymentRoute, {
                            params: {
                                date: selectedDate
                            }
                        })
                        .then(resp => {
                            $('#current-installment-date').text(moment(event.date).format('DD.MM.YYYY'));
                            $('#current-installment-amount').text(resp.data.data.total);
                            $('.principal-value').text(resp.data.data.principal);
                            $('.interest-value').text(resp.data.data.interests);
                            $('.other-fee-value').text(resp.data.data.taxes);

                            $('#current-installment-amount-eur').text(resp.data.data.total_eur);
                            $('.principal-value-eur').text(resp.data.data.principal_eur);
                            $('.interest-value-eur').text(resp.data.data.interests_eur);
                            $('.other-fee-value-eur').text(resp.data.data.taxes_eur);
                        })
                        .catch(error => {
                            console.error(error);
                        });
                });

                axios
                    .get($repaymentRoute, {
                        params: {
                            date: '{{$lastInstallmentDate}}'
                        }
                    })
                    .then(resp => {
                        $('#current-installment-amount').text(resp.data.data.total);
                        $('.principal-value').text(resp.data.data.principal);
                        $('.interest-value').text(resp.data.data.interests);
                        $('.other-fee-value').text(resp.data.data.taxes);

                        $('#current-installment-amount-eur').text(resp.data.data.total_eur);
                        $('.principal-value-eur').text(resp.data.data.principal_eur);
                        $('.interest-value-eur').text(resp.data.data.interests_eur);
                        $('.other-fee-value-eur').text(resp.data.data.taxes_eur);
                    })
                    .catch(error => {
                        console.error(error);
                    });
            });
        </script>
    @endpush

    <div class="col-12 col-md-8 offset-md-2 col-lg-6 offset-lg-3">
        <p class="text-lendivo-primary fw-semibold m-1 text-center">
            {{__('Към')}}
            <span id="current-installment-date">
                {{\Illuminate\Support\Carbon::parse($lastInstallmentDate)->format('d.m.Y')}}
            </span>
        </p>

        <p class="text-lendivo-primary fw-semibold text-center">
            {{__('Общо дължимо:')}}
            <span id="current-installment-amount">0.00</span> лв. / €<span id="current-installment-amount-eur">0.00</span>
        </p>

        <div class="w-100 mt-2" id="early-repayment-stats">
    <div class="border-bottom border-gray text-dark-gray">
        <div class="py-2 w-100 d-flex align-items-center">
            <span class="fw-semibold col-4">{{ __('Главница') }}</span>
            <span class="col-4 text-end">
                <span class="principal-value">0.00</span> лв.
            </span>
            <span class="col-4 text-end">
                €<span class="principal-value-eur">0.00</span>
            </span>
        </div>
    </div>

    <div class="border-bottom border-gray text-dark-gray">
        <div class="py-2 w-100 d-flex align-items-center">
            <span class="fw-semibold col-4">{{ __('Лихви') }}</span>
            <span class="col-4 text-end">
                <span class="interest-value">0.00</span> лв.
            </span>
            <span class="col-4 text-end">
                €<span class="interest-value-eur">0.00</span>
            </span>
        </div>
    </div>

    <div class="border-bottom border-gray text-dark-gray mb-3">
        <div class="py-2 w-100 d-flex align-items-center">
            <span class="fw-semibold col-4">{{ __('Други такси') }}</span>
            <span class="col-4 text-end">
                <span class="other-fee-value">0.00</span> лв.
            </span>
            <span class="col-4 text-end">
                €<span class="other-fee-value-eur">0.00</span>
            </span>
        </div>
    </div>
</div>

        <div class="d-block w-100 text-center mt-4 mb-5">
            <a href="{{ route('how-to-pay') }}" class="btn btn-primary w-100 mw-250 rounded-pill btn-pink">
                {{__('Как да платя?')}}
            </a>
        </div>
    </div>
    </div>
@endsection
