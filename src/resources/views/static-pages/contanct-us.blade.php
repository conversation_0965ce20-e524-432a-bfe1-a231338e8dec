@extends('layout.master')
@section('content')
    <div class="row mt-lg-5">
        <div class="col-lg-6">
            <h1 class="mb-3">Контакти</h1>
            <div>
                <p class="mb-1">
                    Телефон: <strong>070010514</strong>
                </p>
                <p class="mb-1">
                    Мобилен: <strong>0876339520</strong>
                </p>
                <p class="mb-1">
                    Имейл: <strong><EMAIL></strong>
                </p>

                <p class="mb-1 mt-4">
                    Адрес:
                    <strong>гр. Шумен, пл. Оборище 13Б</strong>
                </p>

                <p class="mt-4 mb-1">
                    Работно време: <br>
                    <strong>Понеделник - Петък:<br> от 08:00 до 20:00 часа</strong><br><br>
                    <strong>Събота и Неделя:<br> от 09:00 до 18:00 часа</strong><br><br>
                </p>
            </div>

            <div id="centrala-map" style="height: 400px;"></div>

        </div>
        <!-- End ./col-lg-6 -->

        <div class="col-lg-6">

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show p-2" role="alert">
                    <strong class="alert-heading">{{__('Успех !')}}</strong>
                    <p class="mb-0">{{session('success')}}</p>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif
            @if(session('fail'))
                <div class="alert alert-danger alert-dismissible fade show p-2" role="alert">
                    <strong class="alert-heading">{{__('Възникна проблем.')}}</strong>
                    <p class="mb-0">{{session('fail')}}</p>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif


                <h2 class="mb-3">Изпрати ни запитване</h2>
            <form action="{{route('sendContactForm')}}" method="POST">
                @csrf
                <div class="form-group mb-2">
                    <label for="name">Име</label>
                    <input type="text" class="form-control" name="name" id="name" required="required"/>
                </div>
                <!-- End ./form-group -->

                <div class="form-group mb-2">
                    <label for="phone">Телефон</label>
                    <input type="text" class="form-control" name="phone" id="phone" required="required"/>
                </div>
                <!-- End ./form-group -->

                <div class="form-group mb-2">
                    <label for="email">Email</label>
                    <input type="text" class="form-control" name="email" id="email" required="required"/>
                </div>
                <!-- End ./form-group -->

                <div class="form-group mb-2">
                    <label for="message">Съобщение</label>
                    <textarea name="message" class="form-control" id="message" required="required" rows="5"></textarea>
                </div>
                <!-- End ./form-group -->

                <div class="form-group mb-4">
                    <button type="submit" class="btn btn-lg btn-secondary no-radius">
                        ИЗПРАТИ
                    </button>
                </div>

                <div class="form-group mb-2">
                    За решаване на потребителски спорове, жалби и сигнали. Свържете се с нас или съответните органи.
                    <br><br>

                    <strong>Сайт на Комисия за защита на потребителите:</strong><br>
                    https://kzp.bg
                    <br><br>

                    <strong>Сайт на платформата OPC:</strong><br>
                    https://ec.europa.eu/consumers/odr/main/index.cfm?event=main.home2.show&lng=BG
                </div>
            </form>
        </div>
        <!-- End ./col-lg-6 -->
    </div>
    <!-- End ./row -->
@endsection

@section('outside-container')
    <div class="container-fluid mb-5 p-0" style="min-height: 100px;background-color: #f5f9fd">
        <div class="container mt-5">
            <div class="row">
                <div class="col-lg-4 text-lg-end text-sm-center p-4">
                    <h2>
                        КЪДЕ ДА ОТКРИЕТЕ <br> НАШ ОФИС?
                    </h2>
                </div>

                <div class="col-lg-8 p-4">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="select w-100 w-xs-auto pl-0 pl-xs-1 pt-2 pt-xs-0">
                                <label for="offices-city-select" class="label">Изберете населено място </label>
                                <select id="offices-city-select" class="city-select form-control" name="city">
                                    @foreach($cities as $city)
                                        <option value="{{$city['citySlug']}}">{{$city['cityName']}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="select w-100 w-xs-auto pl-0 pl-xs-1 pt-2 pt-xs-0">
                                <label for="offices-office-select" class="label">Изберете офис </label>
                                <select id="offices-office-select" class="offices-select form-control">
                                    <option selected="" disabled="" hidden="">Oфис</option>
                                </select>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End ./row -->
        </div>
        <!-- End ./container -->

        <div id="map" style="height: 600px !important;"></div>
    </div>
@endsection

@push('scripts')
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB-QZZ8WLI1qBfyLj0y9V1AwxXJOD9UKwo&callback=initMap"
            async defer></script>
    <script>
        let offices = @json($offices);
        markersData = {};
        markerContent = {};

        function initMap() {
            bounds = new google.maps.LatLngBounds();
            coordinates = {lat: 42.696702, lng: 23.322041};
            map = new google.maps.Map(document.getElementById('map'), {
                center: coordinates,
                zoom: 15
            });


            ////////////////////////////////////////////////////////////////////////////////////////////////////////////
            contentString7 = '<div class="map-info-window-holder">' +
                '<h5>Стик Кредит Шумен - Цар Освободител </h5>' +
                '<div class="strong">Телефон:</div>' +
                '<div class="strong mb20">0894 35 62 36</div>' +
                '<div class="strong">Работно време:</div>' +
                '<div><p>Понеделник - Петък от 9:00 до 18:30 часа<br />Събота от 9:00 до 18:30 часа</p></div>' +
                '<div class="d-flex align-items">' +
                '<a href="https:&#47;&#47;www.google.com/maps/dir&#47;&#47;43.27220060, 26.92148740/@ 43.27220060, 26.92148740" title="Виж в Google maps" target="_blank" rel="nofollow" class="infowindow-link">Виж в Google maps</a>' +
                '</div>' +
                '</div>';

            let infoWindowCentrala = new google.maps.InfoWindow({
                content: contentString7,
                maxWidth: 300
            });

            centralaMapBounds = new google.maps.LatLngBounds();
            centralaMap = new google.maps.Map(document.getElementById('centrala-map'), {
                center: {lat: 43.27220060, lng: 26.92148740},
                zoom: 15
            });

            // Create a marker and set its position
            let markerCentrala = new google.maps.Marker({
                map: centralaMap,
                position: {lat: 43.27220060, lng: 26.92148740},
                title: 'Стик Кредит Шумен - Цар Освободител ',
                animation: google.maps.Animation.DROP,
                icon: {
                    url: 'https://stikcredit.bg/assets/images/markers.png',
                    size: new google.maps.Size(55, 76),
                    origin: new google.maps.Point(0, 0)
                }
            });
            markerCentrala.addListener('click', function () {
                infoWindowCentrala.open(centralaMap, markerCentrala);
            });
            ////////////////////////////////////////////////////////////////////////////////////////////////////////////

            @foreach($offices as $office)
                markerContent[{{$office['officeId']}}] = '<div class="map-info-window-holder">' +
                '<h5>{!! $office['officeName'] !!}</h5>' +
                '<div class="strong">Телефон:</div>' +
                '<div class="strong mb20">{!! $office['phone'] !!}</div>' +
                '<div class="strong">Работно време:</div>' +
                '<div>{!! $office['working_time'] !!} </div>' +
                '<div class="d-flex align-items">' +
                '<a href="https://www.google.com/maps/dir//{{$office['lat']}},+{{$office['lng']}}/@ {{$office['lat']}},{{$office['lng']}}" title="Виж в Google maps" target="_blank" rel="nofollow" class="infowindow-link">Виж в Google maps</a>' +
                '</div>' +
                '</div>';

            markersData[{{$office['officeId']}}] = {};
            markersData[{{$office['officeId']}}]['infoWindow'] = new google.maps.InfoWindow({
                content: markerContent[{{$office['officeId']}}],
                maxWidth: 400
            });

            markersData[{{$office['officeId']}}]['marker'] = new google.maps.Marker({
                position: {lat: {{$office['lat'] ?? 43.27220060}}, lng: {{$office['lng'] ?? 26.92148740}}},
                map: map,
                title: '{{$office['officeName']}}',
                animation: google.maps.Animation.DROP,
                icon: {
                    url: 'https://stikcredit.bg/assets/images/markers.png',
                    size: new google.maps.Size(55, 76),
                    origin: new google.maps.Point(0, 0)
                }
            });
            bounds.extend({lat: {{$office['lat'] ?? 43.27220060}}, lng: {{$office['lng'] ?? 26.92148740}}});

            @endforeach

            /// add events
            Object.values(markersData).forEach(function (row) {
                row['marker'].addListener('click', function () {
                    row['infoWindow'].open(map, row['marker']);
                });
            });

            google.maps.event.addListener(map, 'zoom_changed', function () {
                zoomChangeBoundsListener =
                    google.maps.event.addListener(map, 'bounds_changed', function (event) {
                        if (this.getZoom() > 15 && this.initialZoom === true) {
                            this.setZoom(15);
                            this.initialZoom = false;
                        }
                        google.maps.event.removeListener(zoomChangeBoundsListener);
                    });
            });

            map.initialZoom = true;
            map.setCenter(bounds.getCenter());
            map.fitBounds(bounds);
        }

        $('#offices-city-select').on('change', function () {
            var citySlug = $(this).val();

            var officeList = Object.values(offices).filter(row => row.citySlug.includes(citySlug));

            let selectOptions = '';
            officeList.forEach(function (row) {
                selectOptions += '<option data-lat="' + row.lat + '" data-long="' + row.lng + '" value="' + row.officeId + '" >' + row.officeName + '</option>';
            });

            $('#offices-office-select').html(selectOptions);
            $('#offices-office-select').trigger('change');
        });

        $('#offices-office-select').on('change', function () {
            var self = $(this);

            var boundsObj = {
                lat: self.find('option:selected').data().lat,
                lng: self.find('option:selected').data().long
            };

            $.each(markersData, function (index, value) {
                value['infoWindow'].close();
                if (index == self.val()) {
                    map.setZoom(14);
                    map.setCenter(value['marker'].getPosition());

                    map.panTo(value['marker'].position);
                    value['infoWindow'].open(map, value['marker']);
                }
            })

        });
    </script>
@endpush

