@extends('layout.master')
@section('content')
    <div class="page-container pb-5">
        <!-- FAQ -->
        <div class="row">
            <div class="col-12">
                <h1 class="text-title text-black fw-bold text-center mb-0">Актуални промоции и специални предложения</h1>
                <div class="row">
                    <div class="col-12 col-md-8 col-lg-6 offset-md-2 offset-lg-3">
                        <div class="d-block js-listing subsection">
                            <p style="margin-top: 30px; margin-bottom: 30px;">
                                Следи тази страница за актуалните промоционални кампании на StikCredit.bg или посещавай своят <a href="https://stikcredit.bg/vhod-v-profil">профил</a> редовно за персонализирани предложения.
                            </p>
                            <h2>Безлихвен първи кредит</h2>
                            <p>
                                <b>Всеки първи кредит до заплата при нас е с 0% лихва за срока на договора. Вземи до {{$amount}} лева / €{{$amount_eur}} още днес и върни същата сума в края на периода.</b>
                            </p>
                            <p>
                                От промоционалните условия може да се възползва всеки, който е наш клиент за първи път и кандидатства за кредит до заплата на нашата страница <a href="https://stikcredit.bg">StikCredit.bg</a>. Не пропускай шанса си и кандидатствай за кредит сега - без поръчител, без лица за контакт и без доказване на доход.
                            </p>
                            <h2>Големи отстъпки при всеки следващ кредит</h2>
                            <p>
                                <b>Грабни до 30% отстъпка от следващият си онлайн заем.</b>
                            </p>
                            <p>
                               Плащай вноските по твоят бърз кредит или кредит до заплата редовно. Възползвай се от уникалните предложения, които ние ще ти изпратим. По-големият брой изплатени кредити увеличава шансовете ти за по-голяма отстъпка.
                            </p>
                            <h2>Отминали кампании</h2>
                            <p>
                                <b>Stikcredit подари 10 телевизора за 10-тата си годишнина</b>
                            </p>
                            <p>
                                Отпразнувахме нашият 10-ти рожден ден заедно с нашите клиенти - цели десет години <a href="https://stikcredit.bg/blog/chestit-10-ti-rozhden-den-stik-kredit/ "> бързи кредити и доволни клиенти</a>. Всеки изтеглил онлайн кредит в периода 01.07.2023 - 31.07.2023 участва в томболата за големите ни награди, сред които смарт телевизор и смартфон.
                            </p>
                            <p>
                                <b>Обратно на училище</b>
                            </p>
                            <p>
                                През периода 01.09.2022 до 31.09.2022 ние подарихме 25% отстъпка от оскъпяването по <a href="https://stikcredit.bg">бърз заем</a> на родителите, които изпратиха децата си на училище и имаха нужда от допълнителни средства.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calculator -->
        @include('partial.calculator', ['products' => $products])
        @auth
            @push('scripts')
                <script type="text/javascript">
                    $(function () {
                        document
                            .querySelectorAll('.submitBtnProfile')
                            .forEach(function (btn) {
                                btn.addEventListener('click', (event) => {
                                    event.preventDefault();
                                    let form = $(btn).parents('form').first()[0];

                                    let $paymentMethodId = form.querySelector('input[name="payment_method_id"]:checked').value;
                                    if (parseInt($paymentMethodId) === 1) {
                                        $(form.querySelector('input[name="iban"]')).parsley().validate();

                                        if (!$(form.querySelector('input[name="iban"]')).parsley().isValid()) {
                                            return false;
                                        }
                                    }

                                    const profileSubmitBtn = form.querySelector('.submitBtnProfile');
                                    if (profileSubmitBtn) {
                                        profileSubmitBtn.disabled = true;
                                    }

                                    axios
                                        .post('/new-loan', {
                                            product_id: form.querySelector('input[name="product_id"]').value,
                                            amount_requested: form.querySelector('input[name="amount_requested"]').value,
                                            period_requested: form.querySelector('input[name="period_requested"]').value,
                                            payment_method_id: $paymentMethodId,
                                            iban: form.querySelector('input[name="iban"]').value,
                                        })
                                        .then(resp => {

                                            if (profileSubmitBtn) {
                                                profileSubmitBtn.disabled = false;
                                            }

                                            if (!resp.data.success && !resp.data?.redirectTo) {

                                                let alertHeading = document.querySelector('h5.alert-heading');
                                                let alertDanger = document.querySelector('.alert.alert-danger');

                                                if (!alertHeading) {
                                                    alertDanger = document.createElement('div');
                                                    alertDanger.classList.add('alert', 'alert-danger', 'alert-dismissible', 'fade', 'show', 'p-2', 'd-none');

                                                    alertDanger.innerHTML = '<h5 class="alert-heading">Нещо не е наред!</h5><p class="mb-0 error-message"></p><button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';

                                                    alertHeading = alertDanger.querySelector('h5.alert-heading');

                                                    document
                                                        .querySelector('div#profile-errors-container')
                                                        ?.prepend(alertDanger);
                                                }

                                                if (resp.data.messages && resp.data.messages.length > 0) {
                                                    alertHeading.textContent = resp.data.messages;
                                                }

                                                document.querySelector('div.alert-danger').classList.remove('d-none');
                                            }

                                            /// when session expired redirect to home
                                            if (!resp.data.success && resp.data?.redirectTo) {
                                                window.location.replace(resp.data.redirectTo);
                                            }

                                            if (resp.data.success) {
                                                window.location = resp.data.redirectTo;
                                            }
                                        })
                                        .catch(error => {
                                            let $errors = "";
                                            Object.values(error.response.data.errors).forEach(function (row) {
                                                $errors += row[0] + "\n";
                                            });

                                            let alertDanger = document.createElement('div');
                                            alertDanger.classList.add('alert', 'alert-danger', 'alert-dismissible', 'fade', 'show', 'p-2', 'd-none', 'errors-container');
                                            document
                                                .querySelector('div#profile-errors-container')
                                                ?.prepend(alertDanger);

                                            $('div.errors-container').html($errors);
                                            $('div.errors-container').removeClass('d-none');
                                        });
                                });
                            });
                    })
                </script>
            @endpush
        @endauth
    </div>

@endsection
