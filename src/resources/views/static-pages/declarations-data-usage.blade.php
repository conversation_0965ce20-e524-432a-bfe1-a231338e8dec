@extends('layout.master')
@section('content')
    <div class="page-container pb-5">
        <!-- Declarations -->
        <div class="row">
            <div class="col-12">
                <h1 class="text-title text-black fw-bold text-center mb-0">
                    {{__('Декларации и обработка на данни')}}
                </h1>
                <div class="row">
                    <div class="col-12 col-md-8 col-lg-6 offset-md-2 offset-lg-3">
                        <div class="d-block js-listing">
                            @php
                                $faq = [
                                    'personal-data-contract' => [
                                        'label' => 'Декларация за обработване и съхраняване на лични данни, за целите на сключване и изпълнение на договор за кредит',
                                        'partial' => 'personal-data-contract'
                                    ],
                                    'individual-declaration' => [
                                        'label' => 'Декларация относно индивидуализация посредством e-mail',
                                        'partial' => 'individual'
                                    ],
                                    'declaration-13' => [
                                        'label' => 'Декларация относно действие на паролата като саморъчен елетронен подпис съгласно чл. 13, ал. 1 от ЗЕДЕП',
                                        'partial' => 'declaration-13'
                                    ],
                                    'personal-data' => [
                                        'label' => 'Декларация за обработване и съхранение на лични данни',
                                        'partial' => 'personal-data'
                                    ],
                                ];
                            @endphp

                            @foreach ($faq as $department => $data)
                                <section class="mt-4 js-list" data-department="{{ $department }}">
                                    <button
                                            class="d-flex align-items-start justify-content-between w-100 py-3 px-3 btn-list js-list-button"
                                            data-department="{{ $department }}"
                                    >
                                        <div class="d-inline-flex align-items-center pe-3 left">
                                    <span class="d-inline-block text-regular text-dark-grey text-left">
                                        {{ $data['label'] }}
                                    </span>
                                        </div>
                                        <img
                                                data-src="{{ asset('static/images/arrow-i.svg') }}"
                                                class="d-inline-block mt-2 js-list-button-icon js-lazyload invisible"
                                                width="16"
                                                height="16"
                                                alt="Stikcredit FAQ: {{ $data['label'] }}"
                                                title="Stikcredit FAQ: {{ $data['label'] }}"
                                        />
                                    </button>
                                    <div class="js-list-jobs" data-department="{{ $department }}"
                                         style="display: none;">
                                        <div class="d-block py-2 px-3 mt-2 js-list-job faq text-middle text-dark-gray">
                                            @include('partial.declarations.'. $data['partial'])
                                        </div>
                                    </div>
                                </section>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
