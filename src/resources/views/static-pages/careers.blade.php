@extends('layout.master')
@section('content')
    <div class="page-container pb-5">

        <!-- Open Jobs -->
        <div class="row">
            <div class="col-12">
                <h1 class="text-title text-black fw-bold text-center mb-0">Stikcredit се разраства</h1>
                <div class="row mt-4">
                    <div class="col-12 col-md-6 offset-md-3">
                        <div class="d-block js-listing">
                            @php
                                $openJobs = [
                                    [
                                        'label' => 'Копирайтър',
                                        'url' => route('careers') .'/copywriter',
                                    ],
                                    [
                                        'label' => 'Маркетинг специалист',
                                        'url' => route('careers') .'/marketing-specialist',
                                    ],
                                ];
                            @endphp

                            @if (!empty($openJobs))
                                @foreach ($openJobs as $job)
                                    <div class="mt-4 js-list">
                                        <a
                                                href="{{ $job['url'] }}"
                                                class="d-flex align-items-center justify-content-between w-100 py-3 px-3 btn-list js-list-button job-link"
                                        >
                                            <div class="d-inline-flex align-items-center left">
                                                <img
                                                        data-src="{{ asset('static/images/star.svg') }}"
                                                        class="d-inline-block me-2 js-lazyload invisible"
                                                        width="40"
                                                        height="36"
                                                        alt="Stikcredit Careers as {{ $job['label'] }}"
                                                        title="Stikcredit Careers as {{ $job['label'] }}"
                                                />
                                                <span class="d-inline-block text-regular text-dark-gray">
                                            {{ $job['label'] }}
                                        </span>
                                            </div>
                                            <div class="d-inline-block">
                                        <span
                                                class="btn-purple-outline js-list-more"
                                        >
                                            виж
                                        </span>
                                            </div>
                                        </a>
                                    </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- What Makes us Different -->
        <div class="row">
            <div class="col-12 col-md-6 offset-md-3 mt-section">
                <h2 class="text-title text-black fw-bold text-center">Какво ни отличава?</h2>
                <div class="d-block">
                    <img
                            data-src="{{ asset('static/images/what-makes-diff.svg') }}"
                            class="d-block mx-auto img-fluid js-lazyload invisible mt-4"
                            width="320"
                            height="312"
                            alt="Stikcredit What Makes us Different"
                            title="Stikcredit What Makes us Different"
                    />
                </div>
                <div class="d-block">
                    @php
                        $whatMakesUsDifferent = [
                            [
                                'label' => 'Високо качество на работа',
                                'color' => '#B6A4FF',
                                'width' => '75%',
                            ],
                            [
                                'label' => 'Адаптивност',
                                'color' => '#B6A4FF',
                                'width' => '80%',
                            ],
                            [
                                'label' => 'Постоянство',
                                'color' => '#A58DFF',
                                'width' => '85%',
                            ],
                            [
                                'label' => 'Ориентирани сме към резултатите',
                                'color' => '#A58DFF',
                                'width' => '90%',
                            ],
                            [
                                'label' => 'Отворена комуникация',
                                'color' => '#8B72F2',
                                'width' => '95%',
                            ],
                            [
                                'label' => 'Свобода на действие',
                                'color' => '#714FD6',
                                'width' => '100%',
                            ],
                        ];
                    @endphp

                    @foreach ($whatMakesUsDifferent as $data)
                        <div class="d-block rounded-10 text-center text-white fw-bold mt-3 p-2 mx-auto"
                             style="background-color: {{ $data['color'] }}; width: {{ $data['width'] }};">
                            {{ $data['label'] }}
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Our Tech -->
        <div class="row">
            <div class="col-12">
                <div class="shadow-light rounded-15 p-4 pt-3">
                    <h2 class="text-title text-black fw-bold text-center mb-0">Нашите технологии</h2>
                    <div class="row mt-2">
                        <div class="col-12 col-lg-11 col-xl-10 col-xxl-8 mx-lg-auto">
                            <div class="d-flex flex-wrap align-items-center justify-content-center out-tech-container">
                                @php
                                    $ourTechDir = 'static/images/tech/';
                                    $ourTech = scandir($ourTechDir);
                                @endphp

                                @foreach ($ourTech as $tech)
                                    @php
                                        if (
                                            empty($tech) ||
                                            $tech === '.' ||
                                            $tech === '..'
                                        ) { continue; }

                                        $techPath = $ourTechDir . $tech;
                                    @endphp

                                    <div class="d-inline-flex align-items-center justify-content-between our-tech border rounded-circle mt-2">
                                        <img
                                                data-src="{{ asset($techPath) }}"
                                                class="d-block mx-auto img-fluid js-lazyload invisible"
                                                width="88"
                                                height="88"
                                        />
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- <!-- Testimonial #1 -->
        <div class="row">
            <div class="col-12 col-md-7 mx-md-auto mt-section testimonial-section">
                <div class="shadow-thick rounded-10 px-4 pt-5 pb-3 position-relative testimonial-container">
                    <img
                        data-src="{{ asset('static/images/avatar-ex.svg') }}"
                        class="d-inline-block mx-auto img-fluid js-lazyload invisible testimonial-avatar"
                        width="69"
                        height="69"
                        alt="Stikcredit Exmployee Name"
                        title="Stikcredit Exmployee Name"
                    />
                    <p class="text-default text-dark-gray text-center mb-2">
                        „Присъединяването ми към Stikcredit беше ново предизвикателство за мен и чудесен начин да се усъвършенствам. Работата във финтех компания е много вдъхновяваща. В екипа решаваме всички проблеми заедно, помагаме си взаимно и ставаме по-добри."
                    </p>
                    <p class="text-default text-dark-gray text-center mb-0">
                        <span class="d-block fw-bold">Василен</span>
                        Front-end developer
                    </p>
                </div>
            </div>
        </div> --}}

        <!-- Team -->
        <div class="row">
            <div class="col-12 col-md-8 col-lg-6 offset-md-2 offset-lg-3 mt-section">
                <h2 class="text-title text-black fw-bold text-center mb-0">Екип</h2>
                <div class="row align-items-start">
                    @php
                        $teamMembers = [
                            [
                                'avatar' => asset('static/images/team/svetlin-sabev.png'),
                                'linkedIn' => 'https://www.linkedin.com/in/svetlinsabev/',
                                'name' => 'Светлин Събев',
                                'position' => 'Главен оперативен директор',
                            ],
                            [
                                'avatar' => asset('static/images/team/plamen-dechev.png'),
                                'linkedIn' => 'https://www.linkedin.com/in/plamen-dechev/',
                                'name' => 'Пламен Дечев',
                                'position' => 'Мениджър "Обслужване на клиенти"',
                            ],
                        ];
                    @endphp

                    @foreach ($teamMembers as $member)
                        <div class="col-12 col-md-6 team-member mt-4">
                            <div class="team-member-avatar text-center">
                                <a
                                        href="{{ $member['linkedIn'] }}"
                                        class="invisible-anchor d-inline-block position-relative"
                                        target="_blank"
                                >
                                    <img
                                            data-src="{{ $member['avatar'] }}"
                                            class="d-block mx-auto img-fluid js-lazyload invisible team-member-avatar-image rounded-circle"
                                            width="120"
                                            height="120"
                                            alt="Stikcredit Team Member: {{ $member['name']; }}"
                                            title="Stikcredit Team Member: {{ $member['name']; }}"
                                    />
                                    <img
                                            data-src="{{ asset('static/images/lin.svg'), }}"
                                            class="d-block mx-auto img-fluid js-lazyload invisible rounded-circle lin"
                                            width="24"
                                            height="24"
                                            alt="Stikcredit Team Member LinkedIn: {{ $member['linkedIn']; }}"
                                            title="Stikcredit Team Member LinkedIn: {{ $member['linkedIn']; }}"
                                    />
                                </a>
                            </div>
                            <h2 class="text-middle fw-bold text-black text-center mb-0 mt-2">{{ $member['name']; }}</h2>
                            <h3 class="text-default text-dark-grey text-center mb-0 mt-2">{{ $member['position']; }}</h3>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Life In Stikcredit -->
        <div class="row">
            <div class="col-12 col-md-8 col-lg-6 offset-md-2 offset-lg-3 mt-section">
                <div class="px-3">
                    <h2 class="text-title text-black fw-bold text-center mb-0">Животът в Stikcredit</h2>
                    <p class="text-steady text-dark-grey text-center mb-0 mt-4">Последвай ни в LinkedIn и научи повече
                        за компанията и свободните ни позиции</p>
                    <div class="d-block text-center mt-4">
                        <a
                                href="#"
                                class="btn btn-icon btn-purple-outline position-relative text-center"
                                target="_blank"
                        >
                            Последвай ни

                            <img
                                    data-src="{{ asset('static/images/lin.svg'), }}"
                                    class="d-inline-block mx-auto js-lazyload invisible rounded-circle icon"
                                    width="24"
                                    height="24"
                                    alt="Stikcredit LinkedIn: "
                                    title="Stikcredit LinkedIn: "
                            />
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testimonial #2 -->
        <div class="row">
            <div class="col-12 col-md-7 mx-md-auto mt-section testimonial-section">
                <div class="shadow-thick rounded-10 px-4 pt-5 pb-3 position-relative testimonial-container">
                    <img
                            data-src="{{ asset('static/images/team/testimonial.png') }}"
                            class="d-inline-block mx-auto img-fluid js-lazyload invisible testimonial-avatar"
                            width="69"
                            height="69"
                            alt="Stikcredit Exmployee Name"
                            title="Stikcredit Exmployee Name"
                    />
                    <p class="text-default text-dark-gray text-center mb-2">
                        „Обичам Stikcredit, защото срещам разбиране и доверие. Това ме кара да усещам независимост, чрез
                        която успявам да допринеса по възможно най-добрия начин за компанията."
                    </p>
                    <p class="text-default text-dark-gray text-center mb-0">
                        <span class="d-block fw-bold">Теодора</span>
                        Експерт Клиентско Обслужване
                    </p>
                </div>
            </div>
        </div>

        <!-- Pictures -->
        <div class="row mt-section">
            <div class="col-12 col-md-10 offset-md-1">
                @php
                    $images = [
                        [
                            [
                                asset('static/images/party/1.jpg'),
                                asset('static/images/party/2.jpg'),
                                asset('static/images/party/3.jpg'),
                            ],
                            [
                                asset('static/images/party/4.jpg'),
                            ]
                        ],
                        [
                            [
                                asset('static/images/party/5.jpg'),
                            ],
                        ],
                        [
                            [
                                asset('static/images/party/6.jpg'),
                            ],
                            [
                                asset('static/images/party/7.jpg'),
                            ]
                        ]
                    ];
                @endphp

                @foreach ($images as $imageLvlTopIndex => $row)
                    @if (!empty($row))
                        <div class="row align-items-start justify-content-start align-items-normal mt-4">
                            @foreach ($row as $columnIndex => $columnData)
                                @if (!empty($columnData))
                                    <div class="{{ (count($row) > 1 ? 'col-6' : 'col-12' ) }}">
                                        @foreach ($columnData as $imageUrl)
                                            <div class="row image-gallery-item {{ (count($columnData) > 1 ? 'semi' : 'full') }}">
                                                <div class="col-12">
                                                    <img
                                                            data-src="{{ $imageUrl }}"
                                                            class="d-block mx-auto img-fluid js-lazyload cover rounded-10"
                                                            alt="Stikcredit Team"
                                                            title="Stikcredit Team"
                                                    />
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
    </div>

@endsection
