@extends('layout.master')
@section('content')
    <div class="page-container">
        <!-- About us -->
        <section class="row">
            <div class="col-12 col-md-6 offset-md-3 mt-section">
                <h1 class="text-title text-black fw-bold text-center mb-0">Кои сме ние</h1>
                <div class="d-block">
                    <img
                            data-src="{{ url('static/images/hand-shake.svg') }}"
                            class="d-block mx-auto img-fluid js-lazyload invisible mt-4"
                            width="72"
                            height="46"
                            alt="Stikcredit Handshake"
                            title="Stikcredit Handshake"
                    />
                </div>
                <p class="text-regular text-dark-gray text-center mb-0 mt-4">
                    Stikcredit има богат опит в предоставянето на бързи и гъвкави онлайн кредити, съчетани с изключително
                    клиентско обслужване.
                </p>
            </div>
        </section>

        <!-- What makes us different -->
        <section class="row">
            <div class="col-12">
                <h2 class="text-title text-black fw-bold text-center mb-0">Защо сме различни</h2>
                <div class="d-block">
                    <img
                            data-src="{{ url('static/images/wave.svg') }}"
                            class="d-block mx-auto img-fluid js-lazyload invisible mt-4"
                            width="364"
                            height="311"
                            alt="Stikcredit Wave"
                            title="Stikcredit Wave"
                    />
                </div>
                <div class="row">
                    @php
                        $whatMakesUsDifferent = [
                            [
                                'title' => 'Предлагаме ти най-доброто решение',
                                'content' => 'Без значение пред какви предизвикателства се изправяш, нашият любезен екип “Клиентско обслужване” ще намери точното финансово решение за теб.',
                                'subContent' => false,
                            ],
                            [
                                'title' => 'Партньор, на който може да се довериш',
                                'content' => 'Правим нещата лесни, удобни и честни. Предварително знаеш своите разходи. Дори може да ни изпробваш с 0% лихва за първия кредит*.',
                                'subContent' => '*При сключване на първи договор за кредит до заплата от Stikcredit.',
                            ],
                            [
                                'title' => 'Помагаме ти да поемеш контрол над бъдещето си',
                                'content' => 'С гъвкаво финансиране и безплатно финансово консултиране, Stikcredit е тук, за да ти помогне да управляваш успешно своето финансовото състояние.',
                                'subContent' => false,
                            ]
                        ];
                    @endphp

                    @foreach ($whatMakesUsDifferent as $column)
                        <div class="col-12 col-lg-4 mt-4">
                            <h2 class="text-steady-title text-lendivo-primary fw-bold text-center mb-0">
                                {{ $column['title'] }}
                            </h2>
                            <p class="text-regular text-dark-grey text-center mt-3 mb-0">{{ $column['content'] }}</p>
                            @if (!empty($column['subContent']))
                                <p class="text-micro text-dark-grey text-center text-italic mt-3 mb-0">{{ $column['subContent'] }}</p>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </section>

        <!-- Mission -->
        <section class="rounded-15 shadow-thick overflow-hidden mt-section">
            <div class="row align-items-start">
                @php
                    $missionColumns = [
                        [
                            'icon' => [
                                'src' => url('static/images/flag.svg'),
                                'width' => '39',
                                'height' => '40',
                                'alt' => 'Stikcredit Flag',
                                'title' => 'Stikcredit Flag',
                            ],
                            'title' => 'Мисия',
                            'content' => 'Развиваме достъпа до кредитиране, като използваме иновации, за да премахнем финансовия стрес.',
                            'isPurple' => false,
                        ],
                        [
                            'icon' => [
                                'src' => url('static/images/eye.svg'),
                                'width' => '63',
                                'height' => '40',
                                'alt' => 'Stikcredit Eye',
                                'title' => 'Stikcredit Eye',
                            ],
                            'title' => 'Визия',
                            'content' => 'Да осигурим леснодостъпно финансиране в глобален мащаб',
                            'isPurple' => false,
                        ],
                        [
                            'icon' => [
                                'src' => url('static/images/diamond.svg'),
                                'width' => '53',
                                'height' => '40',
                                'alt' => 'Stikcredit Diamond',
                                'title' => 'Stikcredit Diamond',
                            ],
                            'title' => 'Ценности',
                            'content' => '<ul class="ul-lendivo">
                                <li><span>Иновативност</span></li>
                                <li><span>Себеразвитие</span></li>
                                <li><span>Работа за общия успех</span></li>
                                <li><span>Трудолюбие</span></li>
                            </ul>',
                            'isPurple' => true,
                        ],
                    ];
                @endphp

                @foreach ($missionColumns as $column)
                    <div class="col-12 col-lg-4 pt-4 @php echo (!empty($column['isPurple']) ? 'mt-4 mt-lg-0 py-4' : ''); @endphp @php echo (!empty($column['isPurple']) ? 'bg-light-pink' : ''); @endphp">
                        <img
                                data-src="{{ $column['icon']['src'] }}"
                                class="d-block mx-auto js-lazyload invisible"
                                width="{{ $column['icon']['width'] }}"
                                height="{{ $column['icon']['height'] }}"
                                alt="{{ $column['icon']['alt'] }}"
                                title="{{ $column['icon']['title'] }}"
                        />
                        <h2 class="text-steady-title text-lendivo-primary fw-bold text-center mb-0 px-3">{{ $column['title'] }}</h2>
                        <div class="text-regular text-dark-grey text-center mt-3 mb-0 px-3">{!! $column['content'] !!}</div>
                    </div>
                @endforeach
            </div>
        </section>

        <!-- Accomplishments -->
        <section class="row">
            <div class="col-12">
                <h2 class="text-title text-black fw-bold text-center mb-0">Постиженията ни</h2>
                <div class="d-block mt-4">
                    @php
                        $accomplishments = [
                            [
                                'title' => '2013',
                                'content' => 'Основаване на компанията. Развитие на проекта и тестване.'
                            ],
                            [
                                'title' => '2014 - 2015',
                                'content' => '1 млн. лева обща кумулативна сума от раздадени кредити.'
                            ],
                            [
                                'title' => '2016 - 2017',
                                'content' => 'Нова платформа и стартиране на уебсайт.'
                            ],
                            [
                                'title' => '2017 - 2018',
                                'content' => 'Стартиране на мобилно приложение за iOS и Android. Над 25 000 заявления за кредит.'
                            ],
                            [
                                'title' => '2018 - 2019',
                                'content' => 'Листване на p2p пазара за първи път, с последващи 3 нови партньорства. 14 млн. лева обща кумулативна сума на заявените кредити.'
                            ],
                            [
                                'title' => '2019 - 2020',
                                'content' => '120 000 заявления за кредити.'
                            ],
                            [
                                'title' => '2020 - 2021',
                                'content' => '190 000 заявления за кредит и 125 000 регистрирани клиенти. Редизайн на клиентския уебсайт за подобряване на клиентското преживяване. 4000+ отпуснати кредити на месец за първи път 100+ служители'
                            ],
                        ];
                    @endphp

                            <!-- Mobile Slider -->
                    <div class="d-block d-md-none">
                        <div class="carousel js-carousel" id="accomplishments_carousel">
                            <div class="carousel-inner p-1" role="listbox">
                                @foreach($accomplishments as $index => $column)
                                    <div class="carousel-item @if ($index === 0) active @endif"
                                         data-bs-key="{{ $index }}">
                                        <div class="col-12 shadow-thick rounded-4" data-holder-rendered="true">
                                            <div class="p-4">
                                                <h2 class="text-steady-title text-lendivo-primary fw-bold text-center mb-0 px-3">{{ $column['title'] }}</h2>
                                                <div class="text-regular text-dark-grey text-center mt-3 mb-0 px-3">{!! $column['content'] !!}</div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <ol class="list-unstyled d-flex justify-content-center mt-4 js-carousel-btns"
                            id="carousel-btns">
                            @foreach($accomplishments as $index => $column)
                                <li class="ms-3">
                                    <button
                                            data-bs-target="#accomplishments_carousel"
                                            data-bs-slide-to="{{ $index }}"
                                            class="btn btn-pink-outline btn-circle p-1 @if ($index === 0) active @endif">
                                    </button>
                                </li>
                            @endforeach
                        </ol>
                    </div>

                    <!-- Tablet Slider -->
                    <div class="d-none d-md-block d-lg-none">
                        <div class="carousel js-carousel" id="accomplishments_carousel_tablet">
                            <div class="carousel-inner p-1" role="listbox">
                                @php
                                    $countItem = 0;
                                @endphp

                                <div class="carousel-item @if ($countItem === 0) active @endif"
                                     data-bs-key="{{ $countItem }}">
                                    <div class="row">
                                        @foreach($accomplishments as $index => $column)
                                            <div class="col-6" data-holder-rendered="true">
                                                <div class="shadow-thick rounded-4 p-4">
                                                    <h2 class="text-steady-title text-lendivo-primary fw-bold text-center mb-0 px-3">{{ $column['title'] }}</h2>
                                                    <div class="text-regular text-dark-grey text-center mt-3 mb-0 px-3">{!! $column['content'] !!}</div>
                                                </div>
                                            </div>

                                            @php $countItem += 1; @endphp

                                            @if ($countItem % 2 === 0)
                                    </div>
                                </div>
                                <div class="carousel-item @if ($countItem === 0) active @endif"
                                     data-bs-key="{{ $countItem / 2 }}">
                                    <div class="row">
                                        @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>

                        <ol class="list-unstyled d-flex justify-content-center mt-4 js-carousel-btns"
                            id="carousel-btns">
                            @foreach($accomplishments as $index => $column)
                                @if ($index % 2 === 0)
                                    <li class="ms-3">
                                        <button
                                                data-bs-target="#accomplishments_carousel_tablet"
                                                data-bs-slide-to="{{ $index / 2 }}"
                                                class="btn btn-pink-outline btn-circle p-1 @if ($index === 0) active @endif">
                                        </button>
                                    </li>
                                @endif
                            @endforeach
                        </ol>
                    </div>

                    <!-- Large Slider -->
                    <div class="d-none d-lg-block">
                        <div class="carousel js-carousel" id="accomplishments_carousel_large">
                            <div class="carousel-inner p-1" role="listbox">
                                @php
                                    $countItem = 0;
                                @endphp

                                <div class="carousel-item @if ($countItem === 0) active @endif"
                                     data-bs-key="{{ $countItem }}">
                                    <div class="row">
                                        @foreach($accomplishments as $index => $column)
                                            <div class="col-4" data-holder-rendered="true">
                                                <div class="shadow-thick rounded-4 p-4">
                                                    <h2 class="text-steady-title text-lendivo-primary fw-bold text-center mb-0 px-3">{{ $column['title'] }}</h2>
                                                    <div class="text-regular text-dark-grey text-center mt-3 mb-0 px-3">{!! $column['content'] !!}</div>
                                                </div>
                                            </div>

                                            @php $countItem += 1; @endphp

                                            @if ($countItem % 3 === 0)
                                    </div>
                                </div>
                                <div class="carousel-item @if ($countItem === 0) active @endif"
                                     data-bs-key="{{ $countItem / 3 }}">
                                    <div class="row">
                                        @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>

                        <ol class="list-unstyled d-flex justify-content-center mt-4 js-carousel-btns"
                            id="carousel-btns">
                            @foreach($accomplishments as $index => $column)
                                @if ($index % 3 === 0)
                                    <li class="ms-3">
                                        <button
                                                data-bs-target="#accomplishments_carousel_large"
                                                data-bs-slide-to="{{ $index / 3 }}"
                                                class="btn btn-pink-outline btn-circle p-1 @if ($index === 0) active @endif">
                                        </button>
                                    </li>
                                @endif
                            @endforeach
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <!-- Partners -->
        <section class="row">
            <div class="col-12 my-5">
                <h2 class="text-title text-black fw-bold text-center mb-0">Партньори</h2>
                <div class="row">
                    <div class="col-12 col-lg-6 offset-lg-3">
                        <div class="d-flex align-items-center justify-between mt-4">
                            @php
                                $partners = [
                                    [
                                        'icon' => [
                                            'src' => url('static/images/fintech.svg'),
                                            'alt' => 'Stikcredit Partner Fintech',
                                            'title' => 'Stikcredit Partner Fintech',
                                        ]
                                    ],
                                ];
                            @endphp

                            @foreach ($partners as $column)
                                <div class="d-inline-block flex-grow-1">
                                    <img
                                            data-src="{{ $column['icon']['src'] }}"
                                            class="d-block mx-auto img-fluid js-lazyload invisible"
                                            alt="{{ $column['icon']['alt'] }}"
                                            title="{{ $column['icon']['title'] }}"
                                    />
                                </div>
                            @endforeach
                        </div>

                        <div class="d-flex align-items-center justify-between mt-4">
                            @php
                                $partners = [
                                    [
                                        'icon' => [
                                            'src' => url('static/images/bg-post.svg'),
                                            'alt' => 'Stikcredit Partner Balgarski Posti',
                                            'title' => 'Stikcredit Partner Balgarski Posti',
                                        ]
                                    ],
                                    [
                                        'icon' => [
                                            'src' => url('static/images/easypay.svg'),
                                            'alt' => 'Stikcredit Partner EasyPay',
                                            'title' => 'Stikcredit Partner EasyPay',
                                        ]
                                    ],
                                    [
                                        'icon' => [
                                            'src' => url('static/images/epay.svg'),
                                            'alt' => 'Stikcredit Partner ePay',
                                            'title' => 'Stikcredit Partner ePay',
                                        ]
                                    ],
                                ];
                            @endphp

                            @foreach ($partners as $column)
                                <div class="d-inline-block flex-grow-1">
                                    <img
                                            data-src="{{ $column['icon']['src'] }}"
                                            class="d-block mx-auto img-fluid js-lazyload invisible"
                                            alt="{{ $column['icon']['alt'] }}"
                                            title="{{ $column['icon']['title'] }}"
                                    />
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
