@extends('layout.master')
@section('content')
    <div class="page-container pb-5">
        <!-- FAQ -->
        <div class="row">
            <div class="col-12">
                <h1 class="text-title text-black fw-bold text-center mb-0">Как работи stikcredit.bg?</h1>
                <div class="row">
                    <div class="col-12 col-md-8 col-lg-6 offset-md-2 offset-lg-3">
                        <div class="d-block js-listing subsection">
                            <p style="margin-top: 30px;margin-bottom: 15px;">
                                Изтегли кредит до заплата или кредит на вноски като посетиш нашата страница stikcredit.bg. Минималната сума, за която можеш да кандидатстваш е {{$min_amount}} лв/ €{{$min_amount_eur}}, а максималната е {{$max_amount}} лв/ €{{$max_amount_eur}}. Трябва да отговаряш на условията за  одобрение на бърз кредит преди да кандидатстваш.
                            </p>
                            <h2>Как се кандидатства за бърз кредит?</h2>
                            <p>
                                <ul>
                                    <li>Избери подходящият за теб бърз кредит от нашата лесна форма за кандидатстване. Допитай се до нашето ръководство, когато кандидатстваш за кредит онлайн.</li>
                                    <li>Попълни телефонният си номер и натисни бутон "Кандидатствай", което ще те отведе към следващата стъпка.</li>
                                    <li>Попълни своето ЕГН, номер на лична карта и е-мейл адрес.</li>
                                    <li>Съгласи се с Договора и общите условия по него, които ще ти предоставим. Това е всичко.</li>
                                </ul>
                            </p>
                            <h2>Как ще получа парите? </h2>
                            <p>
                                <ul>
                                    <li>Само след няколко минути ще ти изпратим СМС и email с нашето решение.</li>
                                    <li>Ако искането ти е одобрено, може да получиш парите си на момента - на всяка каса на ИзиПей в страната или директно по посочена от теб сметка.</li>
                                </ul>
                            </p>
                            <h2>Как се плаща кредита? </h2>
                            <p>
                                <ul>
                                    <li>Когато дойде датата ти на падеж, може да платиш вноската по кредита си използвайки нашият КИН номер на всяка каса на ИзиПей в страната, по банков път или с карта чрез нашият онлайн POS терминал.  Научи повече за <a href="https://stikcredit.bg/kak-da-platya"> методите на плащане </a></li>
                                    <li>Ако искаш да платиш кредита си предсрочно, може да го направиш без допълнителни такси по всяко едно време. Влез в профила си и натисни бутон "предсрочно погасяване". На екрана ще видиш точната сума, която трябва да платиш.</li>
                                    <li>Ако нямаш възможност да платиш на време, не се притеснявай, защото може да удължиш срока на кредита си. Кредит до заплата може да се удължава неограничен брой пъти, като за целта трябва да се свържеш с нашият отдел клиентско обслужване.</li>
                                </ul>
                            </p>
                            <p>
                                Също така имаш възможност да кандидатстваш за по-голяма сума, с която да се рефинансира кредита ти - независимо дали си изтеглил кредит до заплата или кредит на вноски.
                            </p>
                            <p style="margin-top: 15px;">
                                <strong> Полезен съвет: Бързите кредити са съвременен начин да решиш краткосрочните си финансови проблеми, но помисли внимателно дали нямаш възможност да изтеглиш кредит от банка, който е с по-ниски разходи.</strong>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calculator -->
        @include('partial.calculator', ['products' => $products])
        @auth
            @push('scripts')
                <script type="text/javascript">
                    $(function () {
                        document
                            .querySelectorAll('.submitBtnProfile')
                            .forEach(function (btn) {
                                btn.addEventListener('click', (event) => {
                                    event.preventDefault();
                                    let form = $(btn).parents('form').first()[0];

                                    let $paymentMethodId = form.querySelector('input[name="payment_method_id"]:checked').value;
                                    if (parseInt($paymentMethodId) === 1) {
                                        $(form.querySelector('input[name="iban"]')).parsley().validate();

                                        if (!$(form.querySelector('input[name="iban"]')).parsley().isValid()) {
                                            return false;
                                        }
                                    }

                                    const profileSubmitBtn = form.querySelector('.submitBtnProfile');
                                    if (profileSubmitBtn) {
                                        profileSubmitBtn.disabled = true;
                                    }

                                    axios
                                        .post('/new-loan', {
                                            product_id: form.querySelector('input[name="product_id"]').value,
                                            amount_requested: form.querySelector('input[name="amount_requested"]').value,
                                            period_requested: form.querySelector('input[name="period_requested"]').value,
                                            payment_method_id: $paymentMethodId,
                                            iban: form.querySelector('input[name="iban"]').value,
                                        })
                                        .then(resp => {

                                            if (profileSubmitBtn) {
                                                profileSubmitBtn.disabled = false;
                                            }

                                            if (!resp.data.success && !resp.data?.redirectTo) {

                                                let alertHeading = document.querySelector('h5.alert-heading');
                                                let alertDanger = document.querySelector('.alert.alert-danger');

                                                if (!alertHeading) {
                                                    alertDanger = document.createElement('div');
                                                    alertDanger.classList.add('alert', 'alert-danger', 'alert-dismissible', 'fade', 'show', 'p-2', 'd-none');

                                                    alertDanger.innerHTML = '<h5 class="alert-heading">Нещо не е наред!</h5><p class="mb-0 error-message"></p><button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';

                                                    alertHeading = alertDanger.querySelector('h5.alert-heading');

                                                    document
                                                        .querySelector('div#profile-errors-container')
                                                        ?.prepend(alertDanger);
                                                }

                                                if (resp.data.messages && resp.data.messages.length > 0) {
                                                    alertHeading.textContent = resp.data.messages;
                                                }

                                                document.querySelector('div.alert-danger').classList.remove('d-none');
                                            }

                                            /// when session expired redirect to home
                                            if (!resp.data.success && resp.data?.redirectTo) {
                                                window.location.replace(resp.data.redirectTo);
                                            }

                                            if (resp.data.success) {
                                                window.location = resp.data.redirectTo;
                                            }
                                        })
                                        .catch(error => {
                                            let $errors = "";
                                            Object.values(error.response.data.errors).forEach(function (row) {
                                                $errors += row[0] + "\n";
                                            });

                                            let alertDanger = document.createElement('div');
                                            alertDanger.classList.add('alert', 'alert-danger', 'alert-dismissible', 'fade', 'show', 'p-2', 'd-none', 'errors-container');
                                            document
                                                .querySelector('div#profile-errors-container')
                                                ?.prepend(alertDanger);

                                            $('div.errors-container').html($errors);
                                            $('div.errors-container').removeClass('d-none');
                                        });
                                });
                            });
                    })
                </script>
            @endpush
        @endauth

        <!-- Contact us -->
        @include('partial.contact-us')

    </div>

@endsection
