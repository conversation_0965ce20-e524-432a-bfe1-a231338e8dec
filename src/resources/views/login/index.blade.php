@extends('layout.master')
@section('content')
    <div class="row mt-lg-5 mb-5">
        <div class="col-12 col-lg-7 mx-lg-auto">
            <div class="d-block shadow-thick rounded-15 py-4 mb-3">
                <section class="container">
                    <x-flash-messages/>

                    <h1 class="text-center text-middle-regular text-black fw-bold my-0">
                        {{ __('За да влезеш в профила си, въведи своето ЕГН в полето по-долу.') }}
                    </h1>

                    <div class="response-message text-center mt-3"></div>

                    <div class="row">
                        <div class="col-12 col-lg-8 offset-lg-2">
                            <form action="{{ route('login.post') }}"
                                  method="POST"
                                  class="mt-4"
                                  data-parsley-validate="true"
                                  id="send-sms-code"
                            >
                                @csrf

                                <label class="w-100 mb-0">
                                    <input type="text"
                                           class="form-control rounded-4 border-dark"
                                           name="pin"
                                           placeholder="{{__('ЕГН')}}"
                                           required="required"
                                           minlength="10"
                                           maxlength="10"
                                           data-parsley-length-message="{{__('Невалидно ЕГН')}}"
                                    />
                                </label>

                                <p class="text-dark-gray text-steady-middle mt-4 mb-0">
                                    {{__('Ще получиш СМС, с код за временен достъп.')}}
                                </p>

                                <div class="d-block w-100 mt-4 text-center">
                                    <button type="submit"
                                            id="submit-sms-button"
                                            class="btn btn-primary w-100 mw-250 rounded-pill btn-pink mt-2 mb-3"
                                            disabled>
                                        {{__('Изпрати СМС код')}}
                                    </button>
                                </div>

                            </form>
                        </div>
                    </div>
                </section>
            </div>
            <!-- End ./block-shadow -->
        </div>
        <!-- End ./col -->
    </div>
    <!-- End ./row -->
@endsection
@push('scripts')
<script type="text/javascript">
    $(document).ready(function () {
        $('#submit-sms-button').prop('disabled', false);

        $('form#send-sms-code').on('submit', function (event) {
            event.preventDefault();

            const $form = $(this);
            const formElement = this;
            const responseMessage = $('div.response-message');
            const errorMessage = $('<h5 class="text-danger">');

            // Disable submit button (optional, define this if needed)
            // disableSubmitButton(formElement);

            axios.post($form.attr('action'), $form.serialize())
                .then(response => {
                    const data = response.data;

                    responseMessage.empty(); // Clear previous message

                    if (!data.success) {
                        errorMessage.text(data.error || 'Възникна грешка.');
                        responseMessage.append(errorMessage);
                        enableSubmitButton(formElement);
                        return;
                    }

                    if (data.redirectTo) {
                        window.location.replace(data.redirectTo);
                    }
                })
                .catch(error => {
                    responseMessage.empty();

                    const errorMsg = error.response?.data?.error || 'Възникна сървърна грешка.';
                    errorMessage.text(errorMsg);
                    responseMessage.append(errorMessage);

                    enableSubmitButton(formElement);
                });
        });
    });
</script>
@endpush
