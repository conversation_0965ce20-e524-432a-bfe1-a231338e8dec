@extends('layout.master')
@section('content')
    <div class="page-container pb-5">
        <!-- Calculator -->
        <div class="row">
            <div class="col-12">
                <div class="row">
                    <div class="col-12 col-md-6 offset-md-3" id="profile-errors-container">
                        <div class="alert alert-danger alert-dismissible fade show p-2 d-none" role="alert">
                            <h5 class="alert-heading">{{__('Нещо не е наред!')}}</h5>
                            <p class="mb-0 error-message"></p>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>

                        @include('partial.calculator-tabs', ['products' => ($products ?? [])])
                    </div>
                </div>
                <!-- End ./row -->
            </div>
            <!-- End ./col -->
        </div>
        <!-- End ./row -->
    </div>
    <!-- End ./page-container -->

    @push('scripts')
        <script type="text/javascript">
            $(function () {
                document
                    .querySelectorAll('.submitBtnProfile')
                    .forEach(function (btn) {
                        btn.addEventListener('click', (event) => {
                            event.preventDefault();
                            let form = $(btn).parents('form').first()[0];

                            let $paymentMethodId = form.querySelector('input[name="payment_method_id"]:checked').value;
                            if (parseInt($paymentMethodId) === 1) {
                                $(form.querySelector('input[name="iban"]')).parsley().validate();

                                if (!$(form.querySelector('input[name="iban"]')).parsley().isValid()) {
                                    return false;
                                }
                            }

                            const profileSubmitBtn = form.querySelector('.submitBtnProfile');
                            if (profileSubmitBtn) {
                                profileSubmitBtn.disabled = true;
                            }

                            axios
                                .post('/new-loan', {
                                    product_id: form.querySelector('input[name="product_id"]').value,
                                    amount_requested: form.querySelector('input[name="amount_requested"]').value,
                                    period_requested: form.querySelector('input[name="period_requested"]').value,
                                    payment_method_id: $paymentMethodId,
                                    iban: form.querySelector('input[name="iban"]').value,
                                    insurance: form.querySelector('input[type="radio"][name="insurance"]:checked')?.value || 'without_insurance'
                                })
                                .then(resp => {

                                    if (profileSubmitBtn) {
                                        profileSubmitBtn.disabled = false;
                                    }

                                    if (!resp.data.success && !resp.data?.redirectTo) {

                                        let alertHeading = document.querySelector('h5.alert-heading');
                                        let alertDanger = document.querySelector('.alert.alert-danger');

                                        if (!alertHeading) {
                                            alertDanger = document.createElement('div');
                                            alertDanger.classList.add('alert', 'alert-danger', 'alert-dismissible', 'fade', 'show', 'p-2', 'd-none');

                                            alertDanger.innerHTML = '<h5 class="alert-heading">Нещо не е наред!</h5><p class="mb-0 error-message"></p><button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';

                                            alertHeading = alertDanger.querySelector('h5.alert-heading');

                                            document
                                                .querySelector('div#profile-errors-container')
                                                ?.prepend(alertDanger);
                                        }

                                        if (resp.data.messages && resp.data.messages.length > 0) {
                                            alertHeading.textContent = resp.data.messages;
                                        }

                                        document.querySelector('div.alert-danger').classList.remove('d-none');
                                    }

                                    /// when session expired redirect to home
                                    if (!resp.data.success && resp.data?.redirectTo) {
                                        window.location.replace(resp.data.redirectTo);
                                    }

                                    if (resp.data.success) {
                                        window.location = resp.data.redirectTo;
                                    }
                                })
                                .catch(error => {
                                    let $errors = "";
                                    Object.values(error.response.data.errors).forEach(function (row) {
                                        $errors += row[0] + "\n";
                                    });

                                    $('div.errors-container').html($errors);
                                    $('div.errors-container').removeClass('d-none');
                                });
                        });
                    });
            })
        </script>
    @endpush
@endsection
