<p class="mb-2 d-flex align-items-center justify-content-center">
    сума
    <b id="amount-range-{{ $product['product_id'] }}-value" class="fs-2 mx-4">
        {{ $slidersData['default_amount'] }}
    </b>
    лева / €<span id="selected_amount_eur-{{ $product['product_id'] }}">{{ $slidersData['default_amount_eur'] }}</span>
</p>

<input type="hidden" name="product_id" value="{{ $product['product_id'] }}">
<label class="d-flex align-items-center justify-content-between">
    <span class="float-start fs-08 fw-bold">{{ $slidersData['min_amount'] }} лв. / €{{ $slidersData['min_amount_eur'] }}</span>
    <input type="range"
           class="form-range w-65"
           min="{{ $slidersData['min_amount'] }}"
           max="{{ $slidersData['max_amount'] }}"
           step="{{ $slidersData['amount_step'] }}"
           value="{{ $slidersData['default_amount'] }}"
           name="amount_requested"
           data-parsley-excluded="true"
           id="amount-range-{{ $product['product_id']  }}">
    <span class="float-end fs-08 fw-bold">{{ $slidersData['max_amount'] }} лв. / €{{ normalizeCurrency($slidersData['max_amount_eur']) }}</span>
</label>

@if($hasPeriod)
    <p class="mb-2 d-flex align-items-center justify-content-center">
        {{__('срок')}}
        <b id="period-range-{{ $product['product_id'] }}-value" class="fs-2 mx-4">
            {{ $slidersData['default_period'] }}
        </b>
        {{$product['slider_stats']['periodLabel']}}
    </p>

    <label class="d-flex align-items-center justify-content-between">
        <span class="float-start fs-08 fw-bold">
            {{ $slidersData['min_period'] }} {{mb_substr($product['slider_stats']['periodLabel'], 0,3)}}.
        </span>
        <input type="range"
               class="form-range w-65"
               min="{{ $slidersData['min_period'] }}"
               max="{{ $slidersData['max_period'] }}"
               step="{{ $slidersData['period_step'] }}"
               value="{{ $slidersData['default_period'] }}"
               data-parsley-excluded="true"
               name="period_requested"
               id="period-range-{{ $product['product_id']  }}">
        <span class="float-end fs-08 fw-bold">{{ $slidersData['max_period'] }} {{mb_substr($product['slider_stats']['periodLabel'], 0,3)}}.</span>
    </label>
@endif
