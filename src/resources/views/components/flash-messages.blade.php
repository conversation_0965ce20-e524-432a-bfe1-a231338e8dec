@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show p-3" role="alert">
        <p class="mb-0">{{session('success')}}</p>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show p-3" role="alert">
        <p class="mb-0">{{session('error')}}</p>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if($errors->count())
    <div class="alert alert-danger alert-dismissible fade show p-3" role="alert">
        <ol>
            @foreach($errors->all() as $error)
                <li>{{$error}}</li>
            @endforeach
        </ol>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if(session('warning'))
    <div class="alert alert-warning alert-dismissible fade show p-3" role="alert">
        <p class="mb-0">{{session('warning')}}</p>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif
