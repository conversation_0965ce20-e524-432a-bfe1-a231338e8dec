<div class="row credit-info">
    <div class="col-12 {{ empty($isFull) ? 'col-lg-6 offset-lg-3' : '' }}">
        <div class="d-block w-100">
            <h4 class="fw-semibold text-center text-middle text-black my-0">
                {{ $header }}
            </h4>

            <div class="d-block mt-4 mb-4">
                @foreach($creditInfo as $title => $value)
                    <x-credit-info-row :title="$title" :param="$value" :last="$loop->last"/>
                @endforeach
            </div>

            @isset($addonCreditInfo)
                @foreach($addonCreditInfo as $title => $value)
                    <x-credit-info-row :title="$title" :param="$value" :last="$loop->last"/>
                @endforeach
            @endisset

            @if($grid)
                <div class="d-flex flex-column">
                    <div class="d-flex justify-content-between flex-row mb-4">
                        <a class="btn rounded-pill btn-purple-outline align-self-center me-5 pt-3 pb-3 w-100 fw-semibold fs-15 ref-btn"
                           href="{{ route('refinance') }}"
                        >
                            {{__('Рефинанс')}}
                        </a>
                        <a class="btn rounded-pill btn-purple-outline align-self-center w-100 fw-semibold fs-15"
                           href="{{ route('installment.calendar') }}">
                            {!! __('Календар <br> на вноски') !!}
                        </a>
                    </div>
                    <div class="d-flex justify-content-around flex-row">
                        <a class="btn rounded-pill btn-purple-outline align-self-center w-100 fw-semibold fs-15"
                           href="{{ route('early.repayment') }}">
                            {!! __('Предсрочно <br> погасяване') !!}
                        </a>
                    </div>
                </div>

                <div class="d-block w-100 mt-4 text-center">
                    <a class="d-inline-block btn btn-primary w-100 mw-250 rounded-pill btn-pink"
                       href="{{ route('how-to-pay') }}">
                        {{__('Как да платя?')}}
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
