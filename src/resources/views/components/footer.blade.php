@php
    $socialIcons = [
        [
            'href' => 'https://www.facebook.com/stikcredit.bg',
            'icon' => asset('static/images/facebook.svg')
        ],
        [
            'href' => 'https://twitter.com/stikcredit',
            'icon' => asset('static/images/twitter.svg'),
        ],
        [
            'href' => 'https://www.linkedin.com/company/stikcredit/',
            'icon' => asset('static/images/linkedin.svg'),
        ],
        [
            'href' => 'https://www.instagram.com/stikcredit.bg/',
            'icon' => asset('static/images/instagram.svg'),
        ],
        [
            'href' => 'https://www.youtube.com/channel/UCck_L1CB_qoD3eOYwiagHXw',
            'icon' => asset('static/images/youtube.svg'),
        ],
    ];
@endphp

<footer class="text-white footer-colour footer">
    <div class="container py-5 pb-5">
        <div class="row">
            <div id="footer-contacts" class="col-12 col-lg-8">
                <a href="{{route('home.index')}}" class="d-block inivisible-anchor mb-5 white-footer-link-with-img">
                    <img src="{{ asset('/images/stikcredit_logo_white.svg') }}"
                         class="d-inline-block m-0"
                         height="103"
                         alt="{{__('Stikcredit')}}"
                    />
                </a>

                <a class="d-flex text-left text-white invisible-anchor p-0 mb-2" href="tel:070010514" role="button">
                    <img class="icon contact-icon-2 d-inline-block me-3"
                         src="{{ asset('static/images/phone.svg') }}"
                         alt="phone"
                    />
                    <span class="mx-2 d-inline-block mb-0 mt-0 align-self-center">070010514</span>
                </a>

                <a class="d-flex text-left text-white invisible-anchor p-0 mb-2" href="mailto:<EMAIL>"
                   role="button">
                    <img class="icon contact-icon-2 d-inline-block me-3" src="{{ asset('static/images/email.svg') }}"
                         alt="email">
                    <span class="mx-2 d-inline-block mb-0 mt-0 align-self-center"><EMAIL></span>
                </a>

                <p class="d-flex align-items-start text-left text-white invisible-anchor p-0 mt-0 mb-2">
                    <img class="icon contact-icon-2 d-inline-block me-3" src="{{ asset('static/images/hours.svg') }}"
                         alt="hours">
                    <span class="mx-2">
                        Работно време:<br>
                        Пон. - Пет.: 08:00-20:00<br>
                        Съб. - Нед.: 09:00-18:00
                    </span>
                </p>

                <div class="mt-5 d-none d-lg-block">
                    @foreach ($socialIcons as $icon)
                        <a class="btn-floating" target="_blank" href="{{ $icon['href'] }}" role="button">
                            <img class="icon" src="{{ $icon['icon'] }}" alt="facebook">
                        </a>
                    @endforeach
                </div>
            </div>

                        <div class="col-12 col-lg-3 offset-lg-1 mt-5 mt-lg-0 js-list">
                            @php
                                $quickLinks = [
                                    'stikcredit' => [
                                        'label' => 'Полезно',
                                        'href' => '#',
                                        'children' => [
                                            [
                                                'label' => 'Общи условия за кредит',
                                                'href' => route('terms-and-conditions'),
                                            ],
                                            [
                                                'label' => 'Политика за защита на личните данни',
                                                'href' => route('politika-za-zashtita-na-lichnite-danni'),
                                            ],
                                            [
                                                'label' => 'Кажи на приятел - вземи пари',
                                                'href' => route('referrals.terms'),
                                            ],
                                            [
                                                'label' => 'Тарифа',
                                                'href' => route('tarifa'),
                                            ],
                                            [
                                                'label' => 'Политика за бисквитки',
                                                'href' => route('politika-cookies'),
                                            ],
                                            [
                                                'label' => 'Комисия за защита наличните данни',
                                                'href' => 'https://cpdp.bg/',
                                            ],
                                        ],
                                    ],
                                ];
                            @endphp
                            @foreach ($quickLinks as $key => $section)
                                <div class="quick-link-section mt-1 pb-1 mt-lg-5 js-link-section" data-department="{{ $key }}">
                                    <a
                                            href="{{ $section['href'] }}"
                                            class="d-flex align-items-center justify-content-between text-micro fw-bold text-white invisible-anchor quick-link-section-trigger js-list-button js-link-section-trigger"
                                            data-department="{{ $key }}"
                                    >
                                    <span class="d-inline-block">
                                        {{ $section['label'] }}
                                    </span>
                                        <span class="d-inline-block d-lg-none">
                                        <img class="icon d-inline-block" src="{{ asset('static/images/down.svg') }}" alt="down">
                                    </span>
                                    </a>

                                    <div class="d-lg-block js-list-jobs js-link-section-links" data-department="{{ $key }}"
                                         style="display: none;">
                                        @foreach ($section['children'] as $childSection)
                                            <a
                                                    href="{{ $childSection['href'] }}"
                                                    class="d-block mt-2 text-micro text-white invisible-anchor"
                                            >
                                                {{ $childSection['label'] }}
                                            </a>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>

            <div class="col-12 d-lg-none mt-5 mb-5">
                <div class="d-flex align-items-center justify-content-around">
                    @foreach ($socialIcons as $icon)
                        <a class="btn-floating" target="_blank" href="{{ $icon['href'] }}" role="button">
                            <img class="icon" src="{{ $icon['icon'] }}" alt="facebook">
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    {{--    <div class="d-block text-center lower-footer-colour pt-5">--}}
    {{--        <div class="container">--}}
    {{--            <div class="row">--}}
    {{--                <div class="col-12 col-lg-6 offset-lg-3">--}}
    {{--                    <div class="row">--}}
    {{--                        <div class="col-12 col-lg-10 offset-lg-1">--}}
    {{--                            <section class="d-flex align-content-center justify-content-around">--}}
    {{--                                <div id="lcqualitybadge"></div>--}}
    {{--                                <script async src="//cdn.livechatinc.com/qb/qb-8055621-light-200.js"></script>--}}
    {{--                            </section>--}}
    {{--                        </div>--}}
    {{--                    </div>--}}

    {{--                    <div class="mt-5 pb-5 fs-10">© 2013-@php echo date("Y"); @endphp „СТИК-КРЕДИТ“ АД</div>--}}
    {{--                </div>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}
</footer>
