@php
    $startAmount = (int) config('app.insurance_start_amount');

    $showClass = 'd-none';
    $checkedByDefault = false;
    if (
        isset($productData['slider_data']['default_amount'])
        && (int) $productData['slider_data']['default_amount'] >= (int) config('app.insurance_start_amount')
    ) {
        $showClass = '';
        $checkedByDefault = true;
    }

    if (null !== session('insurance_choice')){
        $showClass = 'd-none';
    }


    if(false === $hasInsuranceAction && empty($refinance)){
        $checkedByDefault = false;
    }

    /// if loan has insurance
    if(true === $hasInsuranceAction && empty($refinance)){
        $checkedByDefault = true;
    }

    // Set default values if not provided
    $insuranceInfoExists = $insuranceInfoExists ?? '';
    $insuranceInfoNonExists = $insuranceInfoNonExists ?? '';
@endphp
@push('styles')
    <style>
        @media (max-width: 1199px) {
            label[for^="with-insurance-"] {
                font-size: 14px;
            }

            label[for^="without-insurance-"] {
                font-size: 14px;
            }
        }

        @media (max-width: 991px) {
            label[for^="with-insurance-"] {
                font-size: 13px;
                white-space: nowrap;
            }

            label[for^="without-insurance-"] {
                font-size: 13px;
                white-space: nowrap;
            }
        }

        .no-wrap {
            white-space: nowrap !important;
        }
    </style>
@endpush
<div id="insurance-{{$productId}}" class="{{$showClass}} text-start">
    <div class="p-3 mt-3 mb-3" style="border:1px solid #cccccc; border-radius: 6px">
        <!-- Toggle Header -->
        <p class="mb-0 no-wrap">
            <label for="with-insurance-{{$productId}}" class="cursor-pointer">
                <input type="radio"
                       name="insurance"
                       value="with_insurance"
                       id="with-insurance-{{$productId}}"
                    @checked(true === $checkedByDefault)
                />
                <span class="cursor-pointer" style="padding: 5px;" id="insuranceToggle-{{$productId}}">
                    &nbsp;Защита срещу финансова нестабилност
                    <i class="fa-solid fa-chevron-right arrow" id="insuranceArrow-{{$productId}}"></i>
                </span>
            </label>
        </p>

        <!-- Collapsible Content -->
        <div id="insuranceContent-{{$productId}}" class="d-none">

            <div class="ps-4 mt-3">
                <p class="fs-12">
                    <label for="pay-type-{{$productId}}" class="cursor-pointer">
                        <input type="radio"
                               name="pay_type"
                               value="with_insurance_from_loan"
                               id="pay-type-{{$productId}}"
                            @checked(true === $checkedByDefault)
                        />&nbsp;
                        Желая сумата на застрахователната премия да бъде платена от размера на кредита.
                    </label>
                </p>

                <p class="fs-12">
                    <label for="pay-type-{{$productId}}" class="text-secondary cursor-pointer">
                        <input type="radio"
                               name="pay_type"
                               id="pay-type-{{$productId}}"
                               disabled
                        />&nbsp;
                        Желая да платя застрахователната премия по банкова сметка.
                    </label>
                </p>
            </div>

            <p class="text-secondary">
                <label for="without-insurance-{{$productId}}" class="cursor-pointer">
                    <input type="radio"
                           name="insurance"
                           value="without_insurance"
                           class="me-2"
                           id="without-insurance-{{$productId}}"
                        @checked(false === $hasInsuranceAction)
                    />
                    &nbsp;Без защита срещу финансова нестабилност.
                </label>
            </p>
        </div>
    </div>
</div>

@once
    @push('scripts')
        <script type="text/javascript">
            $(() => {
                let lastChoice = {};

                document.querySelectorAll('[id^="insurance-"]').forEach(function (insuranceBox) {
                    const productId = insuranceBox.id.replace('insurance-', '');

                    const inputRadioWithInsurance = document.getElementById(`with-insurance-${productId}`);
                    const inputRadioWithoutInsurance = document.getElementById(`without-insurance-${productId}`);
                    const inputRadioPayType = document.getElementById(`pay-type-${productId}`);

                    const allPayTypeRadios = insuranceBox.querySelectorAll('input[name="pay_type"]');

                    const amountInput = document.getElementById(`amount-range-${productId}`);

                    // Toggle functionality
                    const toggleButton = document.getElementById(`insuranceToggle-${productId}`);
                    const toggleContent = document.getElementById(`insuranceContent-${productId}`);
                    const toggleArrow = document.getElementById(`insuranceArrow-${productId}`);

                    if (toggleButton && toggleContent && toggleArrow) {
                        toggleButton.addEventListener('click', function (e) {
                            e.preventDefault();
                            e.stopPropagation();

                            const isHidden = toggleContent.classList.contains('d-none');

                            if (isHidden) {
                                // Show content
                                toggleContent.classList.remove('d-none');
                                toggleArrow.setAttribute('data-icon', 'chevron-down');

                            } else {
                                // Hide content
                                toggleContent.classList.add('d-none');
                                toggleArrow.setAttribute('data-icon', 'chevron-right');
                            }

                        });
                    }

                    /// by default all is with insurance
                    if (inputRadioPayType && inputRadioPayType.checked) {
                        lastChoice[productId] = inputRadioWithInsurance.value;
                    } else {
                        lastChoice[productId] = inputRadioWithoutInsurance.value;
                    }

                    // Function to update insurance info display in credit-info component
                    function updateInsuranceInfo() {
                        // Find the credit info row with "Застраховка" title
                        const creditInfoRows = document.querySelectorAll('.credit-info .py-2');
                        creditInfoRows.forEach(row => {
                            const titleSpan = row.querySelector('.fw-semibold');
                            if (titleSpan && titleSpan.textContent.trim() === 'Застраховка') {
                                const valueSpan = row.querySelector('.value');
                                if (valueSpan) {
                                    if (inputRadioWithInsurance.checked) {
                                        lastChoice[productId] = inputRadioWithInsurance.value;
                                    }
                                    if (inputRadioWithoutInsurance.checked) {
                                        lastChoice[productId] = inputRadioWithoutInsurance.value;
                                    }

                                    if (lastChoice[productId] === 'without_insurance') {
                                        valueSpan.innerHTML = '{!! $insuranceInfoNonExists !!}';
                                    } else {
                                        valueSpan.innerHTML = '{!! $insuranceInfoExists !!}';
                                    }
                                }
                            }
                        });
                    }

                    insuranceBox.querySelectorAll('input[name="insurance"]').forEach(radio => {
                        radio.addEventListener('change', function (event) {

                            /// set last selected
                            lastChoice[productId] = event.target.value;

                            localStorage.setItem(productId + '.lastInsuranceChoice', event.target.value);

                            if (event.target.value === 'with_insurance' && inputRadioPayType) {
                                // When "with insurance" is selected, automatically check "from loan" payment type
                                inputRadioPayType.checked = true;
                            } else {
                                // If user selects "without insurance", uncheck any pay_type
                                allPayTypeRadios.forEach(payTypeRadio => {
                                    payTypeRadio.checked = false;
                                });
                            }

                            // Update insurance info display
                            updateInsuranceInfo();

                            /// this event will be refresh refinance data and update received amount
                            document.dispatchEvent(new Event('change-insurance-choice'));
                        });
                    });

                    insuranceBox.querySelectorAll('input[name="pay_type"]').forEach(radio => {
                        radio.addEventListener('change', function (event) {
                            if (event.target.checked) {
                                // Automatically check "with_insurance" if a pay type is selected
                                if (inputRadioWithInsurance && !inputRadioWithInsurance.checked) {
                                    inputRadioWithInsurance.checked = true;

                                    lastChoice[productId] = inputRadioWithInsurance.value;
                                    localStorage.setItem(productId + '.lastInsuranceChoice', inputRadioWithInsurance.value);

                                    inputRadioWithInsurance.dispatchEvent(new Event('change', {bubbles: false}));

                                    updateInsuranceInfo();
                                }
                            }
                        });
                    });

                    if (amountInput) {
                        function handleInsuranceBoxUpdate(event) {
                            let targetAmount = 0;
                            @if(!empty($refinance))
                                targetAmount = parseInt(document.querySelector('span.amount-to-receive').textContent.trim());
                                targetAmount += 40;
                            @else
                                targetAmount = parseInt(event.target.value);
                            @endif

                            if (targetAmount >= {{$startAmount}}) {
                                insuranceBox.classList.remove('d-none');

                                @if(empty($refinance))
                                    inputRadioWithInsurance.checked = true;
                                    inputRadioPayType.checked = true;
                                    inputRadioWithoutInsurance.checked = false;
                                @endif
                            } else {
                                insuranceBox.classList.add('d-none');

                                inputRadioWithInsurance.checked = false;
                                inputRadioPayType.checked = false;

                                inputRadioWithoutInsurance.checked = true;
                            }

                            /// update insurance info only for active product tab
                            if (insuranceBox.closest('div.tab-pane')?.classList.contains('active')) {
                                updateInsuranceInfo();
                            }
                        }

                        /// when change amount trigger event for show or hide insurance box
                        amountInput.addEventListener('change', handleInsuranceBoxUpdate);
                    }

                    if (amountInput && parseInt(amountInput?.value) >= parseInt('{{config('app.insurance_start_amount')}}')) {
                        insuranceBox.classList.remove('d-none');
                    }

                    // Initialize insurance info display on page load
                    updateInsuranceInfo();

                    // Run when custom recalc-done event is dispatched
                    document.addEventListener('recalc-done', handleInsuranceBoxUpdate);
                });
            });
        </script>
    @endpush
@endonce
