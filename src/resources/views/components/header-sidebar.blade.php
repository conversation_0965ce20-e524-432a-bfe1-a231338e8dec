<header class="on-top bg-white">
    <div class="container bg-white position-relative z-4">
        <div class="row">
            <div class="col-12 position-relative">

                <nav
                        class="flex align-items-center justify-content-between navbar navbar-expand-lg navbar-light on-top bg-white pb-0 mb-2">
                    <div class="d-inline-block d-xl-none">
                        <a href="tel:070010514" class="py-2">
                            <img src="{{ asset('static/images/phone_icon.svg') }}"
                                 alt="phone"
                            />
                        </a>
                    </div>

                    <a href="{{ route('home.index') }}">
                        <img src="{{ asset('/images/stikcredit.svg') }}"
                             alt="{{__('Stikcredit')}}"
                             class="d-inline-block"
                             height="33"
                        />
                    </a>

                    <div class="d-inline-flex align-items-center">
                        <div class="d-none d-xl-inline-flex desktop-header-menu">
                            @php
                                $menuItems = [
                                    [
                                        'label' => 'Как работи',
                                        'href' => route('how-it-works'),
                                        'classes' => 'text-dark-gray',
                                    ],
                                    [
                                        'label' => 'Как да платя',
                                        'href' => route('how-to-pay'),
                                        'classes' => 'text-dark-gray',
                                    ],
                                    [
                                        'label' => 'Новини',
                                        'href' => route('blog.index'),
                                        'classes' => 'text-dark-gray',
                                    ],
                                    [
                                        'label' => 'Промоции',
                                        'href' => route('promotions'),
                                        'classes' => 'text-dark-gray',
                                    ],
                                    [
                                        'label' => 'Контакти',
                                        'href' => route('contacts'),
                                        'classes' => 'text-dark-gray',
                                    ],
                                    [
                                        'label' => '070010514',
                                        'href' => 'tel:070010514',
                                        'classes' => 'text-lendivo-secondary',
                                    ],
                                ];
                            @endphp
                            @foreach ($menuItems as $item)
                                <a
                                        href="{{ $item['href'] }}"
                                        class="d-inline-block fw-semibold text-steady-middle {{ $item['classes'] }}"
                                >
                                    {!! $item['label']; !!}
                                </a>
                            @endforeach
                        </div>
                        @auth
                            <a href="#" data-toggle="dropdown" class="py-2 ms-3" id="profile-dd">
                                <img src="{{ asset('static/images/profile_logged.svg') }}" alt="profile">
                            </a>
                        @endauth
                        @guest
                            <a href="{{ route('login') }}" class="py-2 ms-3" id="profile-dd">
                                <img src="{{ asset('static/images/profile_icon.svg') }}" alt="profile">
                            </a>
                        @endguest
                    </div>
                </nav>
                @auth
                    <div class="dropdown lendivo-dropdown border-0 bg-light-pink rounded-bottom-8 mt-n2 visually-hidden"
                         id="dd">
                        <a class="dropdown-item border-bottom fw-semibold fs-16 ps-4 p-2 border-dark-purple"
                           href="{{ route('active.loan') }}">
                            {{__('Начало')}}
                        </a>

                        <a class="dropdown-item border-bottom fw-semibold fs-16 ps-4 p-2 border-dark-purple"
                           href="{{ route('profileSettings') }}">
                            {{__('Клиентски настройки')}}
                        </a>

                        <a class="dropdown-item fw-semibold fs-16 ps-4 p-2" href="{{ route('logout') }}">
                            {{__('Изход')}}
                        </a>
                    </div>
                @endauth
            </div>
        </div>
    </div>
    <!-- End ./container -->

    @if(!auth()->check())
        <nav id="menu-mobile">
            <div id="menu-mobile-btns">
                <div class="container">
                    <nav
                            class="flex align-items-center justify-content-between navbar navbar-expand-lg navbar-light on-top">
                        <a href="javascript:void(0);" id="menu-mobile-button" class="px-2">
                            <i class="fa fa-lg fa-bars"></i>
                        </a>
                        <span>Меню</span>
                        <a id="menu-mobile-button-hidden" class="px-2">
                            <i class="fa fa-lg fa-bars"></i>
                        </a>
                    </nav>
                </div>
                <!-- End ./container -->
            </div>

            <ul id="menu-mobile-list">
                @foreach ($menuItems as $item)
                    <li>
                        <a
                                href="{{ $item['href'] }}"
                                class="d-inline-block fw-semibold text-steady-middle {{ $item['classes'] }}"
                        >
                            {!! $item['label']; !!}
                        </a>
                    </li>
                @endforeach
            </ul>
        </nav>
        <!-- End ./container -->
    @endif
    @auth
        <div id="cover" class="position-fixed visually-hidden d-lg-none"></div>
    @endauth
</header>
<script type="text/javascript">
    // const mobMenuButton = document.getElementById("menu-mobile-button");
    const mobMenuButton = document.getElementById("menu-mobile");
    const mobNav = document.getElementById("menu-mobile");
    const mobNavList = document.getElementById("menu-mobile-list");

    if (mobMenuButton && mobNav && mobNavList) {
        mobMenuButton.addEventListener('click', () => {
            mobNav.classList.toggle('show');
            mobNavList.classList.toggle('show');
        });
    }
</script>
