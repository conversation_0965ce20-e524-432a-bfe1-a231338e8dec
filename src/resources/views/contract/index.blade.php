@extends('layout.master')
@section('content')
    <div class="row">
        <div class="col-12 col-lg-6 mx-lg-auto">

            <h1 class="text-center text-middle-regular text-black fw-bold my-0">
                {{__('Приеми договора')}}
            </h1>
            <p class="text-center text-dark-gray text-default mt-4 mb-0">
                Това е твоят договор за кредит. Моля, запознай се с него подробно.
                При натискане на бутон “Подпис” приемаш, че си прочел и си съгласен с условията на
                кредита.
            </p>
            <div class="text-center mt-4">
                <x-document-download
                    :templateType="'contract'"
                    :templateText="'Изтегли Договор за кредит ' . $client->get('loan_data')['loan_id']"
                    :loanData="$client->get('loan_data')"
                />

            </div>
            <div class="d-block w-100 mt-4">
                <form class="w-100 text-center" action="{{ route('contract.post') }}" method="POST"
                      data-parsley-validate="true"
                      onsubmit="disableSubmitButton(this)"
                >
                    {{ csrf_field() }}
                    <input type="hidden" name="loan_id" value="{{$loan_data['loan_id']}}"/>
                    <input type="hidden" name="client_id" value="{{$client->get('client_id')}}"/>
                    <input type="hidden" name="login_token" value="{{$client->get('remember_token')}}"/>
                    @if(!empty($loan_data['source']))
                        <input type="hidden" name="source" value="{{$loan_data['source']}}"/>
                    @endif

                    @if(!empty($loan_data['source']) && $loan_data['source'] === 'affiliate')
                        @include('partial.payment')
                    @endif

                    <button type="submit" class="btn btn-primary w-100 mw-250 rounded-pill btn-pink">
                        {{__('Подпис')}}
                    </button>

                    <x-insurance-message
                        product-id="{{$loan_data['product_id']}}"
                        :has-insurance-action="$loan_data['has_insurance_action']"
                        :product-data="$productData"
                        :insurance-info-exists="$insurance_info_exists"
                        :insurance-info-non-exists="$insurance_info_non_exists"
                    />
                </form>
            </div>

            <div class="d-block shadow-thick rounded-15 py-4 mt-5">
                <div class="container">

                    <x-flash-messages/>

                    <x-credit-info :header="$header" :creditInfo="$creditInfo" is-full="true"/>
                </div>
            </div>
        </div>
    </div>
    <!-- End ./row -->
@endsection
