@extends('layout.master')
@section('content')
    <div class="row mt-5">
        <div class="col-12 col-lg-7 mx-lg-auto">
            <div class="d-block shadow-thick rounded-15 py-5">
                <section class="container">
                    <x-flash-messages/>

                    <form action="{{ route('third.step.post') }}" method="POST" data-parsley-validate="true"
                          id="third-step-form">
                        <div class="row align-items-start">
                            <div class="col-12 col-md-6 order-1">
                                <h1 class="text-steady-title fw-semibold text-center my-0">
                                    {{__('Попълни следните данни')}}
                                </h1>
                                <div class="d-block mt-3">
                                    {{ csrf_field() }}
                                    <input type="hidden" value="{{ $loan_data['request_id'] ?? 0 }}" name="request_id"/>

                                    <label class="w-100 mb-2">
                                        <input class="form-control rounded-4"
                                               type="text"
                                               placeholder="{{__('Име')}}"
                                               name="first_name"
                                               value="{{old('first_name')}}"
                                               required="required"
                                        />
                                    </label>
                                    <label class="w-100 mb-2">
                                        <input class="form-control rounded-4"
                                               type="text"
                                               placeholder="{{__('Презиме')}}"
                                               value="{{old('middle_name')}}"
                                               name="middle_name"
                                        />
                                    </label>
                                    <label class="w-100">
                                        <input class="form-control rounded-4"
                                               type="text"
                                               placeholder="{{__('Фамилия')}}"
                                               value="{{old('last_name')}}"
                                               name="last_name"
                                               required="required"
                                        />
                                    </label>
                                </div>
                            </div>

                            <div class="col-12 col-md-6 order-2 order-md-3">

                                <label class="w-100 text-center mt-4">
                                    <span class="text-dark-gray text-steady-middle">{{__('Валидност на лична карта')}}</span>
                                    <div class="d-flex flex-row justify-content-between mt-1">

                                        <!-- Days -->
                                        <select class="form-control rounded-4 w-25 flex-grow-1 me-3 js-date"
                                                type="text"
                                                name="valid_date[d]"
                                                required="required"
                                                data-parsley-required-message=""
                                        >
                                            <option value="">{{__('Ден')}}</option>
                                            @for ($countDays = 1; $countDays <= 31; $countDays++)
                                                <option value="{{ ($countDays < 10 ? '0'. $countDays : $countDays) }}"
                                                        @if(intval(old('valid_date.d')) === $countDays) selected="selected" @endif
                                                >
                                                    {{ $countDays  }}
                                                </option>
                                            @endfor
                                        </select>

                                        <!-- Months -->
                                        <select class="form-control rounded-4 w-25 flex-grow-1 me-3 js-date"
                                                type="text" name="valid_date[m]"
                                                required="required"
                                                data-parsley-required-message=""
                                        >
                                            <option value="">{{__('Месец')}}</option>
                                            @for ($countMonths = 1; $countMonths <= 12; $countMonths++)
                                                <option value="{{ ($countMonths < 10 ? '0'. $countMonths : $countMonths) }}"
                                                        @if(intval(old('valid_date.m')) === $countMonths) selected="selected" @endif
                                                >
                                                    {{ $monthsMap[$countMonths] }}
                                                </option>
                                            @endfor
                                        </select>

                                        <!-- Year -->
                                        <input class="form-control rounded-4 w-25 flex-grow-1 js-date"
                                               type="text"
                                               placeholder="{{__('Година')}}"
                                               name="valid_date[y]"
                                               value="{{old('valid_date.y')}}"
                                               required="required"
                                               data-parsley-required-message=""
                                        />
                                    </div>
                                </label>

                                <div class="form-group mt-3 text-center">
                                    <label for="lifetime_idcard">Безсрочна лична карта</label>
                                    <select name="lifetime_idcard"
                                            id="lifetime_idcard"
                                            class="form-control"
                                            required="required"
                                    >
                                        <option value="0">Не</option>
                                        <option value="1">Да</option>
                                    </select>
                                </div>
                                <!-- End ./form-group -->

                            </div>

                            <div class="col-12 col-md-6 order-3 order-md-2">
                                <h2 class="fw-semibold text-regular text-center text-black mt-4 mt-md-0 mb-3">
                                    {{__('Адрес по лична карта')}}
                                </h2>

                                <label class="w-100 mb-2">
                                    <input class="form-control rounded-4" type="text"
                                           placeholder="{{__('Област')}}"
                                           name="district"
                                           required="required"
                                           value="{{old('district')}}"
                                    />
                                </label>

                                <label class="w-100 mb-2">
                                    <select name="city_id"
                                            class="form-control"
                                            required="required"
                                            data-parsley-errors-container="#city-id-errors"
                                    >
                                        @foreach($cities as $cityId => $cityName)
                                            @if(intval($cityId) === 99999)
                                                <option value="">{{$cityName}}</option>
                                            @else
                                                <option value="{{$cityId}}">{{$cityName}}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                    <div id="city-id-errors"></div>
                                </label>
                                <label class="w-100">
                                    <input class="form-control rounded-4" type="text"
                                           placeholder="Адрес: ул., No, бл."
                                           name="address"
                                           required="required"
                                           value="{{old('address')}}"
                                    />
                                </label>
                            </div>

                            <div class="col-12 col-md-6 order-4">
                                <div class="w-100 d-flex justify-content-center mt-5">
                                    <button type="submit"
                                            class="btn btn-primary d-block w-100 mw-250 rounded-pill btn-pink mx-auto ms-md-0 me-md-auto">
                                        {{__('Вземи парите')}}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                </section>
            </div>
        </div>
    </div>

    <div class="mt-5">
        <x-credit-info :creditInfo="$creditInfo" :header="$header"/>
    </div>

    <div class="d-block mt-4 mb-5">
        <x-document-download
                :templateType="'sef'"
                :templateText="'Изтегли СЕФ'"
                :loanData="$loan_data"
        />
    </div>
@endsection

@push('scripts')
    <script type="text/javascript">
        $(() => {

            $('select[name="lifetime_idcard"]').change(function () {
                if (parseInt($(this).val()) === 1) {
                    $('select[name="valid_date[d]"]').removeAttr('required');
                    $('select[name="valid_date[m]"]').removeAttr('required');
                    $('input[name="valid_date[y]"]').removeAttr('required');
                } else {
                    $('select[name="valid_date[d]"]').attr('required', 'required');
                    $('select[name="valid_date[m]"]').attr('required', 'required');
                    $('input[name="valid_date[y]"]').attr('required', 'required');
                }
            });

            $('select[name="city_id"]').select2({
                width: "100%"
            });
            $('form#third-step-form').submit(function () {
                $(this).find(":submit").prop("disabled", true);
            });
        });
    </script>
@endpush
