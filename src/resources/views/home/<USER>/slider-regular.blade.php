@php
    $url = route('register.request');
    if(auth()->check()){
        $url = route('newloan');
        $isLoggedOnWelcome = !empty($onWelcome);
    }

    $iban = isset($loan_data['iban']) ? $loan_data['iban'] : '';

    $targetProp = '';
    if (!empty($target)) {
        $targetProp = 'target="' . $target . '"';
    }
@endphp

<div class="py-3">

    <x-flash-messages/>

    <form id="new-app-form" action="{{ $url }}" method="POST" data-gather="payment_method_id,iban" data-parsley-validate="true"  {{$targetProp}}>
        @csrf

        <x-slider :hasPeriod="true" :sliderData="$productData['slider_data']" :product="$productData"/>

        @guest
            <div class="col-12 col-lg-8 mx-lg-auto mt-3">
                <label for="phone" class="w-100">
                    <input id="phone" type="tel"
                           class="form-control rounded-4 mb-2"
                           name="phone"
                           required="required"
                           placeholder="{{__('Въведи мобилен номер')}}"
                    />
                </label>
                <label class="d-flex align-items-center">
                    <input id="agreement"
                           type="checkbox"
                           class="form-check mr-10"
                           name="agreements[personal_data]"
                           required="required"
                    />
                    <small class="text-left text-gray fs-11 m-1">
                        {!! __('Декларирам, че приемам <a href=":1declaraciq">Декларацията</a> за обработка на личните ми данни и се съгласявам с <a href=":2declaraciq">Декларацията</a> за индивидуализация чрез email, <a href=":3declaraciq">Декларацията</a> по чл. 13 ал. 1 от ЗЕДЕП и <a href=":4declaracia">Декларацията</a> за възможно алтернативно финансиране.',[
                                '1declaraciq' => '/deklaraciya-za-obrabotvane-i-sahranyavane-na-lichni-danni-za-celite-na-sklyuchvane-i-izpalnenie-na-dogovor-za-kredit',
                                '2declaraciq' => '/deklaraciya-e-mail',
                                '3declaraciq' => '/deklaraciya-chl-13',
                                '4declaracia' => '/deklaraciya-ikt',
                        ]) !!}
                    </small>
                </label>
            </div>
        @endguest

        <div class="col-12 col-lg-8 mx-lg-auto mt-3">
            <x-insurance-message
                product-id="{{$productData['product_id']}}"
                :product-data="$productData"
                :has-insurance-action="null"
            />
        </div>

        <div class="text-center">
            @if(!auth()->check())
                <button type="submit"
                        class="submitBtnProfile btn btn-primary w-65 rounded-pill btn-pink mt-2 mb-3 {{$submitBtnClass??''}}"
                >
                    {{__('Кандидатствай')}}
                </button>
            @else
                <button type="submit"
                        class="submitBtnProfile btn btn-primary w-65 rounded-pill btn-pink mt-2 mb-3 {{$submitBtnClass??''}}"
                >
                    {{__('Кандидатствай')}}
                </button>
                <!-- End ./row -->
            @endif
        </div>
        <!-- End ./actions -->

        @auth
            <div class="d-flex justify-content-center align-items-center mt-30px">
                @include('partial.payment',['prefix'=>12,'iban' => $iban])
            </div>
        @endauth
    </form>
</div>
