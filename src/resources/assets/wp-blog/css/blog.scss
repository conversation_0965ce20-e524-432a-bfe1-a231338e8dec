.page-link {
  color: #ff6600 !important;
}

.wp-block-image {
  img {
    //height: 100% !important;
    //width: auto !important;
    max-width: 100%;
    height: auto;
  }
}

.page-link.active, .active > .page-link {
  color: #ffffff !important;
  background-color: #ff6600 !important;
  border-color: #ff6600 !important;
}

.wp-block-button__link {
  padding: 15px 30px !important;
  border-radius: 6px;
  background-color: #672bbe;
  text-decoration: none !important;
  color: #ffffff !important;
  line-height: 1rem !important;
}

.wp-block-buttons {
  padding: 15px 0px !important;
}

.wp-block-heading {
  font-size: 2rem;
}

.wp-block-ht-block-toc .ht_toc_placeholder {
  font-size: 18px
}

.wp-block-ht-block-toc[data-htoc-state=expanded] .htoc__itemswrap {
  margin-top: 1em;
  opacity: 1;
  max-height: 9999px
}

.wp-block-ht-block-toc[data-htoc-state=closed] .htoc__itemswrap {
  opacity: 0;
  max-height: 0;
  display: none
}

.wp-block-ht-block-toc.is-style-outline, .wp-block-ht-block-toc.is-style-gray, .wp-block-ht-block-toc.is-style-rounded, .wp-block-ht-block-toc.is-style-contrasted {
  -webkit-transition: all 1s ease-in-out;
  -o-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out
}

.wp-block-ht-block-toc.is-style-outline {
  border: 1px solid #ccc;
  padding: 2em
}

.wp-block-ht-block-toc.is-style-outline .htoc__toggle {
  border: 1px solid #ccc
}

.wp-block-ht-block-toc.is-style-gray {
  background: #dde2eb;
  padding: 2em
}

.wp-block-ht-block-toc.is-style-rounded {
  background-color: #fafafa;
  border: 1px solid #dfdfdf;
  border-radius: 10px;
  padding: 1.5em;
  margin-top: 20px;
  margin-bottom: 20px
}

.wp-block-ht-block-toc.is-style-contrasted {
  background-color: #fafafa;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 1.5em;
  margin-top: 20px;
  margin-bottom: 20px
}

.wp-block-ht-block-toc.is-style-contrasted li {
  padding: .5em;
  margin-bottom: 0
}

.wp-block-ht-block-toc.is-style-contrasted li:nth-child(odd) {
  background-color: rgba(0, 0, 0, .03);
  border-radius: 4px
}

.wp-block-ht-block-toc.is-style-contrasted li ul {
  margin-bottom: 0
}

.wp-block-ht-block-toc.htoc--position-left, .wp-block-ht-block-toc.htoc--position-right {
  max-width: 260px
}

.wp-block-ht-block-toc.htoc--position-center {
  text-align: center
}

.wp-block-ht-block-toc.htoc--position-left {
  float: left;
  margin-right: 2em
}

.wp-block-ht-block-toc.htoc--position-right {
  float: right;
  margin-left: 2em
}

.wp-block-ht-block-toc .htoc__title {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  font-size: 1em;
  font-weight: 600
}

.wp-block-ht-block-toc .htoc__toggle {
  margin-left: 15px;
  background: #fff;
  padding: 8px;
  border-radius: 2px;
  line-height: 1em;
  cursor: pointer;
  position: relative
}

.wp-block-ht-block-toc .htoc__toggle svg {
  display: -ms-inline-flexbox;
  display: inline-flex
}

.wp-block-ht-block-toc ul, .wp-block-ht-block-toc ol {
  margin: 0;
  padding: 0;
  list-style-position: inside
}

.wp-block-ht-block-toc ul ul, .wp-block-ht-block-toc ul ol, .wp-block-ht-block-toc ol ul, .wp-block-ht-block-toc ol ol {
  padding: 0;
  margin: .5em 0 .5em 1em
}

.wp-block-ht-block-toc ul li, .wp-block-ht-block-toc ol li {
  margin-bottom: .5em
}

.wp-block-ht-block-toc ul li:last-child, .wp-block-ht-block-toc ol li:last-child {
  margin-bottom: 0
}

.wp-block-ht-block-toc ul {
  list-style-type: disc
}

.wp-block-ht-block-toc ol {
  list-style-type: decimal
}

.wp-block-ht-block-toc.toc-list-style-plain ul, .wp-block-ht-block-toc.toc-list-style-plain ol {
  list-style-type: none
}

.ht-toc-clear {
  clear: both;
  width: 0;
  height: 0
}

/*******************/

.wp-block-group .block-editor-block-list__insertion-point {
  left: 0;
  right: 0;
}

[data-type="core/group"].is-selected .block-list-appender {
  margin-left: 0;
  margin-right: 0;
}

[data-type="core/group"].is-selected .has-background .block-list-appender {
  margin-bottom: 18px;
  margin-top: 18px;
}

.wp-block-group.is-layout-flex.block-editor-block-list__block > .block-list-appender:only-child {
  gap: inherit;
  pointer-events: none;
}

.wp-block-group.is-layout-flex.block-editor-block-list__block > .block-list-appender:only-child, .wp-block-group.is-layout-flex.block-editor-block-list__block > .block-list-appender:only-child .block-editor-default-block-appender__content, .wp-block-group.is-layout-flex.block-editor-block-list__block > .block-list-appender:only-child .block-editor-inserter {
  display: inherit;
  flex: 1;
  flex-direction: inherit;
  width: 100%;
}

.wp-block-group.is-layout-flex.block-editor-block-list__block > .block-list-appender:only-child:after {
  border: 1px dashed;
  border-radius: 2px;
  content: "";
  display: flex;
  flex: 1 0 48px;
  min-height: 46px;
  pointer-events: none;
}

.wp-block-group.is-layout-flex.block-editor-block-list__block > .block-list-appender:only-child:after:before {
  background: currentColor;
  bottom: 0;
  content: "";
  left: 0;
  opacity: .1;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
}

.wp-block-group.is-layout-flex.block-editor-block-list__block > .block-list-appender:only-child .block-editor-inserter {
  pointer-events: all;
}

.wp-block-group__placeholder .wp-block-group-placeholder__variations {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
}

.wp-block-group__placeholder .components-placeholder__instructions {
  margin-bottom: 18px;
  text-align: center;
}

.wp-block-group__placeholder .wp-block-group-placeholder__variations svg {
  fill: #ccc !important;
}

.wp-block-group__placeholder .wp-block-group-placeholder__variations > li {
  align-items: center;
  display: flex;
  flex-direction: column;
  margin: 0 12px 12px;
  width: auto;
}

.wp-block-group__placeholder .wp-block-group-placeholder__variations li > .wp-block-group-placeholder__variation-button {
  height: 32px;
  padding: 0;
  width: 44px;
}

.wp-block-group__placeholder .wp-block-group-placeholder__variations li > .wp-block-group-placeholder__variation-button:hover {
  box-shadow: none;
}

.wp-block-group__placeholder .components-placeholder {
  min-height: auto;
  padding: 24px;
}

.wp-block-group__placeholder .is-medium .wp-block-group-placeholder__variations > li, .wp-block-group__placeholder .is-small .wp-block-group-placeholder__variations > li {
  margin: 12px;
}

/*******************/

.wp-block-columns {
  align-items: normal !important;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap !important;
  margin-bottom: 1.75em;
}

@media (min-width: 782px) {
  .wp-block-columns {
    flex-wrap: nowrap !important;
  }
}

.wp-block-columns.are-vertically-aligned-top {
  align-items: flex-start;
}

.wp-block-columns.are-vertically-aligned-center {
  align-items: center;
}

.wp-block-columns.are-vertically-aligned-bottom {
  align-items: flex-end;
}

@media (max-width: 781px) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    padding: 20px !important;
    flex-basis: 100% !important;
  }
}

@media (min-width: 782px) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    padding: 20px !important;
    flex-basis: 0;
    flex-grow: 1;
  }

  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column[style*=flex-basis] {
    flex-grow: 0;
  }
}

.wp-block-columns.is-not-stacked-on-mobile {
  flex-wrap: nowrap !important;
}

.wp-block-columns.is-not-stacked-on-mobile > .wp-block-column {
  padding: 20px !important;
  flex-basis: 0;
  flex-grow: 1;
}

.wp-block-columns.is-not-stacked-on-mobile > .wp-block-column[style*=flex-basis] {
  flex-grow: 0;
}

:where(.wp-block-columns.has-background) {
  padding: 1.25em 2.375em;
}

.wp-block-column {
  flex-grow: 1;
  min-width: 0;
  overflow-wrap: break-word;
  word-break: break-word;
  padding: 20px !important;
}

.wp-block-column.is-vertically-aligned-top {
  align-self: flex-start;
}

.wp-block-column.is-vertically-aligned-center {
  align-self: center;
}

.wp-block-column.is-vertically-aligned-bottom {
  align-self: flex-end;
}

.wp-block-column.is-vertically-aligned-bottom, .wp-block-column.is-vertically-aligned-center, .wp-block-column.is-vertically-aligned-top {
  width: 100%;
}
