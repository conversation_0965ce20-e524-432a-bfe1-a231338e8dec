@import "bootstrap/scss/bootstrap";
@import "custom-variables";
@import "./customs/main";
@import "./customs/slider.css";
@import "../plugins/select2/select2.css";
@import "./customs/plugins/bootstrap-datepicker3.css";
@import "./customs/calendar.css";
@import "../wp-blog/css/blog.scss";
@import "@fortawesome/fontawesome-free/css/all.min.css";

.disabled {
    pointer-events: none;
    opacity: 0.6;
    cursor: not-allowed;
}

.mb-30px {
  margin-bottom: 30px;
}

.mb-15px {
  margin-bottom: 15px;
}

.mt-30px {
  margin-top: 30px;
}

.mt-15px {
  margin-top: 15px;
}

.pb-30px {
  padding-bottom: 30px !important;
}

.pb-15px {
  padding-bottom: 15px !important;
}

.pt-30px {
  padding-top: 30px !important;
}

.pt-15px {
  padding-top: 15px !important;
}

@media (min-width: 992px) {
  .h-lg-120 {
    height: 120px !important;
  }
  .h-lg-160 {
    height: 160px !important;
  }
}

.calculator-layout {
  .btn {
    font-size: 0.8rem;
  }
}

article {
  a {
    text-decoration: none;
  }

  div {
    color: #000000 !important;
  }
}

.mr-10 {
  margin-right: 10px !important;
}

#menu-mobile {
  background: #ADBEFF !important;

  a, span {
    color: #ffffff !important;
  }

  #menu-mobile-list {
    background: #ffffff;

    li > a {
      color: #000000 !important;
    }
  }

  //#menu-mobile-button-hidden {
  //  margin-right: 20px !important;
  //}
}

@media (max-width: 650px) {
  .navbar {
    height: 45px;
    padding-top: 0px !important;
  }

  #menu-mobile {
    .navbar {
      padding-bottom: 0px !important;
    }
  }
}

.mt-30px {
  margin-top: 30px !important;
}

.form-check {
  margin-bottom: 0px !important;
}

.no-radius {
  border-radius: 0px !important;
}

.section-title {
  @extend .h2, .fw-bold, .text-center, .mb-4;
}

.client-name {
  @extend .h4, .fw-bold, .text-end, .text-white;
}

.loan-step-title {
  @extend .h5, .fw-bold, .text-lendivo-secondary;
}

.contact-title {
  @extend .h6, .fw-bold;
}

/* Custom CSS for full-width modal on mobile */
@media (max-width: 992px) {
  .modal {
    max-width: 100%;
  }

  .modal-dialog {
    margin: 0;
    max-width: 100%;
    width: 100%;
  }

  .modal-content {
    border: 0;
    border-radius: 0;
  }
}

.read-more a {
  color: #ff6600 !important;
}

.useful-predications {

  /* Style the scrollbar track */
  ::-webkit-scrollbar {
    width: 0px; /* Width of the scrollbar track */
  }

  /* Style the scrollbar thumb (the part you drag) */
  ::-webkit-scrollbar-thumb {
    background: #FFF; /* Color of the scrollbar thumb */
    width: 0px; /* Rounded corners for the thumb */
  }

  /* Style the scrollbar track on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #FFF; /* Color on hover */
  }

  /* Style the scrollbar track on active (when clicking and dragging) */
  ::-webkit-scrollbar-thumb:active {
    background: #FFF; /* Color on active state */
  }
}

.alert-danger {
  background-color: #FB637E;
  color: #FFFFFF;
}

.rounded-30 {
  border-radius: 30px !important;
}

input.parsley-success,
select.parsley-success,
textarea.parsley-success {
  color: #468847;
  background-color: #DFF0D8;
  border: 1px solid #D6E9C6;
}

input.parsley-error,
select.parsley-error,
textarea.parsley-error {
  color: #FB637E;
  //background-color: #F2DEDE;
  border: 1px solid #FB637E;
}

.parsley-errors-list {
  margin: 2px 0 3px;
  padding: 0;
  list-style-type: none;
  font-size: 12px;
  line-height: 0.9em;
  opacity: 0;
  color: #FB637E;
  font-weight: bold;

  transition: all .3s ease-in;
  -o-transition: all .3s ease-in;
  -moz-transition: all .3s ease-in;
  -webkit-transition: all .3s ease-in;
}

.parsley-errors-list.filled {
  opacity: 1;
}

.white-footer-link-with-img {
  margin-left: -30px;
  margin-top: -20px;
}

.subsection h2 {
  font-size: 1.7rem;
}

@media (max-width: 1190px) {
  #profile-dd.ms-3 {
    margin-left: 0 !important; /* Negate the effect of ms-3 for mobile */
  }

  main.container {
    padding-top: 15px;
  }
}

@media (max-width: 420px) {
  ul[role="tablist"] a.nav-link {
    padding-right: 5px;
    padding-left: 5px;
  }
  small {
    font-size: .775em;
  }
}

// mobile-menu
#menu-mobile-button {
  background-color: transparent;
  border: 10px;
  color: black;
  cursor: pointer;
  //margin-left: 20px;
  float: left;
  display: none;
  //padding: 0px 17px;
}

#menu-mobile-button-hidden {
  background-color: transparent;
  visibility: hidden
}

#menu-mobile-btns {
  display: flex;
  justify-content: space-between;
  //padding: 8px 0px;
}

#menu-mobile-list {
  padding-left: 16px;
}

#menu-mobile-btns span {
  display: flex;
  align-items: center;
  font-size: 1rem !important;
  font-weight: bold;
  color: #9881f4;
}

#menu-bar-img {
  height: 30px;
}


#menu-mobile-button:focus {
  outline: none;
}

#menu-mobile {
  background-color: #eeeeee;;
  flex-wrap: wrap;
  display: none;
}

#menu-mobile ul {
  display: flex;
  justify-content: center;
  list-style: none
}

#menu-mobile ul li {
  padding: 10px;
}

#menu-mobile ul li a {
  color: #9881f4;
  text-decoration: none;
}

#menu-mobile ul li:hover {
  text-decoration: underline;
  text-decoration-color: red;
}

@media (max-width: 1200px) {

  #menu-mobile {
    display: block;
    background-color: #eeeeee;
  }

  #menu-mobile-button {
    display: block;
  }

  #menu-mobile ul {
    display: none;
    flex-direction: column;
    width: 100%;
  }

  #menu-mobile ul.show {
    display: flex;
  }

  //#menu-mobile:not(.show) {
  //  height: 50px;
  //}
  #menu-mobile.show {
    height: auto;
  }
}


.arrow {
    transition: transform 0.3s ease;
}
.arrow.open {
    transform: rotate(90deg) !important;
}

.cursor-pointer{
    cursor: pointer !important;
}
