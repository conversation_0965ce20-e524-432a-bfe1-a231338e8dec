input[type=range] {
    width: 100%;
    margin: 7.9px 0;
    background-color: transparent;
    -webkit-appearance: none;
}

input[type=range]:focus {
    outline: none;
}

input[type=range]::-webkit-slider-runnable-track {
    background: rgba(164, 181, 255, 0.9);
    border: 0;
    border-radius: 25px;
    width: 100%;
    height: 4.2px;
    cursor: pointer;
}

input[type=range]::-webkit-slider-thumb {
    margin-top: -7.9px;
    width: 20px;
    height: 20px;
    background: #ffffff;
    border: 5.9px solid #e855d8;
    border-radius: 50px;
    cursor: pointer;
    -webkit-appearance: none;
}

input[type=range]:focus::-webkit-slider-runnable-track {
    background: #becaff;
}

input[type=range]::-moz-range-track {
    background: rgba(164, 181, 255, 0.9);
    border: 0;
    border-radius: 25px;
    width: 100%;
    height: 4.2px;
    cursor: pointer;
}

input[type=range]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #ffffff;
    border: 5.9px solid #e855d8;
    border-radius: 50px;
    cursor: pointer;
}

input[type=range]::-ms-track {
    background: transparent;
    border-color: transparent;
    border-width: 8.9px 0;
    color: transparent;
    width: 100%;
    height: 4.2px;
    cursor: pointer;
}

input[type=range]::-ms-fill-lower {
    background: #8aa0ff;
    border: 0;
    border-radius: 50px;
}

input[type=range]::-ms-fill-upper {
    background: rgba(164, 181, 255, 0.9);
    border: 0;
    border-radius: 50px;
}

input[type=range]::-ms-thumb {
    width: 20px;
    height: 20px;
    background: #ffffff;
    border: 5.9px solid #e855d8;
    border-radius: 50px;
    cursor: pointer;
    margin-top: 0px;
    /*Needed to keep the Edge thumb centred*/
}

input[type=range]:focus::-ms-fill-lower {
    background: rgba(164, 181, 255, 0.9);
}

input[type=range]:focus::-ms-fill-upper {
    background: #becaff;
}

@supports (-ms-ime-align:auto) {
    /* Pre-Chromium Edge only styles, selector taken from hhttps://stackoverflow.com/a/32202953/7077589 */
    input[type=range] {
        margin: 0;
        /*Edge starts the margin from the thumb, not the track as other browsers do*/
    }
}

@media (max-width: 1190px) {
    input[type=range]::-webkit-slider-thumb {
        margin-top: -10.2px;
        width: 25px;
        height: 25px;
        background: #ffffff;
        border: 6.9px solid #e855d8;
        border-radius: 50px;
        cursor: pointer;
        -webkit-appearance: none;
    }

    input[type=range]::-moz-range-thumb {
        margin-top: -10.2px;
        width: 25px;
        height: 25px;
        background: #ffffff;
        border: 6.9px solid #e855d8;
        border-radius: 50px;
        cursor: pointer;
        -webkit-appearance: none;
    }

    input[type=range]::-ms-thumb {
        margin-top: -10.2px;
        width: 25px;
        height: 25px;
        background: #ffffff;
        border: 6.9px solid #e855d8;
        border-radius: 50px;
        cursor: pointer;
        -webkit-appearance: none;
        /*Needed to keep the Edge thumb centred*/
    }
}
