.text {

    // Colors
    &-black {
        color: $black;
    }

    &-lendivo-primary {
        color: $purpleHover;
    }
    
    &-lendivo-secondary {
        color: $lendivoSecondary;

        &:hover,
        &:focus {
            color: $purpleHover;
        }
    }

    &-decoration-gray {
        text-decoration-color: $purpleHover;
    }    

    &-pink {
        color: $pink;
    }

    &-gray {
        color: $gray;
    }
    
    &-dark-gray {
        color: $darkGray;
    }

    &-danger {
        font-weight: normal !important;
        color: $danger !important;
    }

    &-purple {
        color: $purpleText;
    }

    // Alignments
    &-left {
        text-align: left!important;
    }

    &-md-left {

        @media (min-width: $md) {
            text-align: left!important;
        }
    }

    &-lg-left {

        @media (min-width: $lg) {
            text-align: left!important;
        }
    }
    
    // Typography
    &-title {
        font-size: 22px;

        @media (min-width: $md) {
            font-size: 32px;
        }
    }

    &-steady-title {
        font-size: 20px;
    }

    &-steady {
        font-size: 18px;
    }

    &-steady-middle {
        font-size: 16px;
    }

    &-regular {
        font-size: 18px;

        @media (min-width: $md) {
            font-size: 20px;
        }
    }

    &-middle {
        font-size: 16px;

        @media (min-width: $md) {
            font-size: 18px;
        }
    }

    &-middle-regular {
        font-size: 16px;

        @media (min-width: $md) {
            font-size: 20px;
        }
    }
    
    &-default {
        font-size: 14px;

        @media (min-width: $md) {
            font-size: 16px;
        }
    }

    &-micro {
        font-size: 14px;
    }

    // Styles
    &-italic {
        font-style: italic;
    }
}