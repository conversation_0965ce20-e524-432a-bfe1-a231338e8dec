.js-list {

    &-button {
        z-index: 2;
        position: relative;

        &.disabled { cursor: default !important; }

        &.job-link {
            text-decoration: none;
            border: 1px solid transparent;

            &:not(.disabled) {
                &:hover,
                &:focus {
                    border-color: $lendivoSecondary;

                    .js-list-more {
                        padding-right: 28px;

                        &:after {
                            transform: translate(10%,-50%) translateZ(0);
                        }
                    }
                }
            }
        }
    }

    &-jobs {
        margin-top: -8px;
    }

    &-more {
        display: block;
        text-decoration: none;
        border: 2px solid;
        border-radius: 100px;
        text-transform: uppercase;
        font-size: 12px;
        font-weight: 600;
        padding: 0px 8px;
        background-color: $white;
        position: relative;
        overflow: hidden;
    
        &:after {
            content: " ";
            display: inline-block;
            width: 20px;
            height: 20px;
            position: absolute;
            top: 50%;
            right: 0;
            transform: translate(100%, -50%) translateZ(0);
            border: 2px solid;
            border-color: $purpleBg;
            border-radius: 100%;
            background-color: $purpleLight;
            background-image: url("/static/images/arrow-r-i.svg");
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
        }
    }

    .open {

        .icon {
            transform: rotate(180deg);
        }
    }

    &.benefit {
        width: 100%;

        @media (min-width: $md) {
            width: calc(50% - 16px);
        }
    }
}