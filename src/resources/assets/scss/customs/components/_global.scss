.white-space-pre-line {
    white-space: pre-line;
}

.contact-icon-2 {
    width: 2.5rem;
}

.lower-footer-colour {
    background-color: #333333 !important;
}

.pointer {
    cursor: pointer !important;
}

#cover {
    min-width: 100%;
    min-height: 100%;
    background-color: rgba(0,0,0,.35);
    position: absolute;
    z-index: 3;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.nav-tab-rounded {
    border-radius: 10px 10px 0 0!important;
}

.nav-content-rounded {
    border-radius: 0 0 10px 10px!important;
}

.footer {
    border-radius: 30px 30px 0 0;
}

#phone::-webkit-input-placeholder {
    color: #BDBDBD;
}

.lendivo-dropdown {
    width: 100%;
    position: absolute !important;
    z-index: 3;
    overflow: hidden;
    right: 0;
    
    @media (min-width: $lg) {
        max-width: 213px;
        margin-right: 12px;
    }
}

.on-top {
    z-index: 2 !important;
}

.calculator-layout.tab-pane.active {
    background-color: $white;
    box-shadow: 0 .125rem .4rem rgba(0,0,0,.25)!important
}

.nav-tabs {
    border: none;
}
.nav-item {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;

    &:first-of-type {

        .nav-link {

            &:after {
                left: 0;
            }
        }
    }

    &:last-of-type {

        .nav-link {

            &:after {
                right: 0;
            }
        }
    }
}
.nav-link {
    border: none !important;
    background-color: $purpleLighest !important;
    padding-top: 12px;
    padding-bottom: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100% - 4px);
}
.nav-link.active {
    position: relative;
    background-color: $white !important;
    box-shadow: 0rem .125rem .4rem 0 rgba(0,0,0,.25)!important;
    z-index: 2;
    padding-top: 24px;
    padding-bottom: 24px;
    height: 100%;

    &:after {
        content: " ";
        display: block;
        position: absolute;
        width: calc(100% + 0.4rem);
        height: 0.5rem;
        background: $white;
        bottom: 0;
        transform: translateY(100%);
        box-shadow: none;
    }
}

.menu-open {

    .nav-link {

        &.active {
            z-index: 1 !important;
        }
    }
}

.tab-content {
    position: relative;
}

.out-tech-container {
    margin-right: -12px;

    .our-tech {
        width: 64px;
        height: 64px;
        margin-right: 12px;
    
        @media (min-width: $md) {
            width: 88px;
            height: 88px;
        }
    
        img {
            filter: grayscale(1);
        }
    }
}

.invisible-anchor {
    text-decoration: none;
    outline: 0;
    text-shadow: none;
    box-shadow: none;
}

.image-gallery {

    &-item {
        height: 100%;
        margin-bottom: 24px;

        .col-12 {
            height: 100%;
        }
    
        img {
            display: block;
            object-fit: cover;
            height: 100%;
            min-height: 168px;
            max-height: 552px;
            background-color: red;
            width: 100%;
            filter: grayscale(1);

            &:hover {
                filter: grayscale(0);
            }
        }

        &.semi {
            height: 168px !important;
        }
    
        &:last-of-type {
            margin-bottom: 0 !important;
        }
    }
}

.align-items-normal {
    align-items: normal !important;
}

.desktop-header-menu {

    a {
        text-decoration: none;
        outline: 0;
        margin-right: 40px;

        &:last-of-type {
            margin-right: 0;
        }

        &:hover {
            color: $lendivoSecondary;
        }
    }
}

.quick-link-section {
    border-bottom: 1px solid $purple;

    @media (min-width: $lg) {
        border-bottom: none !important;
    }
    
    &:first-of-type {
        margin-top: 0 !important;
    }
}

.payment-method-container  {
    width: 85%;

    @media (min-width: $md) {
        width: 45%;
    }

    @media (min-width: $lg) {
        width: 55%;
    }

    @media (min-width: $xl) {
        width: 45%;
    }

    .js-iban-container,
    label {
        width: 100%;
    }
}

.js-date {
    text-align: center;
}

.lendivo-notification {
    display: block;
    padding: 14px 0 0;
    font-size: 14px;

    @media (min-width: $lg) {
        font-size: 16px;
    }

    &.all {
        color: $purpleText;
        padding-bottom: 14px;
    }

    input[type="checkbox"] {
        position: relative;
        width: 16px;
        height: 16px;
        border-radius: 3px;
        overflow: hidden;
        margin-right: 8px;
        vertical-align: middle;

        &:checked:before {
            opacity: 0;
        }

        &:before {
            content: " ";
            display: inline-block;
            opacity: 1;
            background: #fff;
            border: 1px solid $gray;
            width: 100%;
            height: 100%;
            position: absolute;
            border-radius: 3px;
        }
    }

    span {
        display: inline-block;
        vertical-align: middle;
    }
}

.lendivo-modal {

    &-container {
        display: flex;
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, .35);
        z-index: 9;
        padding: 16px;
        align-items: flex-start;
        justify-content: center;
    }

    &-inner {
        display: block;
        width: 100%;
        max-width: 620px;
        border-radius: 10px;
        background: white;

        @media (min-width: $lg) {
            border-radius: 30px !important;
        }
    }
}