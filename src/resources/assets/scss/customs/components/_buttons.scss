.btn {
    font-weight: 600;
    padding-top: 9px;
    padding-bottom: 8px;
    border-radius: 100px;
    outline: 0;
    box-shadow: none;

    &:focus,
    &:active {
        outline: 0;
        box-shadow: none !important;
    }

    &-floating {
        margin: .5rem;
    }

    &-purple {
        border-color: $purpleBg;
        background-color: $purpleBg;
    }

    &-purple-outline {
        border-color: $purpleBg;
        color: $purpleBg;

        &:hover,
        &:focus {
            border-color: $purpleHover !important;
            color: $purpleHover !important;
        }
    }

    &-pink-outline {
        color: $pink;
        border-color: $pink;

        &:hover,
        &:focus,
        &.active {
            background-color: $pink!important;
            border-color: $pink!important;
        }
    }

    &-pink {
        color: $white !important;
        background-color: $pink;
        border: none;

        &:hover,
        &:focus {
            background-color: $pinkHover;
        }
    }

    &-circle {
        max-width: 0.8rem !important;
        max-height: 0.8rem !important;
        min-width: 0.8rem !important;
        min-height: 0.8rem !important;
        border-radius: 20rem;
    }

    &-list {
        border: none;
        cursor: pointer;

        @extend .bg-white;
        @extend .shadow-thick;
        @extend .rounded-10;

        &.open {

            .js-list-button-icon {
                transform: rotate(180deg) translateZ(0);
            }
        }
    }
    
    &-icon {
        font-weight: 600;
        text-align: center;
        padding: 9px 56px 8px;
        border-radius: 100px;

        .icon {
            border-radius: 100%;
            object-fit: cover;
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translate(-0, -50%) translateZ(0);
        }
    }

    &:disabled {
        background-color: $gray !important;
        border-color: $gray !important;
    }
}
