.calendar {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    width: 100%;
    margin: 0 auto;
    overflow: hidden;

    .label-container {
        margin-top: 10px;
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        font-weight: 600;
        order: 4;
        position: absolute;
        bottom: 0;
        left: 0;
        transform: translateY(100%);
        padding: 24px 16px 0;
        font-size: 18px;

        span {
            display: inline-block;
            flex-grow: 1;
        }
    }
    
    .header {
        width: 100%;
        background: $lendivoSecondary;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        text-align: center;
        position:relative;
        z-index: 100;
        margin-left: auto;
        margin-right: auto;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
        padding: 8px 16px;
        margin-bottom: calc(48px + 18px);

        .calendar-actions {
            order: 2;

            img {
                cursor: pointer;
            }
        }
    }
    
    .header h1 {
        margin: 0;
        padding: 0;
        font-size: 18px;
        line-height: 50px;
        font-weight: 600;
        color: $white;
        order: 1;
        flex-grow: 1;
        text-align: left;

        @media (min-width: $lg) {
            font-size: 28px;
        }
    }

    .weekend {
        color: $lendivoSecondary;
    }    
    
    .left, .right {
        position: absolute;
        width: 0px;
        height: 0px;
        border-style: solid;
        top: 50%;
        cursor: pointer;
        transform: translateY(-50%);
    }
    
    .left {
        border-width: 7.5px 10px 7.5px 0;
        border-color: transparent rgba(160, 159, 160, 1) transparent transparent;
        right: calc(3 * 16px);
        order: 2
    }
    
    .right {
        border-width: 7.5px 0 7.5px 10px;
        border-color: transparent transparent transparent rgba(160, 159, 160, 1);
        right: 16px;
        order: 3;
    }
    
    .month {
        /*overflow: hidden;*/
        opacity: 0;
        width: 100% !important;
        padding: 0 16px 24px;
    }
    
    .month.new {
        -webkit-animation: fadeIn 1s ease-out;
        opacity: 1;
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;
        margin-top: 30px;
        width: 100% !important;
    }
    
    .month.in.next {
        -webkit-animation: moveFromTopFadeMonth .4s ease-out;
        -moz-animation: moveFromTopFadeMonth .4s ease-out;
        animation: moveFromTopFadeMonth .4s ease-out;
        opacity: 1;
    }
    
    .month.out.next {
        -webkit-animation: moveToTopFadeMonth .4s ease-in;
        -moz-animation: moveToTopFadeMonth .4s ease-in;
        animation: moveToTopFadeMonth .4s ease-in;
        opacity: 1;
    }
    
    .month.in.prev {
        -webkit-animation: moveFromBottomFadeMonth .4s ease-out;
        -moz-animation: moveFromBottomFadeMonth .4s ease-out;
        animation: moveFromBottomFadeMonth .4s ease-out;
        opacity: 1;
    }
    
    .month.out.prev {
        -webkit-animation: moveToBottomFadeMonth .4s ease-in;
        -moz-animation: moveToBottomFadeMonth .4s ease-in;
        animation: moveToBottomFadeMonth .4s ease-in;
        opacity: 1;
    }
    
    .week {
        background: transparent;
        display: flex;
        align-content: center;
        justify-content: center;
    }
    
    .day {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0;
        text-align: center;
        vertical-align: top;
        cursor: pointer;
        margin: 0 auto;
        background: transparent;
        position: relative;
        z-index: 100;
    }
    
    .disabled {
        pointer-events: none;
        color: gray;
    }
    
    .day.other {
        color: $mediumGray;
    }
    
    .day-name {
        display: none;
    }
    
    .day-number {
        display: flex;
        width: 30px;
        height: 30px;
        font-size: 16px !important;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .today {

        .day-number {
            background-color: $lendivoSecondary;
            border-radius: 100%;
            color: white;
        }
    }
    
    
    .day .day-events {
        list-style: none;
        margin-top: 3px;
        text-align: center;
        height: 12px;
        line-height: 6px;
        overflow: hidden;
    }
    
    .day .day-events span {
        vertical-align: top;
        display: inline-block;
        padding: 0;
        margin: 0;
        width: 5px;
        height: 5px;
        line-height: 5px;
        margin: 0 1px;
    }
    
    .blue { background: rgba(156, 202, 235, 1); }
    .orange { background: rgba(247, 167, 0, 1); }
    .green { background: rgba(153, 198, 109, 1); }
    .yellow { background: rgba(249, 233, 0, 1); }
    
    .details.in {
        -webkit-animation: moveFromTopFade .5s ease both;
        -moz-animation: moveFromTopFade .5s ease both;
        animation: moveFromTopFade .5s ease both;
    }
    
    .details.out {
        -webkit-animation: moveToTopFade .5s ease both;
        -moz-animation: moveToTopFade .5s ease both;
        animation: moveToTopFade .5s ease both;
    }
    
    .arrow {
        position: absolute;
        top: -5px;
        left: 50%;
        margin-left: -2px;
        width: 0px;
        height: 0px;
        border-style: solid;
        border-width: 0 5px 5px 5px;
        border-color: transparent transparent rgba(164, 164, 164, 1) transparent;
        transition: all 0.7s ease;
    }
    
    .events {
        height: 75px;
        padding: 7px 0;
        overflow-y: auto;
        overflow-x: hidden;
    }
    
    .events.in {
        -webkit-animation: fadeIn .3s ease both;
        -moz-animation: fadeIn .3s ease both;
        animation: fadeIn .3s ease both;
    }
    
    .events.in {
        -webkit-animation-delay: .3s;
        -moz-animation-delay: .3s;
        animation-delay: .3s;
    }
    
    .details.out .events {
        -webkit-animation: fadeOutShrink .4s ease both;
        -moz-animation: fadeOutShink .4s ease both;
        animation: fadeOutShink .4s ease both;
    }
    
    .events.out {
        -webkit-animation: fadeOut .3s ease both;
        -moz-animation: fadeOut .3s ease both;
        animation: fadeOut .3s ease both;
    }
    
    .event {
        font-size: 16px;
        line-height: 22px;
        letter-spacing: .5px;
        padding: 2px 16px;
        vertical-align: top;
    }
    
    .event.empty {
        color: #eee;
    }
    
    .event-category {
        height: 10px;
        width: 10px;
        display: inline-block;
        margin: 6px 0 0;
        vertical-align: top;
    }
    
    .event span {
        display: inline-block;
        padding: 0 0 0 7px;
    }
    
    .legend {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 30px;
        background: transparent;
        line-height: 30px;
    
    }
    
    .entry {
        position: relative;
        padding: 0 0 0 25px;
        font-size: 13px;
        display: inline-block;
        line-height: 30px;
        background: transparent;
    }
    
    .entry:after {
        position: absolute;
        content: '';
        height: 5px;
        width: 5px;
        top: 12px;
        left: 14px;
    }
    
    .entry.blue:after { background: rgba(156, 202, 235, 1); }
    .entry.orange:after { background: rgba(247, 167, 0, 1); }
    .entry.green:after { background: rgba(153, 198, 109, 1); }
    .entry.yellow:after { background: rgba(249, 233, 0, 1); }
}

/* Animations are cool!  */
@-webkit-keyframes moveFromTopFade {
    from { opacity: .3; height:0px; margin-top:0px; -webkit-transform: translateY(-100%); }
}
@-moz-keyframes moveFromTopFade {
    from { height:0px; margin-top:0px; -moz-transform: translateY(-100%); }
}
@keyframes moveFromTopFade {
    from { height:0px; margin-top:0px; transform: translateY(-100%); }
}

@-webkit-keyframes moveToTopFade {
    to { opacity: .3; height:0px; margin-top:0px; opacity: 0.3; -webkit-transform: translateY(-100%); }
}
@-moz-keyframes moveToTopFade {
    to { height:0px; -moz-transform: translateY(-100%); }
}
@keyframes moveToTopFade {
    to { height:0px; transform: translateY(-100%); }
}

@-webkit-keyframes moveToTopFadeMonth {
    to { opacity: 0; -webkit-transform: translateY(-30%) scale(.95); }
}
@-moz-keyframes moveToTopFadeMonth {
    to { opacity: 0; -moz-transform: translateY(-30%); }
}
@keyframes moveToTopFadeMonth {
    to { opacity: 0; -moz-transform: translateY(-30%); }
}

@-webkit-keyframes moveFromTopFadeMonth {
    from { opacity: 0; -webkit-transform: translateY(30%) scale(.95); }
}
@-moz-keyframes moveFromTopFadeMonth {
    from { opacity: 0; -moz-transform: translateY(30%); }
}
@keyframes moveFromTopFadeMonth {
    from { opacity: 0; -moz-transform: translateY(30%); }
}

@-webkit-keyframes moveToBottomFadeMonth {
    to { opacity: 0; -webkit-transform: translateY(30%) scale(.95); }
}
@-moz-keyframes moveToBottomFadeMonth {
    to { opacity: 0; -webkit-transform: translateY(30%); }
}
@keyframes moveToBottomFadeMonth {
    to { opacity: 0; -webkit-transform: translateY(30%); }
}

@-webkit-keyframes moveFromBottomFadeMonth {
    from { opacity: 0; -webkit-transform: translateY(-30%) scale(.95); }
}
@-moz-keyframes moveFromBottomFadeMonth {
    from { opacity: 0; -webkit-transform: translateY(-30%); }
}
@keyframes moveFromBottomFadeMonth {
    from { opacity: 0; -webkit-transform: translateY(-30%); }
}

@-webkit-keyframes fadeIn  {
    from { opacity: 0; }
}
@-moz-keyframes fadeIn  {
    from { opacity: 0; }
}
@keyframes fadeIn  {
    from { opacity: 0; }
}

@-webkit-keyframes fadeOut  {
    to { opacity: 0; }
}
@-moz-keyframes fadeOut  {
    to { opacity: 0; }
}
@keyframes fadeOut  {
    to { opacity: 0; }
}

@-webkit-keyframes fadeOutShink  {
    to { opacity: 0; padding: 0px; height: 0px; }
}
@-moz-keyframes fadeOutShink  {
    to { opacity: 0; padding: 0px; height: 0px; }
}
@keyframes fadeOutShink  {
    to { opacity: 0; padding: 0px; height: 0px; }
}
