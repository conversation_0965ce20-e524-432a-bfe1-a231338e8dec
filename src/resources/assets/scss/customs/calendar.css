div.datepicker-inline {
    width: auto !important;
}

div.datepicker-days {
    border-radius: 8px;
    box-shadow: 0 0.125rem 0.4rem rgba(0, 0, 0, 0.25) !important;
    padding-bottom: 20px;
}

table.table-condensed {
    width: 100% !important;
    border-radius: 8px;
}

table.table-condensed tbody tr td {
    background: #ffffff !important;
    /*border-radius: 100% !important;*/
}

table.table-condensed td.active > span {
    background-image: none !important;
    background-color: #f7ca77 !important;
    color: #ffffff;
}

table.table-condensed td.today > span {
    background-image: none !important;
    background-color: #f7ca77 !important;
    border-radius: 100% !important;
    color: #ffffff !important;
}

table.table-condensed td.installment-day > span {
    background-color: #714FD6;
    color: #ffffff;
}

table.table-condensed thead tr:nth-child(2) {
    background: #714FD6 !important;
    color: #ffffff;

    & th:hover {
        background: #714FD6;
    }

    & th {
        visibility: visible !important;
        border-radius: 0px !important;
        padding: 15px;
    }

    & th:first-child {
        border-top-left-radius: 8px !important;
    }

    & th:last-child {
        border-top-right-radius: 8px !important;
    }
}

table.table-condensed thead tr:last-child {
    & th {
        padding: 15px;
    }
}


