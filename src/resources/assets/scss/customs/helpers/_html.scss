*,
*::after {
    font-family: "Open Sans", sans-serif !important;
}

html {
    position: relative;
}

body {
    margin: 0 !important;
}

a,
button {

    &,
    & *,
    & *::after {
        transition-timing-function: ease-in-out;
        transition: 0.2s;
    }
}

.bg {
    
    &-white {
        background-color: $white;
    }
    
    &-purple {
        background-color: $purpleBg;
    }
    
    &-light-pink {
        background-color: $lightPink;
    }

    &-lendivo-secondary {
        background-color: $lendivoSecondary;
    }
}

.mt {

    &-section {
        margin-top: 32px;

        &.testimonial-section {
            margin-top: 66.5px;

            @media (min-width: $md) {
                margin-top: 94.5px;
            }
        }

        @media (min-width: $md) {
            margin-top: 60px;
        }
    }

    &-n {

        &1 {
            margin-top: -0.25rem!important;
        }

        &2 {
            margin-top: -0.5rem!important;
        }

        &5 {
            margin-top: -48px!important;
        }
    }
}

.error-label {
    flex-wrap: wrap;

    .form-control {
        color: $danger !important;
        border-color: $danger !important;
    }

    span {

        &.error {
            display: block;
            font-size: 12px;
            font-weight: 600;
            text-align: left;
            color: $danger;
            width: 100%;
        }
    }
}

.general-error {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $danger;
    color: $white;
    border-radius: 8px;
    padding: 24px;
    font-size: 14px;
    font-weight: 600;
}

.general-success {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #7FFFD4;
    color: $white;
    border-radius: 8px;
    padding: 24px;
    font-size: 14px;
    font-weight: 600;
}

.z {

    &-1 {
        z-index: 1;
    }

    &-2 {
        z-index: 2;
    }

    &-3 {
        z-index: 3;
    }

    &-4 {
        z-index: 4;
    }
}
