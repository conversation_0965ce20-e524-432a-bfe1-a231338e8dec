import axios from "axios";

export default class FetchClientData {
    refreshClientData(eventData) {
        axios
            .get('/refresh-client-data')
            .then(response => {
                if (response.data.success === true && this.isCurrentPage('/profile/profile-settings')) {
                    window.location.reload();
                }
            })
            .catch(error => {
            });
    }

    isCurrentPage(path) {
        return window.location.pathname.replace(/\/+$/, '') === path.replace(/\/+$/, '');
    }
}
