class DeclarationNavigator {

    init() {
        this.navigateToTarget();
    }

    navigateToTarget() {

        let queryString = window.location.search;
        let params = new URLSearchParams(queryString);
        if (!params.has('d')) {
            return;
        }

        let declarationTarget = params.get('d');

        const target = (
            typeof document.querySelector(`.js-list[data-department="${declarationTarget}"]`) !== 'undefined' &&
            document.querySelector(`.js-list[data-department="${declarationTarget}"]`) !== null ?
                document.querySelector(`.js-list[data-department="${declarationTarget}"]`) :
                false
        );

        if (!target) {
            return false;
        }

        $(target).find('.js-list-button').trigger('click');

        setTimeout(() => {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'start'
            });
        }, 0);
    }
}

window.DeclarationNavigator = new DeclarationNavigator;

$(document).ready(function () {
    window.DeclarationNavigator.init();
});
