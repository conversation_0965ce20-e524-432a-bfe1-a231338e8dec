class GetLoanStats {

    init() {
        this.config = {
            updateInterval: 1000 * 10, // 10 seconds interval
            inUpdate: false,
        };

        this.updateInterval = false;

        // Init Update Interval
        this.initUpdateInterval();
    }

    initUpdateInterval() {

        this.updateInterval = setInterval(() => {
            this.fetchLoanStats();
        }, this.config.updateInterval);
    }

    fetchLoanStats() {
        if (this.config.inUpdate) {
            return false;
        }

        this.config.inUpdate = true;

        $.ajax({
            type: 'GET',
            url: '/get-loan-stats',
            success: (response) => {
                localStorage.setItem('redirectTo', location.href);

                this.config.inUpdate = false;

                if (response.success && localStorage.getItem('redirectTo') !== response.redirectTo) {
                    localStorage.removeItem('redirectTo');
                    window.location.replace(response.redirectTo);
                }

                if (response.success === false && localStorage.getItem('redirectTo') !== response.redirectTo) {
                    localStorage.removeItem('redirectTo');
                    window.location.replace(response.redirectTo);
                }
            },
            error: (response) => {
                this.config.inUpdate = false;
            },
        });
    }
}

window.GetLoanStats = new GetLoanStats;

if (document.querySelector('.js-loan-stats-fetcher')) {
    window.GetLoanStats.init();
}
