// class Cities {
//
//     init() {
//
//         this.initOptionsBuilder();
//     }
//
//     initOptionsBuilder() {
//
//         $(document).on('focus', '[name="city_id"]', (event) => {
//
//             window.citiesTarget = $(event.target);
//             window.citiesCounter = 0;
//             window.citiesListed = [];
//             this.prepareOption();
//
//             window.citiesInterval = setInterval(() => {
//                 this.prepareOption();
//
//                 if (window.citiesListed.length === Object.keys(window.cities).length) {
//                     clearInterval(window.citiesInterval);
//                     return false;
//                 }
//             }, 100);
//         });
//
//         $(document).on('blur', '[name="city_id"]', (event) => {
//
//             window.citiesTarget = $(event.target);
//             window.citiesCounter = 0;
//             window.citiesListed = [];
//
//             clearInterval(window.citiesInterval);
//
//             window.citiesTarget.empty();
//             window.citiesTarget.html(window.citiesTarget.data('default-option'));
//
//             if (
//                 typeof window.cityId !== 'undefined' &&
//                 window.cityId !== '-1'
//             ) {
//                 const option = document.createElement('option');
//                 option.value = window.cityId;
//                 option.innerHTML = window.citiesSelectedOption;
//                 option.selected = 'selected';
//
//                 window.citiesTarget.find('option[selected="selected"]').removeAttr('selected');
//                 window.citiesTarget.append(option);
//
//                 window.citiesListed.push(window.cityId);
//             }
//         });
//
//         $(document).on('change', '[name="city_id"]', (event) => {
//
//             window.citiesTarget = $(event.target);
//             window.cityId = window.citiesTarget.val();
//             window.citiesSelectedOption = window.citiesTarget.find(`option[value="${window.cityId}"]`).html();
//
//             window.citiesTarget.trigger('blur');
//         });
//     }
//
//     prepareOption() {
//         window.citiesCounter = 0;
//         Object.keys(window.cities).forEach((cityId) => {
//
//             if (
//                 window.citiesListed.indexOf(cityId) < 0 &&
//                 window.citiesCounter < 10
//             ) {
//                 const cityLabel = window.cities[cityId];
//                 window.citiesCounter += 1;
//
//                 const option = document.createElement('option');
//                 option.value = cityId;
//                 option.innerHTML = cityLabel;
//
//                 window.citiesTarget.append(option);
//
//                 window.citiesListed.push(cityId);
//             }
//         });
//     }
// }
//
// window.Cities = new Cities;
//
// $(document).ready(function () {
//     if ($('select[name="city_id"]').length !== 0) {
//         window.Cities.init();
//     }
// });
