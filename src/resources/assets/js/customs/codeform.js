const inputs = document.querySelectorAll('#code-form input[type="text"]');

const allowed = '1234567890QWERTYUIOPASDFGHJKLZXCVBNM';

Object.values(inputs).forEach((el, key) => {
    el.addEventListener('keyup', function (e) {
        const btn = e.key;

        if (!allowed.includes(btn.toUpperCase())) {
            return
        }

        this.value = this.value.toUpperCase();

        const maxlen = Number.parseInt(this.getAttribute('maxlength'));

        if (Number.parseInt(this.value.length) === maxlen && key < (inputs.length - 1)) {
            inputs[key + 1].focus();
        }
    })

    el.addEventListener('keypress', function (e) {
        if (e.keyCode === 32) {
            e.preventDefault();
        }

        const btn = e.key;
        const maxlen = Number.parseInt(this.getAttribute('maxlength'));

        if (this.value.length > 0 && btn.length === maxlen && key < (inputs.length - 1)) {
            inputs[key + 1].value = btn.toUpperCase();
        }
    })

    el.addEventListener('paste', e => {
        e.preventDefault();

        const clipboardData = e.clipboardData || window.clipboardData;
        const pastedData = clipboardData.getData('Text');

        pastedData.split('').forEach((char, key) => {
            if (key < inputs.length) {
                inputs[key].value = char;
            }
        })
    })
})
