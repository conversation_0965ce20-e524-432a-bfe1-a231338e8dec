$(document).ready(() => {

    const body = document.querySelector('body');
    const cover = document.querySelector('#cover');
    const dropdown = document.querySelector('#dd');
    const dropdownBtn = document.querySelector('#profile-dd');

    if (!dropdown) {
        return;
    }

    cover.classList.add('visually-hidden');
    dropdown.classList.add('visually-hidden');

    const triggerDropdown = () => {
        if (dropdown.classList.contains('visually-hidden')) {
            dropdownBtn.classList.add('bg-light-pink');
            dropdownBtn.classList.add('rounded-top-8');
            body.classList.add('menu-open');

            dropdown.classList.remove('visually-hidden');
            cover.classList.remove('visually-hidden');
            return;
        }

        dropdownBtn.classList.remove('bg-light-pink');
        dropdownBtn.classList.remove('rounded-top-8');
        body.classList.remove('menu-open');

        cover.classList.add('visually-hidden');
        dropdown.classList.add('visually-hidden');
    };

    const hideDropdown = () => {
        if (!dropdown.classList.contains('visually-hidden')) {
            dropdownBtn.classList.remove('bg-light-pink');
            dropdownBtn.classList.remove('rounded-top-8');
            body.classList.remove('menu-open');

            cover.classList.add('visually-hidden');
            dropdown.classList.add('visually-hidden');
        }
    }

    dropdownBtn.addEventListener('click', e => {
        e.preventDefault();
        triggerDropdown();
        e.stopPropagation();
    })
    body.addEventListener('click', hideDropdown)
});
