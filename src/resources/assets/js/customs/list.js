class List {

    init() {

        // Init List Togglers
        this.initListTogglers();
    }

    initListTogglers() {

        window.addEventListener('click', (event) => {

            const target = (
                event.target.classList.contains('js-list-button') ?
                event.target :
                (
                    typeof event.target.closest('.js-list-button') !== 'undefined' &&
                    event.target.closest('.js-list-button') !== null ?
                    event.target.closest('.js-list-button') :
                    false
                )
            );

            if (
                !target ||
                target.classList.contains('job-link')
            ) { return event; }

            event.preventDefault();

            if (target.classList.contains('js-link-section-trigger')) {
                $('.js-link-section-trigger.open').each(function() {

                    if (target !== this) {
                        const _this = $(this);

                        _this.removeClass('open');

                        _this.closest('.js-link-section').find('.js-link-section-links').slideUp('fast');
                    }
                });
            }

            const department = target.dataset.department;
            const jobsListContainer = document.querySelector(`.js-list-jobs[data-department="${department}"]`);

            if (target.classList.contains('open')) {
                target.classList.remove('open');
                $(jobsListContainer).slideUp('fast');
            } else {
                target.classList.add('open');
                $(jobsListContainer).slideDown('fast');
            }
        });
    }
}
window.List = new List;
