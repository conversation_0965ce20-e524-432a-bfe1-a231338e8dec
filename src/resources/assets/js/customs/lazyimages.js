class LazyImages {
    
    init() {
        
        // Lazy Load Images
        this.lazyLoadImages();
    }

    lazyLoadImages() {
        const lazyLoadImages = document.querySelectorAll('.js-lazyload:not(.loaded)');

        if (
            typeof lazyLoadImages === 'undefined' ||
            lazyLoadImages === null ||
            lazyLoadImages.length === 0
        ) { return false; }

        lazyLoadImages.forEach((image) => { 
            var shouldLoad = false;

            if (
                (
                    image.classList.contains('lazy-load-md-only') &&
                    window.screen.width <= window.breakpoints.md
                ) ||
                (
                    image.classList.contains('lazy-load-before-lg-only') &&
                    window.screen.width < window.breakpoints.lg
                )
            ) {
                shouldLoad = true;
            } else if (
                image.classList.contains('lazy-load-lg-only') &&
                window.screen.width >= window.breakpoints.lg
            ) {
                shouldLoad = true;
            } else if (
                !image.classList.contains('lazy-load-md-only') &&
                !image.classList.contains('lazy-load-lg-only')
            ) {
                shouldLoad = true;
            }

            if (shouldLoad) {
                const imageUrl = image.dataset.src;
                image.src = imageUrl;
                image.classList.add('loaded');
                image.classList.remove('invisible');
            }
        });
    }
}
window.LazyImages = new LazyImages;