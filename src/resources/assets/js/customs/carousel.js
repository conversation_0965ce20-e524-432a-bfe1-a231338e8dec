const initCarouselBtns = btns => {
    Object.values(btns).forEach(el => {
        el.addEventListener('click', e => {
            Object.values(btns).forEach(el => {
                el.classList.remove('active');
            })

            e.target.classList.add('active');
        })
    });
}

$(document).ready(function () {
    if (document.querySelectorAll('#review_carousel-btns button')) {
        initCarouselBtns(
            document.querySelectorAll('#review_carousel-btns button')
        );
    }
});
