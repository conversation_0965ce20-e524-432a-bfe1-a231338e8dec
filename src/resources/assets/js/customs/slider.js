const rangeInput = document.querySelectorAll('input[id^="amount-range"]');
const periodInput = document.querySelectorAll('input[id^="period-range"]');


const convertBgnToEur = (bgnAmount) => {
    return (bgnAmount / 1.95583).toFixed(2); // Keep 2 decimal places for currency
};

const changeValue = e => {
    const activeTabAmountClass = e.target.getAttribute('id');
    const rangeValElement = document.querySelector(`#${activeTabAmountClass}-value`);

    // Update the BGN amount display
    if (rangeValElement) {
        rangeValElement.innerHTML = e.target.value;
    }

    // If the changed slider is an amount slider, update the EUR display
    if (activeTabAmountClass.startsWith('amount-range')) {
        const productId = activeTabAmountClass.split('-').pop();
        const selectedAmountEurElement = document.getElementById(`selected_amount_eur-${productId}`);
        if (selectedAmountEurElement) {
            const bgnAmount = parseFloat(e.target.value);
            selectedAmountEurElement.innerHTML = convertBgnToEur(bgnAmount);
        }
    }
}

const bindMultipleEvents = (el, events, callback) => {
    events.split(' ').forEach(ev => {
        el.addEventListener(ev, e => {
            callback(e)
        })
    })
}

if (rangeInput) {
    Object.values(rangeInput).forEach(el => {
        bindMultipleEvents(el, 'pointermove touchmove mousemove change', changeValue)
    });
}

if (periodInput) {
    Object.values(periodInput).forEach(el => {
        bindMultipleEvents(el, 'pointermove touchmove mousemove change', changeValue)
    });
}
