const updateVals = (el, increment, maxVal) => {
    let baseVal = Number.parseFloat(
        el.getAttribute('data-countup-current')
    );

    baseVal += Math.ceil(increment);

    if (baseVal >= maxVal) {
        el.innerHTML = maxVal;
        el.setAttribute('data-countup-current', maxVal);

        clearInterval(updateVals);
        return;
    }

    el.setAttribute('data-countup-current', baseVal);
    el.innerHTML = baseVal;
}

const initCountup = (anchor) => {
    let triggered = false;

    const frames = 800;
    const speed = 20;
    const trigger = 700;
    const elements = document.querySelectorAll('.countup-container');

    window.addEventListener('scroll', () => {
        if (!triggered) {
            const anchorPos = anchor.getBoundingClientRect().top
                - document.querySelector('body').getBoundingClientRect().top;

            if ((anchorPos - trigger) <= window.scrollY) {
                Object.values(elements).forEach(el => {
                    const maxVal = Number.parseInt(el.getAttribute('data-countup-max'));
                    const splitByFrames = maxVal / frames;

                    triggered = true;
                    setInterval(updateVals, speed, el, splitByFrames, maxVal);
                })
            }
        }
    })
}

$(document).ready(function () {
    if (document.querySelector('#countup-anchor') !== null) {
        initCountup(document.querySelector('#countup-anchor'));
    }
});
