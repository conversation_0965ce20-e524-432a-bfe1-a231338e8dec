const b64toBlob = (b64Data, contentType='', sliceSize=512) => {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);

        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, {type: contentType});
}

const generatePdfLink = (response, type = 'doc') => {
    
    if (
        typeof response === 'undefined' ||
        response === null ||
        !response
    ) { return false; }

    const content = (
        typeof response === 'string' &&
        response.length > 0 ?
        response :
        (
            typeof response.file !== 'undefined' &&
            response.file !== null ? 
            response.file : 
            false
        )
    );

    if (!content) { return false; }

    const name = (
        typeof response.name !== 'undefined' &&
        response.name !== null ?
        response.name.replace(/ /g, '_') :
        false
    );

    const blob = b64toBlob(content, 'application/pdf');
    const url = URL.createObjectURL(blob);

    const types = {
        sef: 'Стандартен европейски формуляр',
        doc: 'Документ',
        contract: 'Договор за кредит'
    }

    const fileName = (
        name !== false ?
        `${name}_${new Date().toLocaleDateString('bg')}.pdf` : 
        `${types[type]}_${new Date().toLocaleDateString('bg')}.pdf`
    );

    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
}
