!function () {

    moment.updateLocale('en', {
        week: {
            dow: 1 // Monday is the first day of the week
        }
    });

    var today = moment();

    function Calendar(selector, events, dateFormat) {
        this.el = document.querySelector(selector);
        this.events = events;
        if (Object.values(events).length === 1) {
            this.lastEvent = Object.values(events)[0];
        } else {
            this.lastEvent = Object.values(events)[Object.values(events).length - 1];
        }

        this.dateFormat = dateFormat;
        this.dates = [];
        this.parseInstallmentDates();
        const currentMonth = this.dates[0].clone();
        this.current = currentMonth.startOf('month');
        this.draw();
        var current = document.querySelector('.today');
        if (current) {
            var self = this;
            window.setTimeout(function () {
                self.openDay(current);
            }, 500);
        }
    }

    Calendar.prototype.parseInstallmentDates = function () {
        Object.values(this.events).forEach(installment => {
            this.dates.push(moment(installment.due_date, 'DD-MM-YYYY'));
        })
    }

    Calendar.prototype.draw = function () {
        //Create Header
        this.drawHeader();

        this.drawLabels();

        //Draw Month
        this.drawMonth();

    }

    Calendar.prototype.drawHeader = function () {
        var self = this;
        if (!this.header) {
            //Create the header elements
            this.header = createElement('div', 'block');
            this.header.className = 'header';

            this.title = createElement('h1');

            const left = document.createElement('img');
            left.src = window.calendarArrows.left;
            left.width = 18;
            left.height = 10;
            left.classList.add('left-arrow');

            left.addEventListener('click', function () {
                self.prevMonth();
            });

            const right = document.createElement('img');
            right.src = window.calendarArrows.right;
            right.width = left.width;
            right.height = left.height;
            right.classList.add('right-arrow');

            right.addEventListener('click', function () {
                self.nextMonth();
            });

            const calendarActions = document.createElement('div');
            calendarActions.classList.add('calendar-actions');
            calendarActions.appendChild(left);
            calendarActions.appendChild(right);

            //Append the Elements
            this.header.appendChild(this.title);
            this.header.appendChild(calendarActions);
            this.el.appendChild(this.header);
        }

        const currentDate = this.current.format('MMMM YYYY');

        this.title.innerHTML = this.formatMonth(currentDate);
    }

    Calendar.prototype.formatMonth = function (currentDate) {
        const monthsMap = {
            'January': 'Януари',
            'February': 'Февруари',
            'March': 'Март',
            'April': 'Април',
            'May': 'Май',
            'June': 'Юни',
            'July': 'Юли',
            'August': 'Август',
            'September': 'Септември',
            'October': 'Октомври',
            'November': 'Ноември',
            'December': 'Декември',
        };

        const currentDateArr = currentDate.split(' ');

        return `${monthsMap[currentDateArr[0]]} ${currentDateArr[1]}`;
    }

    Calendar.prototype.drawLabels = function () {
        if (!this.labels) {
            this.labels = createElement('div', 'label-container');
            this.labels.className = 'label-container';
            this.labels.style.backgroundColor = 'transparent'
            this.labels.style.height = '30px'
            this.labels.style.width = '100%'

            const days = [
                'П', 'В', 'С', 'Ч', 'П', 'С', 'Н'
            ]

            Object.values(days).forEach((day, index) => {
                this.labelTitle = createElement('span');
                this.labelTitle.innerHTML = day;

                if (index >= 5) {
                    this.labelTitle.classList.add('weekend')
                }

                this.labels.append(this.labelTitle);
            })

            //Append the Elements
            this.header.appendChild(this.labels);
        }
    }


    Calendar.prototype.drawMonth = function () {
        var self = this;

        this.events.forEach(function (ev) {
            ev.date = self.current.clone().date(Math.random() * (29 - 1) + 1);
        });


        if (this.month) {
            this.oldMonth = this.month;
            this.oldMonth.className = 'month out ' + (self.next ? 'next' : 'prev');
            this.oldMonth.addEventListener('webkitAnimationEnd', function () {
                self.oldMonth.parentNode.removeChild(self.oldMonth);
                self.month = createElement('div', 'month');
                self.backFill();
                self.currentMonth();
                self.fowardFill();
                self.el.appendChild(self.month);
                window.setTimeout(function () {
                    self.month.className = 'month in ' + (self.next ? 'next' : 'prev');
                }, 16);
            });
        } else {
            this.month = createElement('div', 'month');
            this.el.appendChild(this.month);
            this.backFill();
            this.currentMonth();
            this.fowardFill();
            this.month.className = 'month new';
        }
    }

    Calendar.prototype.backFill = function () {
        var clone = this.current.clone();
        var dayOfWeek = clone.day();

        if (!dayOfWeek) {
            return;
        }

        clone.subtract('days', dayOfWeek);

        for (var i = dayOfWeek; i > 1; i--) {
            this.drawDay(clone.add('days', 1));
        }
    }

    Calendar.prototype.fowardFill = function () {
        var clone = this.current.clone().add('months', 1).subtract('days', 1);
        var dayOfWeek = clone.day();

        if (dayOfWeek === 7) {
            return;
        }

        for (var i = dayOfWeek; i < 7; i++) {
            this.drawDay(clone.add('days', 1));
        }
    }

    Calendar.prototype.currentMonth = function () {
        var clone = this.current.clone();

        while (clone.month() === this.current.month()) {
            this.drawDay(clone);
            clone.add('days', 1);
        }
    }

    Calendar.prototype.getWeek = function (day) {
        if (!this.week || day.day() === 1) {
            this.week = createElement('div', 'week');
            this.month.appendChild(this.week);
        }
    }

    Calendar.prototype.drawDay = function (day) {
        var self = this;
        this.getWeek(day);

        //Outer Day
        var outer = createElement('div', this.getDayClass(day));

        outer.addEventListener('click', function () {
            self.openDay(this);
        });

        //Day Name
        var name = createElement('div', 'day-name', day.format('ddd'));

        //Day Number
        var number = createElement('div', 'day-number', day.format('D'));

        //Events
        var events = createElement('div', 'day-events');

        outer.appendChild(name);
        outer.appendChild(number);
        outer.appendChild(events);

        Object.values(this.dates).forEach(date => {
            if (day.isSame(date)) {
                outer.classList.add('today');
            }

            if (moment(this.lastEvent?.due_date, 'DD-MM-YYYY').isBefore(day)) {
                outer.classList.add('disabled');
            }
        })

        this.week.appendChild(outer);
    }

    Calendar.prototype.getDayClass = function (day) {
        classes = ['day'];
        if (day.month() !== this.current.month()) {
            classes.push('other');
        } else if (
            !this.el.classList.contains('js-installments-calendar') &&
            today.isSame(day, 'day')
        ) {
            classes.push('today');
        }
        return classes.join(' ');
    }

    Calendar.prototype.openDay = function (el) {
        var dayNumber = +el.querySelectorAll('.day-number')[0].innerText || +el.querySelectorAll('.day-number')[0].textContent;
        var day = this.current.clone().date(dayNumber);

        var currentOpened = document.querySelector('.day.today');

        if (currentOpened) {
            currentOpened.classList.remove('today');
        }

        el.classList.add('today');

        const currAmountEur = document.querySelector('#current-installment-amount-eur');
        const currAmount = document.querySelector('#current-installment-amount');
        const currDate = document.querySelector('#current-installment-date');


        var installments = this.events.reduce(function (memo, ev) {
            const date = moment(ev.due_date, 'DD-MM-YYYY');
            if (date.isSame(day, 'day')) {
                memo.push(ev);
            }
            return memo;
        }, []);

        if (installments.length > 0) {
            currAmountEur.parentNode.classList.remove('visually-hidden');
            currAmount.parentNode.classList.remove('visually-hidden');
            currDate.parentNode.classList.remove('visually-hidden');

            currAmountEur.innerHTML = installments[0].total_amount_eur;
            currAmount.innerHTML = installments[0].total_amount;
            currDate.innerHTML = moment(installments[0].due_date, 'DD-MM-YYYY').format(this.dateFormat)

            return;
        }

        currAmountEur.parentNode.classList.add('visually-hidden');
        currAmount.parentNode.classList.add('visually-hidden');
        currDate.parentNode.classList.add('visually-hidden');
    }

    Calendar.prototype.nextMonth = function () {
        this.current.add('months', 1);
        this.next = true;
        this.draw();
    }

    Calendar.prototype.prevMonth = function () {
        this.current.subtract('months', 1);
        this.next = false;
        this.draw();
    }

    window.Calendar = Calendar;

    function createElement(tagName, className, innerText) {
        var ele = document.createElement(tagName);
        if (className) {
            ele.className = className;
        }
        if (innerText) {
            ele.innderText = ele.textContent = innerText;
        }
        return ele;
    }
}();
