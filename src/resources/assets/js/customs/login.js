import axios from "axios";

class Login {

    init() {
        // Init the Resend Trigger
        this.initResendTrigger();
    }

    initResendTrigger() {

        $(document).on('click', '.js-resend-code', (event) => {
            event.preventDefault();

            let $loginApiUrl = $('input[name="loginApiUrl"]').val();
            let $pin = $('input[name="pin"]').val();

            axios
                .post($loginApiUrl, {pin: $pin})
                .then(resp => {
                    console.log(resp);
                    if (resp.data.redirectTo !== undefined) {
                        window.location = resp.data.redirectTo;
                    }
                })
                .catch(error => {
                    console.error(error);
                    console.error(error.response);
                });
        });
    }
}

window.Login = new Login;

$(document).ready(function () {
    window.Login.init();
});
