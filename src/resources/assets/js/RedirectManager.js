export default class RedirectManager {
    redirectToSignLoan(data) {
        if (!data.client_id || !data.loan_id) {
            console.error('Invalid params.');
            return;
        }

        /// fetch loan data
        axios.get('/get-loan-stats', {
            params: {
                loan_id: data.loan_id,
                client_id: data.client_id,
            }
        })
            .then((response) => {
                const data = response.data;

                // localStorage.setItem('redirectTo', location.href);
                // const stored = localStorage.getItem('redirectTo');
                const target = data.redirectTo;

                // if (data.success && stored !== target) {
                //     localStorage.removeItem('redirectTo');
                //     window.location.replace(target);
                // }
                //
                // if (!data.success && stored !== target) {
                //     localStorage.removeItem('redirectTo');
                //     window.location.replace(target);
                // }

                window.location.replace(target);
            })
            .catch((error) => {
                console.error(error);
            });
    }
}
