import RedirectManager from "./RedirectManager";
import FetchClientData from "./FetchClientData";

try {
    window.jQuery = window.$ = require("jquery");
    require("bootstrap/dist/js/bootstrap");
    window.moment = require("moment");
    // require("bootstrap-select/dist/js/bootstrap-select.min");
    require('../plugins/select2/select2.min');
    require('@fortawesome/fontawesome-free/js/all');
    require("bootstrap-datepicker/dist/js/bootstrap-datepicker");

    require("./customs/slider");

    require("./customs/lazyimages");
    LazyImages.init();

    require("./customs/countup");
    require("./customs/carousel");
    require("./customs/list");
    List.init();

    require("./customs/declaration-navigator");
    require("./customs/login");
    require("./customs/dropdown");
    require("./customs/get-loan-stats");
    require("./customs/cities");
    require("./customs/loan-refinance");
    require("./customs/calendar");

    require("parsleyjs/dist/parsley");
    require("parsleyjs/dist/i18n/bg");
    window.Parsley.setLocale(document.documentElement.lang);

    window.RedirectManager = new RedirectManager();
    window.FetchClientData = new FetchClientData();

} catch (e) {
    console.error(e);
}
