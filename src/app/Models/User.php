<?php

namespace App\Models;

use Illuminate\Contracts\Auth\Authenticatable;

class User implements Authenticatable
{
    protected $attributes;

    public function __construct(array $attributes)
    {
        if (!empty($attributes['login_token'])) {
            $attributes[$this->getRememberTokenName()] = $attributes['login_token'];
            unset($attributes['login_token']);
        }

        $this->attributes = $attributes;
    }

    public function fullNameLatin(): ?string
    {
        if (!isset($this->attributes['first_name_latin'])) {
            return null;
        }

        return sprintf(
            '%s %s %s',
            $this->attributes['first_name_latin'],
            $this->attributes['middle_name_latin'],
            $this->attributes['last_name_latin']
        );
    }

    public function getAuthPassword()
    {
        return $this->attributes['code'];
    }

    public function getAuthIdentifierName()
    {
        return 'client_id';
    }

    public function getRememberTokenName()
    {
        return 'remember_token';
    }

    public function getAuthIdentifier()
    {
        return $this->attributes[$this->getAuthIdentifierName()];
    }

    public function getRememberToken()
    {
        return $this->attributes[$this->getRememberTokenName()] ?? null;
    }

    public function setRememberToken($value)
    {
        return $this->attributes[$this->getRememberTokenName()] = $value;
    }

    public function get($key)
    {
        return $this->attributes[$key] ?? null;
    }

    public function getAll(): array
    {
        return $this->attributes;
    }

    public function add($key, $value)
    {
        $this->attributes[$key] = $value;
        $this->save();
    }

    public function addData($data)
    {
        $this->attributes = array_merge($this->attributes, $data);
        $this->save();
    }

    public function remove($key)
    {
        if (!empty($this->attributes[$key])) {
            unset($this->attributes[$key]);
            $this->save();
        }
    }

    public function save()
    {
        session(['user' => $this->attributes]);
    }
}
