<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContactFormMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public array $data
    )
    {
    }

    public function envelope()
    {
        return new Envelope(
            subject: 'Contact Form Mail',
        );
    }

    public function content()
    {
        return new Content(
            view: 'mail.contact-form',
        );
    }
}
