<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IpCheckerMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        return $next($request);

        if (app()->environment('stage')) {
            $availableIps = config('available-ips');
            $userIp = $request->getClientIp();

            if (!in_array($userIp, $availableIps)) {

                $maintenanceHtml = '
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Under Maintenance</title>
                    <style>
                      body { text-align: center; padding: 150px; }
                      h1 { font-size: 30px; }
                      body { font: 20px Helvetica, sans-serif; color: #333; }
                      article { display: block; text-align: left; width: 650px; margin: 0 auto; }
                      a { color: #dc8100; text-decoration: none; }
                      a:hover { color: #333; text-decoration: none; }
                    </style>
                </head>
                <body>
                    <h1>Здравей!</h1>
                    <h1>Сайта ни се обновява и няма да бъде достъпен за потребители до 08:00 на 19-ти февруари.</h1>
                    <h1>Ако си направил плащане по кредита си, то ще бъде отразено в системата ни още в понеделник.</h1>
                </body>
                </html>
                ';
                echo $maintenanceHtml; exit;

                return redirect('/coming-soon');
            }
        }

        return $next($request);
    }
}
