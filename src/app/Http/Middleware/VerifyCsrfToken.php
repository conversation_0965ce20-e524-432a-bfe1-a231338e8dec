<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        '/affiliate/submit_application',
        '/verification/start',
        '/verification/finish',
        '/easypay/*',
        '/easypay/test',
        '/easypay/notify',
        '/easypay/pay_init',
        '/easypay/pay_confirm',
        '/pos-payment-result',
        '/payments/success',
    ];
}
