<?php

namespace App\Http\Middleware;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class Authenticate extends Middleware
{
    public function handle($request, Closure $next, ...$guards)
    {
        if (auth()->check()) {

            $user = auth()->user();
            $clientId = $user->get('client_id');
            $sessionKey = 'last_token_validation_' . $clientId;


            $lastValidated = Session::get($sessionKey);
            $validationInterval = 120; // 2 minutes
            if (
                !$lastValidated
                || (time() - $lastValidated) > $validationInterval
            ) {
                $apiResponse = app(CurlService::class)->chGet(
                    ApiRoutesEnum::VERIFY_CLIENT_TOKEN->getRoute() . "/{$clientId}",
                    [
                        'login_token' => $user->get('remember_token'),
                        'ip' => $request->getClientIp(),
                        'browser' => $request->userAgent(),
                    ]
                );

                /// set WsTokenExpired
                if (
                    isset($apiResponse['message'])
                    && $apiResponse['message'] == 'Unauthenticated.'
                ) {
                    Session::put('WsTokenExpired', true);
                } else {
                    Session::put($sessionKey, time());
                }
            }
        }

        if (Session::get('WsTokenExpired', false) || !auth()->check()) {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            if ($request->ajax()) {
                session()->flash('warning', __('Невалидна сесия. Моля започенете отначало.'));

                return response()->json([
                    'success' => false,
                    'status' => false,
                    'redirectTo' => route('login')
                ]);
            }

            return to_route('login');
        }

        return $next($request);
    }

    protected function redirectTo($request)
    {
        if (!$request->expectsJson()) {
            return route('login');
        }
    }
}
