<?php

namespace App\Http\Middleware;

use App\Application\Enums\ApiRoutesEnum;
use App\Application\Enums\LoanStatusEnum;
use App\Services\ChHelperServiceProvider;
use App\Services\CurlService;
use Illuminate\Http\Request;
use Closure;
use Illuminate\Support\Facades\Auth;

class CheckLoanStats
{
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check() || $request->routeIs('verification.*')) {
            return $next($request);
        }

        $user = Auth::user();
        $loanId = $user->get('loan_data')['loan_id'];
        if (intval($loanId) === 0) {
            return $next($request);
        }

        $curlService = app(CurlService::class);
        $chHelperService = app(ChHelperServiceProvider::class);

        $apiResponse = $curlService->chPost(
            ApiRoutesEnum::GET_LOAN_STATUS->getRoute() . "/{$loanId}",
            [
                'login_token' => $user->get('remember_token'),
                'client_id' => $user->get('client_id'),
                'loan_id' => $user->get('loan_data')['loan_id'],
            ]
        );

        if (!empty($apiResponse['success'])) {
            $loanData = $apiResponse['response']['loan_data'];
            $user->add('loan_data', $loanData);
            $user->add('verified', true);

            /// if refinance request is cancelled but client have active loan
            if (
                $chHelperService->hasActiveLoan()
                && !empty($loanData['loan_status_id'])
                && $loanData['loan_status_id'] === LoanStatusEnum::Cancelled->id()
            ) {
                /// reset data to active loan
                $chHelperService->setActiveLoan();
            }

            return match ($loanData['loan_status_id']) {
                LoanStatusEnum::Cancelled->id() => to_route('active.loan'),
                default => $next($request)
            };
        }

        return $next($request);
    }
}
