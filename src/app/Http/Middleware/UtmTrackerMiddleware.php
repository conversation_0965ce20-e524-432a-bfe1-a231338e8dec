<?php

namespace App\Http\Middleware;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class UtmTrackerMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if ($request->hasAny(['utm_source', 'utm_campaign']) && !Session::get('utm_tracker_id')) {
            Session::put('utm', [
                'session_id' => Session::getId(),
                'utm_id' => $request->get('utm_id'),
                'affiliate_id' => $request->get('affiliate_id'),
                'utm_source' => $request->get('utm_source'),
                'utm_medium' => $request->get('utm_medium'),
                'utm_campaign' => $request->get('utm_campaign'),
                'utm_term' => $request->get('utm_term'),
                'utm_content' => $request->get('utm_content'),
            ]);

            $response = app(CurlService::class)->chPost(ApiRoutesEnum::UTM_TRACKER->getRoute(), Session::get('utm'));
            Session::put('utm_tracker_id', $response['utm_tracker_id'] ?? 0);
        }

        return $next($request);
    }
}
