<?php

namespace App\Http\Controllers;

use App\Http\Requests\AffiliateApplicationStatusRequest;
use App\Http\Requests\AffiliateRequest;
use App\Services\AffiliateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * Test url
 * http://localhost:8080/affiliate/submit_application?pin=8109300162&idcard_number=920102794&email=<EMAIL>&phone=0878000000&amount=200&token=PwsT1R2zp7GXsO0r1JpgxhbGvL9xElJwZO3Xr4dzXipIDwSetl2AVICUFZD6OwF8&affiliate=izbirambg
 */
class AffiliatesController extends Controller
{
    public function loanStatus(
        AffiliateApplicationStatusRequest $request,
        AffiliateService                  $affiliateService
    ): JsonResponse
    {
        try {
            // always save to log, what we received
            Log::channel('affiliates')->info('submitApplication():', $request->all());

            $requestData = $request->validated();
            $apiResponse = $affiliateService->checkApplicationStatus([
                ...$requestData,
                'ip' => $request->getClientIp(),
                'browser' => $request->userAgent()
            ]);

            if (empty($apiResponse['success']) && true !== $apiResponse['success']) {
                throw new \RuntimeException('Affiliate application status failed');
            }

            return response()->json([
                'success' => true,
                'data' => $apiResponse['data']
            ]);
        } catch (\Throwable $throwable) {
            Log::channel('affiliates')->error(
                'Error loanStatus(): '
                . $throwable->getMessage() . ', '
                . $throwable->getLine() . ':'
                . $throwable->getCode()
            );

            return response()->json([
                'success' => false,
                'error' => 'Affiliate service system error',
            ], 400);
        }
    }

    public function submitApplication(
        AffiliateRequest $request,
        AffiliateService $affiliateService
    ): JsonResponse
    {
        // always save to log, what we received
        Log::channel('affiliates')->info('submitApplication():', $request->all());

        $requestData = $request->validated();
        $affiliates = $affiliateService->getAvailableAffiliates([
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent()
        ]);

        if (
            empty($requestData['affiliate'])
            || !isset($affiliates[$requestData['affiliate']])
            || $affiliates[$requestData['affiliate']]['token'] !== $requestData['token']
        ) {
            return response()->json([
                'success' => false,
                'error' => 'Wrong affiliate or token',
            ], 400);
        }


        try {

            $requestData['ip'] = $request->getClientIp();
            $requestData['browser'] = $request->userAgent();
            $response = $affiliateService->registerAffiliateRequest($requestData);
            Log::channel('affiliates')->info('submitApplication():', $response);

            if (empty($response['loan_id']) && empty($response['loan_status'])) {
                throw new \RuntimeException('Error registering affiliate request');
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $response['loan_id'] ?? null,
                    'status' => $response['loan_status'], /// received|rejected
                ]
            ]);

        } catch (\Throwable $e) {
            Log::channel('affiliates')->error(
                'Error submitApplication(): '
                . $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return response()->json([
                'success' => false,
                'error' => 'Affiliate service system error'
            ], 400);
        }
    }
}
