<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Application\Enums\LoanStatusEnum;
use App\Http\Requests\LoginCodeRequest;
use App\Services\CurlService;
use Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use RuntimeException;

class ValidateSmsController extends Controller
{
    public function index(Request $request): View|RedirectResponse
    {
        $data = $request->session()->all();
        if (empty($data['phone'])) {
            return to_route('login')->with('error', 'Missing client data');
        }

        $data['phone'] = str_repeat("*", strlen($data['phone']) - 4) . substr($data['phone'], -4);
        $data['loginApiUrl'] = route('login.post');

        return view('validate-sms.index', $data);
    }


    public function store(
        LoginCodeRequest $request,
        CurlService      $curlService
    ): JsonResponse|RedirectResponse {
        $data = $request->validated();
        $sessionData = $request->session()->all();
        if (!$sessionData) {
            return to_route('login')->with('error', 'Missing client data');
        }

        $code = $data['code'] ?? '';

        $apiResponse = $curlService->chPost(ApiRoutesEnum::LOGIN->getRoute(), [
            'code' => $code,
            'client_id' => $sessionData['id'] ?? $sessionData['client_id'],
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent(),
        ]);

        $response = ['success' => true];

        if (!isset($apiResponse['success']) || $apiResponse['success'] === false) {
            $response['success'] = false;

            if (isset($apiResponse['error'])) {
                $response['error'] = $apiResponse['error'];
            } elseif (isset($apiResponse['message'])) {
                $response['error'] = $apiResponse['message'];
            }

            return response()->json($response);
        }

        $loggedInFlag = Auth::attempt([
            'client_id' => $sessionData['id'] ?? $sessionData['client_id'],
            'login_token' => $apiResponse['token'],
            'request' => $request,
        ]);

        /// after logged session id is different
        /// get session data before login to send request if
        /// client no have active loan
        $request->session()->put($sessionData);

        if (!$loggedInFlag) {
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            throw new RuntimeException('Account not found check you data and try again');
        }

        $loanData = Auth::user()->get('loan_data');
        $loanStatusId = null;
        if (!empty($loanData['loan_status_id'])) {
            $loanStatusId = (int) $loanData['loan_status_id'];
        }

        $response['redirectTo'] = route('verify');
        if (
            config('app.use_veriff') === true &&
            !empty($apiResponse['new_client']) &&
            (int) $apiResponse['new_client'] === 1 &&
            $apiResponse['has_action_verif'] === false &&
            in_array($loanStatusId, [LoanStatusEnum::Signed->id(), LoanStatusEnum::Processing->id()])
        ) {
            $response['redirectTo'] = route('verification.index');
        }

        return response()->json($response);
    }
}
