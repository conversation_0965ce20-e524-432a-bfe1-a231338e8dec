<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\NewLoanRequest;
use App\Models\User;
use App\Services\CurlService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Application\Actions\ProductsDataAction;

class NewLoanController extends Controller
{
    private const LOCK_TIMEOUT = 60;

    public function __invoke(
        NewLoanRequest     $request,
        CurlService        $curlService,
        ProductsDataAction $productsDataAction
    ): JsonResponse|RedirectResponse
    {
        $data = $request->validated();
        $data['ip'] = $request->getClientIp();
        $data['browser'] = $request->userAgent();
        $data['amount_requested'] = floatToInt($data['amount_requested']);
        /** @var User $user */
        $user = Auth::user();

        $lock = Cache::lock('new-loan-' . $user?->getAuthIdentifier(), self::LOCK_TIMEOUT);

        if ((int)$data['payment_method_id'] !== 1) {
            unset($data['iban']);
        }


        $activeLoansAmount = $this->getActiveLoansAmount(); // comes as int

        $dataProducts = $productsDataAction->execute($user->get('client_id'));
        $maxProductAmount = 0;
        foreach ($dataProducts['products'] as $productData) {
            if ($productData['product_id'] == $data['product_id']) {
                $maxProductAmount = floatToInt($productData['slider_data']['max_amount']);
                break;
            }
        }

        if ($maxProductAmount > 0 && $maxProductAmount < $activeLoansAmount) {
            return response()->json([
                'messages' => "Сума за рефинансиране (" . intToFloat($activeLoansAmount) . ") надвишава максимална сума на продукта (" . intToFloat($maxProductAmount) . "), моля избери друг продукт",
                'success' => false,
            ]);
        }

        if (!empty($activeLoansAmount) && $data['amount_requested'] < $activeLoansAmount) {
            return response()->json([
                'messages' => "Задължението по активният ти кредит (" . intToFloat($activeLoansAmount) . ") е по-голямо от исканата сума: (" . intToFloat($data['amount_requested']) . "). Избери по-висока сума, за да рефинансираш. Мин: " . intToFloat(($activeLoansAmount + 10000)),
                'success' => false,
            ]);
        }

        $data = [
            'client_id' => $user->get('client_id'),
            'insurance' => ($data['insurance'] === 'with_insurance') ? 1 : 0,
            'product_id' => $data['product_id'],
            'amount_requested' => $data['amount_requested'],
            'period_requested' => $data['period_requested'] ?? 30,
            'payment_method_id' => $data['payment_method_id'],
            'iban' => $data['iban'] ?? null,
            'login_token' => $user->get('remember_token'),
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent(),
        ];

        if (!$lock->get()) {
            return $request->ajax() ? response()->json([
                'success' => false,
                'messages' => __('Try again later'),
            ]) : back()->with('error', __('Try again later'));
        }

        $apiResp = $curlService->chPost(
            ApiRoutesEnum::REQUEST_FROM_PROFILE->getRoute(),
            $data
        );

        $response = [
            'success' => false,
            'messages' => $apiResp['error'] ?? '',
            'redirectTo' => empty($apiResp['error']) ? route('login') : null,
        ];

        if (!empty($apiResp['success'])) {
            $request->session()->put('insurance_choice', true);

            $this->refreshLoanData($apiResp['response']['loan_id']);

            $response = [
                'success' => true,
                'redirectTo' => route($apiResp['response']['redirect']['url']),
            ];

            $lock->release();

            return $request->ajax() ? response()->json($response) : redirect()->to($response['redirectTo']);
        }

        $lock->release();

        return $request->ajax()
            ? response()->json($response)
            : back()->with('error', $response['messages'] ?? __('General error'));
    }
}
