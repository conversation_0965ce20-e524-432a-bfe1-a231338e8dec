<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\UploadDocumentsRequest;
use App\Services\CurlService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class UploadDocumentsController extends Controller
{
    public function index(string $loanHash, string $success = null): View
    {
        return view('upload-documents.index', [
            'loanHash' => $loanHash,
            'success' => $success,
        ]);
    }

    public function upload(
        string                 $loanHash,
        UploadDocumentsRequest $request,
        CurlService            $curlService
    ): RedirectResponse
    {
        try {
            $validated = $request->validated();

            $response = $curlService->chPost(ApiRoutesEnum::UPLOAD_ID_CARD_SELFIE->getRoute(), [
                'selfie' => $validated['selfie'],
                'hash' => $loanHash,
                'ip' => $request->getClientIp(),
                'browser' => $request->userAgent(),
            ]);

            if (
                isset($response['success']) &&
                $response['success'] === false &&
                !empty($response['message'])
            ) {
                return to_route('upload-documents.index', $loanHash)->with('error', $response['message']);
            }

            if (
                isset($response['success']) &&
                $response['success'] === true &&
                !empty($response['message'])
            ) {
                return to_route('upload-documents.index', [
                    'hash' => $loanHash,
                    'success' => 'success'
                ]);
            }
        } catch (\Throwable $e) {
            Log::debug(
                $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return to_route('upload-documents.index', $loanHash)->with('error', __('Системна грешка'));
        }

        return to_route('upload-documents.index', $loanHash)->with('error', __('Системна грешка'));
    }
}
