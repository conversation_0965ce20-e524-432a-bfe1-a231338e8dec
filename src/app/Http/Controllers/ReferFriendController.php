<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\ReferFriendRequest;
use App\Services\CurlService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;
use Throwable;

class ReferFriendController extends Controller
{
    public function store(ReferFriendRequest $request, CurlService $curlService): View|RedirectResponse
    {
        try {
            // 1. validate data
            $data = $request->validated();
            $data['ip'] = $request->getClientIp();
            $data['browser'] = $request->userAgent();

            // 2. get client id which refer a friend
            $clientId = $request->user()?->get('client_id') ?? null;
            if (null === $clientId) {
                throw new \Exception('Error unauthorized client id for refer friend');
            }

            // 3. make post request to CH for register referral
            $response = $curlService->chPost(ApiRoutesEnum::REFER_FRIEND->getRoute(), [
                'client_id' => $clientId,
                ...$data
            ]);

            /// if we came from fefer friend more show success page
            if (!empty($data['refer_more']) && intval($data['refer_more']) === 1) {
                return to_route('refer-success');
            }

            // 4. show error
            $firstStepErrorMessage = null;
            if (!empty($response['data']['phones']) && is_array($response['data']['phones'])) {
                $msg = reset($response['data']['phones']);
                if ($msg !== 'Received') {
                    $firstStepErrorMessage = $msg;
                }
            }
            if (!empty($firstStepErrorMessage)) {
                return back()->with('error', $firstStepErrorMessage);
            }


            /// if we came from /uspeh we need to show refer more friend page
            if (!empty($response['status']) && true === $response['status']) {
                Session::put("{$clientId}-hasSeenReferFriendOption", true);

                return to_route('refer-more');
            }

            return to_route('refer-more')->withErrors($response['data']['phones']);

        } catch (Throwable $e) {
            Log::debug($e->getMessage());

            return view('errors.404');
        }
    }

    public function referMore(Request $request): View|RedirectResponse
    {
        $user = $request->user();
        if (empty($user)) {
            return to_route('login');
        }

        return view('refer-friend.refer-more');
    }

    public function referFromProfile(Request $request): View|RedirectResponse
    {
        $user = $request->user();
        if (empty($user)) {
            return to_route('login');
        }

        return view('refer-friend.refer-from-profile');
    }

    public function success(Request $request): View|RedirectResponse
    {
        $user = $request->user();
        if (empty($user)) {
            return to_route('login');
        }

        return view('refer-friend.success');
    }
}
