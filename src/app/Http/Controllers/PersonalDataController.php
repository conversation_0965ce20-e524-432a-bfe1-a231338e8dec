<?php

namespace App\Http\Controllers;

use App\Application\Actions\ProductsDataAction;
use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\PersonalDataRequest;
use App\Services\CurlService;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class PersonalDataController extends Controller
{
    public function index(
        Request            $request,
        ProductsDataAction $productsDataAction
    ): View|RedirectResponse
    {
        $step = $request->session()->get("step");
        if ($step !== 2) {
            return to_route('home.index');
        }

        $loanData = $request->session()->all();

        $data = $productsDataAction->execute();
        $data['loan_data'] = $loanData;
        $data['header'] = 'Информация за кредит';
        $data['creditInfo'] = [
            'Сума на заявката' => $loanData['totalAmount'] . ' лв. / €' . ($loanData['totalAmount_eur'] ?? 0),
            'Период на изплащане' => $loanData['period'] . ' ' . $loanData['periodLabel'],
            'Дата на изплащане' => Carbon::parse($loanData['last_due_date'])->format('d.m.Y'),
            'ГПР' => $loanData['gpr'] . '%',
        ];

        return view('personal-data.index', $data);
    }

    public function post(
        PersonalDataRequest $request
    ): RedirectResponse
    {
        $data = $request->validated();
        $vars = session()->all();
        if (!$vars) {
            return to_route('home.index')->with('error', __('Lost session, retry from begin.'));
        }

        // Overwritten to not be injected
        $data['request_id'] = $vars['request_id'];
        $data['ip'] = $request->getClientIp();
        $data['browser'] = $request->userAgent();

        // Reset the default iban if needed
        if ((int)$data['payment_method_id'] !== 1 && !empty($data['iban'])) {
            unset($data['iban']);
        }

        $result = app(CurlService::class)->chPost(
            ApiRoutesEnum::UPDATE_REQUEST->getRoute(),
            $data
        );

        //// has error on request api redirect back
        if (
            isset($result['success']) &&
            $result['success'] === false &&
            empty($result['response']['redirect']['url'])
        ) {
            $errorMessage = $result['error'] ?? __('Грешка при регистрация, моля опитайте отново.');

            return back()->withInput($data)->with('error', $errorMessage);
        }

        if (isset($result['response']['redirect']['url']) || isset($result['response']['url'])) {
            $redirectUrl = $result['response']['redirect']['url'] ?? $result['response']['url'];

            return match ($redirectUrl) {
                'login.sms' => $this->redirectToLoginPage($result),
                'third.step' => $this->redirectToPersonalDataFull($result),
                'contract' => $this->logInAndRedirectToProfile($result),
            };
        }

        return back()->withInput($data)->with('error', __('Грешка при регистрация, моля опитайте отново.'));
    }

    private function redirectToPersonalDataFull(array $result): RedirectResponse
    {
        $request = request();
        $sessionData = session()->all();

        $data = array_merge($sessionData, $result['response'], $request->all());
        $data['step'] = 3;

        session($data);

//        $redirectParams = $result['response']['redirect']['url_params'];

        return to_route($result['response']['redirect']['url']);
    }

    private function logInAndRedirectToProfile(array $result): RedirectResponse
    {
        $request = request();
        if (!$this->tryLogin($result, $request)) {
            return back()->with('error', __('General error.'));
        }
        Session::remove('step');

        return to_route($result['response']['url']);
    }

    private function redirectToLoginPage(array $result): RedirectResponse
    {
        $request = request();
        $sessionData = session()->all();

        $data = array_merge($sessionData, $result['response'], $request->all());
        session($data);

//        $redirectParams = $result['response']['redirect']['url_params'];

        return to_route($result['response']['redirect']['url']);
    }
}
