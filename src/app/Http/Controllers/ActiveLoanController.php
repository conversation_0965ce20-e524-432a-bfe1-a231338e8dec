<?php

namespace App\Http\Controllers;

use App\Application\Actions\ProductsDataAction;
use App\Application\Enums\LoanStatusEnum;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class ActiveLoanController extends Controller
{
    public function index(ProductsDataAction $productsDataAction): View|RedirectResponse
    {
        $user = Auth::user();
        $loanId = $user->get('loan_data')['loan_id'] ?? 0;
        $loan_data = $this->refreshLoanData($loanId);

        $loanStatusId = $loan_data['loan_status_id'] ?? null;

        if (!in_array($loanStatusId, [LoanStatusEnum::Active->id(), LoanStatusEnum::Approved->id()])) {
            return match ($loanStatusId) {
                1 => redirect()->route('contract'), /// new loan
                2, 3 => redirect()->route('signed'), /// signed-loan
                default => redirect()->route('profile'), /// new loan
            };
        }

        $data = $productsDataAction->execute($user->get('client_id'));
        $nextInstallmentIndex = $loan_data['next_installment_index'] ?? null;

        $installmentAmount = $loan_data['installments'][0]['total_amount'];
        $installmentAmountEur = $loan_data['installments'][0]['total_amount_eur'];
        if (isset($loan_data['installments'][$nextInstallmentIndex]['total_amount'])) {
            $installmentAmount = $loan_data['installments'][$nextInstallmentIndex]['total_amount'];
            $installmentAmountEur = $loan_data['installments'][$nextInstallmentIndex]['total_amount_eur'];
        }

        $data['creditInfo'] = [
            'Сума'   => $loan_data['amount_approved'] . ' лв. / €' . $loan_data['amount_approved_eur'],
            'Вноска' => $installmentAmount . ' лв. / €' . $installmentAmountEur,
            'Период' => $loan_data['period_approved'] . ' ' . $loan_data['periodLabel']
        ];
        $data['header'] = 'Информация за кредит ' . $loan_data['loan_id'];

        if (!empty($loan_data['insurance_amount'])) {
            $data['creditInfo']['Застраховка'] = $loan_data['insurance_amount'] . ' лв. / €' . $loan_data['insurance_amount_eur'];
        }

        $data['addonCreditInfo'] = [
            'Дължима вноска на' => $loan_data['next_installment_due_date'],
            'Дължима вноска'    => ($loan_data['next_installment_amount'] ?? 0) . ' лв. / €' . ($loan_data['next_installment_amount_eur'] ?? 0),
            'Просрочена сума'   => ($loan_data['current_overdue_amount'] ?? 0) . ' лв. / €' . ($loan_data['current_overdue_amount_eur'] ?? 0),
            'Общо дължимо'      => ($loan_data['total_rest_next_payment'] ?? 0) . ' лв. / €' . ($loan_data['total_rest_next_payment_eur'] ?? 0),
        ];

        return view('active-loan.index', $data);
    }
}
