<?php

namespace App\Http\Controllers;

use App\Application\Actions\LoginSendSmsAction;
use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\LoginRequest;
use App\Services\CurlService;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class LoginController extends Controller
{
    public function index(): View
    {
        return view('login.index');
    }

    public function post(
        LoginRequest       $request,
        LoginSendSmsAction $loginSendSmsAction
    ): JsonResponse {

        $data = $request->validated();

        $data['ip'] = $request->getClientIp();
        $data['browser'] = $request->userAgent();

        $apiResponse = $loginSendSmsAction->execute($data);
        $response = ['success' => true];

        if (isset($apiResponse['success']) && $apiResponse['success'] === true) {
            session([
                'id' => $apiResponse['response']['id'],
                'phone' => $apiResponse['response']['phone'],
                'pin' => $data['pin'],
            ]);

            $response['redirectTo'] = route('login.sms');
        }

        if (!isset($apiResponse['success']) || $apiResponse['success'] === false) {
            $response['success'] = false;

            if (isset($apiResponse['error'])) {
                $response['error'] = $apiResponse['error'];
            } elseif (isset($apiResponse['message'])) {
                $response['error'] = $apiResponse['message'];
            }
        }

        return response()->json($response);
    }

    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home.index');
    }

    // Login by hash
    public function loginByHash(string $hash, Request $request)
    {
        try {

            $vars = [
                'action' => 'sign_link',
                'hash' => $hash,
            ];

            $loginRequest = app(CurlService::class)->chPost(
                ApiRoutesEnum::LOGIN_BY_HASH->getRoute(),
                $vars
            );

            if (
                empty($loginRequest['success'])
                || empty($loginRequest['response'])
                || empty($loginRequest['token'])
                || empty($loginRequest['client_id'])
            ) {
                throw new Exception('Bad response');
            }


            $loggedInFlag = Auth::attempt([
                'client_id' => $loginRequest['client_id'],
                'login_token' => $loginRequest['token'],
                'request' => $request,
            ]);
            if (!$loggedInFlag) {
                throw new Exception('Bad token');
            }


            $url = 'verify';
            if (!empty($loginRequest['redirect'])) {
                $url = $loginRequest['redirect']['url'];
            }

            return redirect()->route($url);

        } catch (\Throwable $e) {
            return view('static-pages.failed-msg', ['msg' => 'Грешен линк за вход']);
        }
    }
}
