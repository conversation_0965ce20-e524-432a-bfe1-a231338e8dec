<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\UpdateProfileSettingsRequest;
use App\Services\ChHelperServiceProvider;
use App\Services\CurlService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class ProfileSettingsController extends Controller
{
    public function index(): View|RedirectResponse
    {
        $user = Auth::user();
        if (!$user) {
            return to_route('home.index');
        }

        $data = $user->getAll();

        return view('profile-settings.index', $data);
    }

    public function store(
        UpdateProfileSettingsRequest $request,
        CurlService                  $curlService,
        ChHelperServiceProvider      $chHelperServiceProvider
    ): JsonResponse|RedirectResponse
    {
        $data = $request->validated();
        $data['ip'] = $clientIp = $request->getClientIp();
        $data['browser'] = $clientBrowser = $request->userAgent();
        if (isset($data['notifications'])) {
            unset($data['notifications']['all']);
        }

        $user = Auth::user();
        $data['client_id'] = $clientId = $user->get('client_id');
        $data['login_token'] = $user->get('remember_token');

        $apiResp = $curlService->chPost(
            ApiRoutesEnum::UPDATE_PROFILE->getRoute(),
            $data
        );

        if (!empty($apiResp['success'])) {
            /// refresh client data
            $clientData = $curlService->chGet(
                ApiRoutesEnum::GET_CLIENT->getRoute() . "/{$clientId}",
                $data
            );

            foreach ($clientData as $clientDataKey => $clientDataVal) {
                $user->add($clientDataKey, $clientDataVal);
            }
            $user->add('verified', true);

            if (!$request->ajax()) {
                $toRoute = route('profile');
                if ($chHelperServiceProvider->hasActiveLoan(['ip' => $clientIp, 'browser' => $clientBrowser])) {
                    $toRoute = route('active.loan');
                }

                return redirect($toRoute)->with('success', 'Емейл адреса ти беше променен успешно.');
            } else {
                return response()->json([
                    'message' => __('Success update profile settings')
                ]);
            }
        }

        if (!$request->ajax()) {
            return back()->with('error', 'Грешка! Емейл адреса ти не беше променен.');
        } else {
            return response()->json([
                'message' => __('Success update profile settings')
            ]);
        }
    }

    public function refreshData(Request $request): JsonResponse
    {
        $clientId = $request->user()?->get('client_id');
        if (empty($clientId)) {
            return response()->json([
                'success' => false,
                'message' => 'Client is not logged to profile'
            ]);
        }

        $this->refreshClientData($clientId);
        return response()->json([
            'success' => true,
            'message' => 'Client data is updated.'
        ]);
    }
}
