<?php

namespace App\Http\Controllers;

use App\Application\Actions\HomeDataAction;
use App\Application\Enums\LoanStatusEnum;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;

class HomeController extends Controller
{
    public function index(
        Request        $request,
        HomeDataAction $homeDataAction
    ): View|RedirectResponse {

        if (Auth::check()) {

            $user = Auth::user();
            $loanData = $user->get('loan_data');

            if (empty($loanData['loan_status_id'])) {
                return redirect()->route('profile');
            }

            $routeName = match ($loanData['loan_status_id']) {
                LoanStatusEnum::New->id() => 'contract',
                LoanStatusEnum::Signed->id(), LoanStatusEnum::Processing->id() => 'signed',
                4, LoanStatusEnum::Approved->id(), LoanStatusEnum::Active->id(), LoanStatusEnum::Cancelled->id()
                    => 'active.loan',
                default => 'profile',
            };

            return redirect()->route($routeName);
        }

        $data = $homeDataAction->execute([
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent()
        ]);
        $data['examples'] = $this->getExampleAmounts();

        return view('home.index', $data);
    }

    private function getExampleAmounts(): array
    {
        $result = [
            'ex1_loan_amount' => 700,
            'ex1_loan_interest' => 52.28,
            'ex1_installment_amount' => 188.32,
            'ex1_return_amount' => 753.28,
            'ex1_tax_review' => 0,
            'ex1_tax_accept' => 0,
            'ex1_tax_administration' => 0,

            'ex2_loan_amount' => 400,
            'ex2_loan_interest' => 196.20,
            'ex2_installment_amount' => 16.35,
            'ex2_return_amount' => 596.20,
            'ex2_tax_review' => 0,
            'ex2_tax_accept' => 0,
            'ex2_tax_administration' => 0,
        ];

        // Add EUR-converted values for each existing key
        $originalKeys = array_keys($result);
        foreach ($originalKeys as $key) {
            $result[$key . '_eur'] = amountEur($result[$key], '');
        }

        return $result;
    }
}
