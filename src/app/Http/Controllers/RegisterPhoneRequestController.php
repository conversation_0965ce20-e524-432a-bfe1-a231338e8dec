<?php

namespace App\Http\Controllers;

use App\Application\Actions\RegisterPhoneRequestAction;
use App\Http\Requests\RegisterPhoneRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class RegisterPhoneRequestController extends Controller
{
    public function index(): View
    {
        return view('register-phone-request.index');
    }

    public function store(
        RegisterPhoneRequest       $request,
        RegisterPhoneRequestAction $registerPhoneRequestAction
    ): JsonResponse {

        $data = $request->validated();

        $data['ip'] = $request->getClientIp();
        $data['browser'] = $request->userAgent();
        $apiResponse = $registerPhoneRequestAction->execute($data);

        if (isset($apiResponse['success']) && $apiResponse['success'] === true) {
            return response()->json([
                'success' => true,
                'message' => __('Thank you for your request, we will call you soon'),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $apiResponse['message_for_client'] ?? __('General server error'),
        ]);
    }
}
