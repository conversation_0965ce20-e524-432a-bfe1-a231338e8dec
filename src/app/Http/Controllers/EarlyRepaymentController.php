<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class EarlyRepaymentController extends Controller
{
    public function index(): View|RedirectResponse|JsonResponse
    {
        $client = Auth::user();
        if (!$client) {
            return response()->json([
                'redirectTo' => route('login')
            ]);
        }

        $loan_data = $client->get('loan_data');
        if (empty($loan_data['installments'])) {
            return redirect()->route('logout');
        }

        $data['loan_data'] = $loan_data;
        $data['last_installment_date'] = Carbon::parse(collect($loan_data['installments'])->last()['due_date'])->format('m-d-Y');

        // for the begin we use today date!
        $data['lastInstallmentDate'] = Carbon::today()->format('Y-m-d');

        return view('early-repayment.index', $data);
    }

    public function earlyRepaymentData(int $loanId, Request $request): JsonResponse
    {
        $data = $request->validate([
            'date' => 'required|string'
        ]);

        try {
            $user = Auth::user();
            $repayment_date = Carbon::parse($data['date'])->format('Y-m-d');

            $apiResponse = app(CurlService::class)->chGet(
                ApiRoutesEnum::EARLY_REPAYMENT_CALC->getRoute() . '/' . $loanId,
                [
                    'client_id' => $user->get('client_id'),
                    'loan_id' => $loanId,
                    'repayment_date' => $repayment_date,
                    'login_token' => $user->get('remember_token'),
                    'ip' => $request->getClientIp(),
                    'browser' => $request->userAgent(),
                ]
            );

            if (
                !empty($apiResponse['success'])
                && !empty($apiResponse['response'])
            ) {
                $response = [
                    'success' => true,
                    'data' => [
                        'repaymentDate' => $apiResponse['response']['repaymentDate'],
                        'principal'     => $apiResponse['response']['principal'],
                        'interests'     => $apiResponse['response']['interests'],
                        'taxes'         => $apiResponse['response']['taxes'],
                        'total'         => $apiResponse['response']['total'],

                        'principal_eur'     => $apiResponse['response']['principal_eur'],
                        'interests_eur'     => $apiResponse['response']['interests_eur'],
                        'taxes_eur'         => $apiResponse['response']['taxes_eur'],
                        'total_eur'         => $apiResponse['response']['total_eur'],
                    ],
                    'redirectTo' => null,
                ];
            } else {
                $response = [
                    'success' => false,
                    'data' => [],
                    'messages' => [
                        0 => 'Възникна грешка. Моля опитай пак.',
                    ],
                    'redirectTo' => null,
                ];
            }

            return response()->json($response);

        } catch (\Throwable $e) {

            $response = [
                'success' => false,
                'data' => [],
                'messages' => [
                    0 => 'Възникна грешка. Моля опитай пак.',
                ],
                'redirectTo' => null,
            ];

            return response()->json($response);

        }
    }
}
