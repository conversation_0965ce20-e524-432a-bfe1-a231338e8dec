<?php

namespace App\Http\Controllers;

use App\Services\CurlService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class BlogController extends Controller
{
    public function index(Request $request): View
    {
        $page = $request->get('page', 1);

        $data['posts'] = app(CurlService::class)->chGet("/get-blog-posts", [
            'page' => $page,
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent()
        ]);

        return view('blog.index', $data);
    }

    public function show(string $slug, ?string $parent = null): View|RedirectResponse
    {
        if ($parent) {
            $slug = $parent;
        }

        $data['post'] = app(CurlService::class)->chGet("/get-blog-post/{$slug}", [
            'ip' => request()->getClientIp(),
            'browser' => request()->userAgent(),
        ]);
        if (isset($data['post']['status']) && !$data['post']['status']) {
            return redirect('/404');
        }

        if (!isset($data['post']['content'])) {
            return redirect('/blog');
        }

        request()->merge(['post' => $data['post']]);

        return view('blog.show', $data);
    }
}
