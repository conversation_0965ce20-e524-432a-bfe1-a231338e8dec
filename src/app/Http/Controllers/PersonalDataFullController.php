<?php

namespace App\Http\Controllers;

use App\Application\Actions\PersonalDataFullDataAction;
use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\FullDataRequest;
use App\Services\CurlService;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PersonalDataFullController extends Controller
{
    public function index(
        Request                    $request,
        PersonalDataFullDataAction $personalDataFullDataAction
    ): View|RedirectResponse
    {
        $step = $request->session()->get('step');
        if ($step !== 3) {
            return to_route('home.index');
        }

        $data = $personalDataFullDataAction->execute();

        return view('personal-data-full.index', $data);
    }

    public function store(
        FullDataRequest $request,
        CurlService     $curlService
    ): RedirectResponse
    {
        $data = $request->validated();
        $data['ip'] = $request->getClientIp();
        $data['browser'] = $request->userAgent();

        unset($data['full_date']);
        if (intval($data['lifetime_idcard'])) {
            $data['valid_date'] = Carbon::now()->modify('+100 years')->format('Y-m-d');
            $data['issue_date'] = Carbon::now()->modify('-10 years')->format('Y-m-d');
        } else {
            $data['valid_date'] = date('Y-m-d', strtotime(implode('-', $data['valid_date'])));
            $data['issue_date'] = date('Y-m-d', strtotime('-10 years ' . $data['valid_date']));
        }

        $apiResp = $curlService->chPost(
            ApiRoutesEnum::FINALIZE_REQUEST->getRoute(),
            $data
        );

        if (empty($apiResp['success'])) {
            $errorMessage = $apiResp['message'] ?? __('General error');

            $redirectTo = $apiResp['response']['redirect']['url'] ?? null;
//            $redirectParams = $apiResp['response']['redirect']['url_params'] ?? null;

            if ($redirectTo) {
                return to_route($redirectTo);
            }

            return back()->withInput($data)->with('error', $errorMessage);
        }

        if ($this->tryLogin($apiResp, $request)) {
            session()->forget('step');

            return to_route($apiResp['response']['url']);
        }

        return back()->withInput($data)->with('error', __('General Error. Failed to login.'));
    }
}
