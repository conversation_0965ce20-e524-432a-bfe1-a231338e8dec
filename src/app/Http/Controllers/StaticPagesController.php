<?php

namespace App\Http\Controllers;

use App\Application\Actions\ProductsDataAction;
use App\Mail\ContactFormMail;
use App\Services\CurlService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class StaticPagesController extends Controller
{
    public function referralsTermsAndConditions(): View
    {
        return view('static-pages.referrals-terms-and-conditions');
    }

    public function aboutUs(ProductsDataAction $productsDataAction): View
    {
        $data = $productsDataAction->getProductSettings();
        if (auth()->check()) {
            $data['onWelcome'] = true;
        }

        return view('static-pages.about-us', $data);
    }

    public function careers(): View
    {
        return view('static-pages.careers');
    }

    public function job(string $jobId): View|RedirectResponse
    {
        if (!view()->exists('jobs.' . $jobId)) {
            return redirect()->route('careers');
        }

        return view('static-pages.job', [
            'jobId' => $jobId
        ]);
    }

    public function howItWorks(ProductsDataAction $productsDataAction): View
    {
        $data = $productsDataAction->getProductSettings();
        if (auth()->check()) {
            $data['onWelcome'] = true;
        }
        $data['min_amount'] = 200;
        $data['min_amount_eur'] = amountEur($data['min_amount'], '');
        $data['max_amount'] = 3000;
        $data['max_amount_eur'] = amountEur($data['max_amount'], '');

        return view('static-pages.how-it-works', $data);
    }

    public function questions(ProductsDataAction $productsDataAction): View
    {
        $data = $productsDataAction->getProductSettings();
        if (auth()->check()) {
            $data['onWelcome'] = true;
        }

        return view('static-pages.questions', $data);
    }

    public function faq(ProductsDataAction $productsDataAction): View
    {
        $data = $productsDataAction->getProductSettings();
        if (auth()->check()) {
            $data['onWelcome'] = true;
        }

        return view('static-pages.faq', $data);
    }

    public function howToPay(): View
    {
        return view('static-pages.how-to-pay');
    }

    public function blog(): View
    {
        return view('static-pages.how-to-pay');
    }

    public function iframeCalcultor(ProductsDataAction $productsDataAction): View
    {
        $data = $productsDataAction->execute();
        return view('static-pages.iframe-calcultor', $data);
    }

    public function promotions(ProductsDataAction $productsDataAction): View
    {
        $data = $productsDataAction->getProductSettings();
        if (auth()->check()) {
            $data['onWelcome'] = true;
        }

        $data['amount'] = 800;
        $data['amount_eur'] = amountEur($data['amount'], '');

        return view('static-pages.promotions', $data);
    }

    public function contacts(): View
    {
        $data = app(CurlService::class)->chGet('/get-office-list');

        return view('static-pages.contanct-us', $data);
    }

    public function sendContactForm(Request $request): RedirectResponse
    {
        $ip = $request->ip();
        $cacheKey = 'contact_form_submissions_' . $ip;

        $submissionCount = (int) Cache::get($cacheKey, 0);
        if ($submissionCount >= 2) {
            return back()->with(['fail' => __('Можете да изпратите максимум 2 запитвания за 24 часа.')]);
        }


        $data = $request->validate([
            'name' => 'required|string',
            'phone' => 'required|string',
            'email' => 'required|email',
            'message' => 'required|string|min:10',
        ]);

        Mail::to('<EMAIL>')->send(new ContactFormMail($data));

        // increment count, or create it with 24h TTL
        Cache::put($cacheKey, $submissionCount + 1, now()->addHours(24));


        return back()->with('success', __('Вашето запитване беше изпратено.'));
    }

    public function termsAndConditions(): View
    {
        $data = ['fee_amount' => 10, 'sms_amount' => 0.30, 'po_amount' => 400];
        $data['fee_amount_eur'] = amountEur($data['fee_amount'], '');
        $data['sms_amount_eur'] = amountEur($data['sms_amount'], '');
        $data['po_amount_eur'] = amountEur($data['po_amount'], '');

        return view('static-pages.terms-and-conditions', $data);
    }

    public function privateDataPolicy(): View
    {
        return view('static-pages.private-data-policy');
    }

    public function tarifa(): View
    {
        return view('static-pages.tarifa');
    }

    public function cookiePolicy(): View
    {
        return view('static-pages.politika-cookies');
    }

    public function declarationsDataUsage(): View
    {
        return view('static-pages.declarations-data-usage');
    }

    public function declarationsDataUsagePdf()
    {
        return response()->file(public_path('/static/declarations/Декларация-за-обработване-и-съхраняване-на-лични-данни.pdf'), [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="Декларация-за-обработване-и-съхраняване-на-лични-данни.pdf"',
        ]);
    }

    public function declarationEmail()
    {
        return response()->file(public_path('/static/declarations/Декларация-относно-индивидуализация-посредством-e-mail.pdf'), [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="Декларация-относно-индивидуализация-посредством-e-mail.pdf"',
        ]);
    }

    public function declarationSign()
    {
        return response()->file(public_path('/static/declarations/Декларация-елетронен-подпис-съгласно-чл-13.pdf'), [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="Декларация-елетронен-подпис-съгласно-чл-13.pdf"',
        ]);
    }

    public function declarationIkt()
    {
        return response()->file(public_path('/static/declarations/Декларация-за-предоставяне-на-лични-данни-за-целите-на-оценка.pdf'), [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="Декларация-за-предоставяне-на-лични-данни-за-целите-на-оценка.pdf"',
        ]);
    }

    public function success(): View
    {
        Auth::user()->add('loanStatusPresented', true);
        return view('static-pages.success');
    }

    public function failed(): View
    {
        Auth::user()->add('loanStatusPresented', true);
        return view('static-pages.failed');
    }
}
