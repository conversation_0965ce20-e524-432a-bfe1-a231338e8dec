<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\DocumentRequest;
use App\Services\CurlService;
use Illuminate\Support\Facades\Response;

class DocumentController extends Controller
{
    public function store(
        DocumentRequest $request,
        CurlService     $curlService
    ) {
        $data = $request->validated();

        $data['amount_requested'] = floatToInt($data['amount_requested']);
        $data['ip'] = $request->getClientIp();
        $data['browser'] = $request->userAgent();

        // $apiResponse['response'] - contains base64 content of document
        $apiResponse = $curlService->chPost(
            ApiRoutesEnum::GET_FILE->getRoute(),
            $data
        );

        if (empty($apiResponse['success']) || empty($apiResponse['response'])) {
            return back()->with('error', __('Възникна грешка при изтеглянето'));
        }

        $headers = [
            'Content-Type' => 'application/pdf', // Set the appropriate MIME type
            'Content-Disposition' => 'attachment; filename="' . date('YmdHi') . '-' . $request->get('type') . '.pdf"',
        ];

        return Response::make(base64_decode($apiResponse['response']), 200, $headers);
    }
}
