<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Throwable;

class VerifController extends Controller
{
    public function __construct(
        private CurlService $curlService
    ) {}

    public function index(Request $request): View
    {
        $clientId = null;
        try {
            $clientId = $request->user()?->get('client_id') ?? null;

            $this->curlService->chPost(ApiRoutesEnum::VERIF_OFFERED->getRoute(), [
                'client_id' => $clientId,
                'ip' => $request->getClientIp(),
                'browser' => $request->userAgent()
            ]);

            Log::channel('veriff')->info('#' . $clientId . ' - offered veriff');

        } catch (Throwable $e) {
            $msg = $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            Log::channel('veriff')->info('#' . $clientId . ' - failed to offer veriff, ' . $msg);
        }

        return view('verif.index');
    }

    public function getVeriffProviderUrl(Request $request): RedirectResponse
    {
        $clientId = null;
        try {
            $user = Auth::user();
            $loanId = $user->get('loan_data')['loan_id'] ?? 0;
            $clientId = $user->get('client_id') ?? null;

            $response = $this->curlService->chPost(ApiRoutesEnum::GET_VERIFICATION_URL->getRoute(), [
                'login_token' => $user->get('remember_token'),
                'client_id' => $clientId,
                'loan_id' => $loanId,
                'ip' => $request->getClientIp(),
                'browser' => $request->userAgent()
            ]);

            if (!empty($response['success']) && $response['success'] === true) {
                Log::channel('veriff')->info('#' . $clientId . ' - redirected to veriff');

                return redirect($response['response']['verification']['url']);
            }

            Log::channel('veriff')->info('#' . $clientId . ' - failed to redirect to veriff, Bad Api response: ' . json_encode($response));

        } catch (Throwable $e) {
            $msg = $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();

            Log::channel('veriff')->info('#' . $clientId . ' - failed to redirect to veriff, ' . $msg);
        }

        return to_route('home.index')->with('fail', 'Системна грешка.');
    }

    public function start(Request $request): JsonResponse
    {
        try {

            $verificationData = $request->all();
            $verificationData['ip'] = $request->getClientIp();
            $verificationData['browser'] = $request->userAgent();
            Log::channel('veriff')->info('webhookStarted:', $request->all());

            if (empty($verificationData['id'])) {
                return response()->json(['status' => 'rejected']); // Respond to Veriff
            }

            $clientId = null;
            if (preg_match('/^(\d+)_/', $verificationData['vendorData'], $matches)) {
                $clientId = $matches[1];
            }
            $response = $this->curlService->chPost(ApiRoutesEnum::START_VERIFICATION->getRoute(), [
                'client_id' => $clientId,
                ...$verificationData
            ]);

            if (!empty($response['success']) && $response['success'] === false) {
                throw new \Exception('Системна грешка: ' . json_encode($response));
            }

            return response()->json([
                'status' => 'received'
            ]);
        } catch (Throwable $e) {
            Log::debug($e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            return response()->json([
                'status' => 'rejected'
            ]);
        }
    }

    public function finish(Request $request): JsonResponse
    {
        try {
            $verificationData = $request->all();
            $verificationData['ip'] = $request->getClientIp();
            $verificationData['browser'] = $request->userAgent();

            Log::channel('veriff')->info('webhookFinished:', $verificationData);

            if (empty($verificationData['status'])) {
                return response()->json(['status' => 'rejected']); // Respond to Veriff
            }
            if (empty($verificationData['verification']['id'])) {
                return response()->json(['status' => 'rejected']); // Respond to Veriff
            }

            $clientId = null;
            if (preg_match('/^(\d+)_/', $verificationData['verification']['vendorData'], $matches)) {
                $clientId = $matches[1];
            }

            $response = $this->curlService->chPost(ApiRoutesEnum::FINISH_VERIFICATION->getRoute(), [
                'client_id' => $clientId,
                ...$verificationData
            ]);

            if (!empty($response['success']) && $response['success'] === false) {
                throw new \Exception('Системна грешка: ' . json_encode($response));
            }

            return response()->json([
                'status' => 'received'
            ]);
        } catch (Throwable $e) {
            Log::debug($e->getMessage() . ',' . $e->getFile() . ':' . $e->getLine());

            return response()->json([
                'status' => 'rejected'
            ]);
        }
    }

    public function skipVerification(Request $request): RedirectResponse
    {
        $loanId = Auth::user()->get('loan_data')['loan_id'] ?? 0;
        $user = Auth::user();

        $response = $this->curlService->chGet(ApiRoutesEnum::SKIP_VERIFICATION->getRoute(), [
            'login_token' => $user->get('remember_token'),
            'client_id' => $user->get('client_id'),
            'loan_id' => $loanId,
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent(),
        ]);

        if (!empty($response['success']) && $response['success'] === true) {
            return to_route('signed');
        }

        return to_route('verification.index');
    }
}
