<?php


namespace App\Http\Controllers;

error_reporting(E_ALL);
ini_set('display_errors', 1);

use App\Http\Requests\PosPaymentRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use VenelinIliev\Borica3ds\SaleRequest;
use VenelinIliev\Borica3ds\SaleResponse;

class PosPaymentController extends Controller
{
    const BORICA_CURRENCY_CODE = 'bg';
    const BORICA_CURRENCY = 'BGN';

    public function index(): View
    {
        return view('pos-payment.index', ['user' => Auth::user()]);
    }

    public function store(PosPaymentRequest $request)
    {
        try {
            // env relations
            $tid = env('BORICA_3DS_TERMINAL_ID');
            $mid = env('BORICA_3DS_MERCHANT_ID');
            $isLive = env('BORICA_LIVE', false);
            $certPass = env('BORICA_PRIVATE_KEY');
            $merchant = env('BORICA_MERCHANT_NAME');

            if (empty($tid) || empty($mid) || empty($merchant) || empty($certPass)) {
                throw new \Exception('No connection details provided');
            }

            // sending params
            $amount = $request->amount;
            $phone = $this->getPhone($request);
            $mInfo = $this->getMInfoData($request, $phone);
            $order = str_ireplace(':', '', Carbon::now()->toTimeString());
            $keyPath = $this->getKeyPath($isLive);
            $description = $this->getDescription($request);
            $defaultUrl = route('home.index');
            $redirectUrl = route('pos-payment.result.old');

            Log::channel('borica')->info("IP: " . $request->ip() . " | amount: " . $amount . " | descr: $description | time: " . now());


            $sale = (new SaleRequest())
                ->setAmount($amount)
                ->setOrder($order)
                ->setDescription($description)
                ->setMerchantName($merchant)
                ->setMerchantUrl($defaultUrl)
                ->setCountryCode(self::BORICA_CURRENCY_CODE)
                ->setBackRefUrl($redirectUrl)
                ->setCurrency(self::BORICA_CURRENCY)
                ->setMInfo($mInfo)
                ->setTerminalID($tid)
                ->setMerchantId($mid)
                ->setPrivateKey($keyPath, $certPass)
                ->setEnvironment($isLive);

            $sale->send();

        } catch (\Throwable $e) {
            Log::channel('borica')->error(
                'PosPaymentController::store - '
                . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine()
            );

            return view('pos-payment.index')->with('error', 'Възникна грешка при връзка с пеймент оператор, опитай пак.');
        }
    }

    private function getKeyPath(bool $isLive): string
    {
        $keyPath = resource_path('certificates/borica-3ds/test.key');

        if ($isLive) {
            $keyPath = resource_path('certificates/borica-3ds/real.key');
        }

        return $keyPath;
    }

    private function getDescription(PosPaymentRequest $request): string
    {
        return Str::limit(
            $request->pin . ','
            . $request->name . ','
            . $request->description
            , 50
            , ''
        );
    }

    private function getPhone(PosPaymentRequest $request): string
    {
        $phone = $request->phone;
        if (substr($phone, 0, 1) === '0') {
            $phone = substr($phone, 1); // Cut the "0"
        } else if (substr($phone, 0, 3) === '359') {
            $phone = substr($phone, 3); // Cut the "359"
        }

        return $phone;
    }

    private function getMInfoData(PosPaymentRequest $request, string $phone): array
    {
        return [
            'cardholderName' => $request->name,
            'mobilePhone' => [
                'cc' => '359',
                'subscriber' => $phone,
            ],
        ];
    }

    /**
     * Кодове за грешка:
     * --------------
     * No | Описание
     * --------------
     * 00 Нормално изпълнена авторизация
     * 86 Транзакция със същите характеристики е вече регистрирана в системата.
     * 87 Грешна версия на протокола.
     * 88 За управляващи транзакции. Не е подаден параметър BOReq.
     * 89 За управляващи транзакции. Не е намерена първоначалната транзакция.
     * 90 Картата не е регистрирана в Directory сървера
     * 91 Timeout от авторизационната система
     * 92 При операция „Проверка за статуса на транзакция”. Изпратеният параметър eBorica е с невалиден формат.
     * 93 Неуспешна 3 D автентикация от ACS.
     * 94 Анулирана (канцелирана) трансакция
     * 95 Невалиден подпис на търговеца
     * 96 Техническа грешка при обработка на транзакцията
     * 97 Отхвърлен Reversal
     * 98 При операция „Проверка за статуса на транзакция”. За изпратения BOReq няма регистриран BOResp в сайта на БОРИКА.
     * 99 Авторизацията е отхвърлена от TPSS
     **/
    public function result(Request $request)
    {
        // $isLive = env('BORICA_LIVE', false);
        // $keyPath = resource_path('certificates/borica-3ds/signed/V6200008-stikcredit.bg-D.cer');
        // if ($isLive) {
        //     $keyPath = resource_path('certificates/borica-3ds/signed/V6200008-stikcredit.bg-P.cer');
        // }

        // $saleResponse = (new SaleResponse())->setPublicKey($keyPath)->setResponseData($_POST);
        // $responseCode = $saleResponse->getResponseCode();

        // $ip = $request->ip();
        // $logMessage = "IP: $ip | responseCode: " . $responseCode . " | time: " . now();
        // Log::channel('borica')->info($logMessage);

        $responseCode = '00';
        return view('pos-payment.result', ['responseCode' => $responseCode]);
    }
}
