<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoanController extends Controller
{
    public function downloadDoc(int $fileId, Request $request)
    {
        $user = Auth::user();
        $fileName = $request->get('fileName');

        if (
            empty($user->get('remember_token')) ||
            empty($user->get('client_id')) ||
            empty($fileId) ||
            empty($fileName)
        ) {
            return false;
        }

        try {
            // $apiResponse['response'] - contains base64 of document
            $apiResponse = app(CurlService::class)->chPost(
                ApiRoutesEnum::GET_CLIENT_FILE->getRoute(),
                [
                    'file_id' => $fileId,
                    'login_token' => $user->get('remember_token'),
                    'client_id' => $user->get('client_id'),
                    'ip' => $request->getClientIp(),
                    'browser' => $request->userAgent()
                ]
            );

            if (
                !empty($apiResponse['success'])
                && !empty($apiResponse['response'])
            ) {
                return [
                    'file' => $apiResponse['response'],
                    'name' => $fileName,
                ];
            }

            return false;
        } catch (\Throwable $e) {
            return false;
        }
    }
}
