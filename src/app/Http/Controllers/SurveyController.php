<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class SurveyController extends Controller
{
    public function index(
        string  $hash,
        int     $rate,
        Request $request
    ): View
    {
        $view = 'survey-failed';
        $hash = trim($hash);

        if (
            empty($hash) ||
            empty($rate)
        ) {
            return view($view);
        }

        $urlTags = [
            'hash' => $hash,
            'rate' => $rate,
        ];

        $url = ApiRoutesEnum::SURVEY_HASH_CHECK->getRoute();

        foreach ($urlTags as $tag => $value) {
            $tag = strtolower('{' . $tag . '}');
            $url = str_replace($tag, $value, $url);
        }

        $surveyRequest = app(CurlService::class)->chPost(
            $url,
            [
                'ip' => $request->getClientIp(),
                'browser' => $request->userAgent(),
            ]
        );

        if (!empty($surveyRequest['success'])) {
            $view = 'survey-success';
        }

        return view("survey.{$view}");
    }
}
