<?php

namespace App\Http\Controllers;

use App\Services\CurlService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ClientRateUsController extends Controller
{
    public function show(int $clientRateUsId, Request $request): JsonResponse
    {
        //// client rate us
        $clientRateUsRows = app(CurlService::class)->chGet('/get-client-rate-us', [
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent()
        ]);
        if (isset($data['clientRateUsRows']['status']) && !$data['clientRateUsRows']['status']) {
            $clientRateUsRows = [];
        }

        $data['clientRateRow'] = null;
        if (!empty($clientRateUsRows)) {
            foreach ($clientRateUsRows as $clientRateUsRow) {
                if (intval($clientRateUsRow['id']) === $clientRateUsId) {
                    $data['clientRateRow'] = $clientRateUsRow;
                }
            }
        }

        return response()->json([
            'modalId' => str($data['clientRateRow']['client_name'] ?? '')->slug(),
            'modal' => view('client-rate-us.show', $data)->render()
        ]);
    }
}
