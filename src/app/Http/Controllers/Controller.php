<?php

namespace App\Http\Controllers;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as RoutingController;
use Illuminate\Support\Facades\Auth;

class Controller extends RoutingController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    protected function tryLogin(array $resFromApi, Request $request): bool
    {
        if (
            !empty($resFromApi['success'])
            && !empty($resFromApi['response']['client_id'])
            && !empty($resFromApi['response']['token'])
        ) {
            return Auth::attempt([
                'client_id' => $resFromApi['response']['client_id'],
                'login_token' => $resFromApi['response']['token'],
                'request' => $request,
                'verified' => 1, // to go to profile directly
            ]);
        }

        return false;
    }

    public function getLoanStatus(int $loanId, array $data = []): array
    {
        $user = Auth::user();

        if ($loanId === 0) {
            return [];
        }

        $apiResponse = app(CurlService::class)->chPost(
            ApiRoutesEnum::GET_LOAN_STATUS->getRoute() . "/{$loanId}",
            [
                'login_token' => $user->get('remember_token'),
                'client_id' => $user->get('client_id'),
                'loan_id' => $loanId,
                ...$data
            ]
        );

        if (!empty($apiResponse['success'])) {
            return $apiResponse['response']['loan_data'];
        }
        return [];
    }

    protected function refreshLoanData(int $loanId, array $data = []): array
    {
        $user = Auth::user();

        if ($loanId === 0) {
            return [];
        }

        $apiResponse = app(CurlService::class)->chPost(
            ApiRoutesEnum::GET_LOAN_STATUS->getRoute() . "/{$loanId}",
            [
                'login_token' => $user->get('remember_token'),
                'client_id' => $user->get('client_id'),
                'loan_id' => $loanId,
                ...$data
            ]
        );

        if (!empty($apiResponse['success'])) {
            $loanData = $apiResponse['response']['loan_data'];
            $user->add('loan_data', $loanData);
            $user->add('verified', true);
        }

        return $user->get('loan_data') ?? [];
    }

    public function getActiveLoansAmount(): int
    {
        if (!Auth::check()) {
            return 0;
        }

        $user = Auth::user();
        $clientId = $user->get('client_id');

        $data = [
            'login_token' => $user->get('remember_token'),
            'client_id' => $user->get('client_id')
        ];

        $apiResponse = app(CurlService::class)->chPost(
            ApiRoutesEnum::CLIENT_TOTAL_DUE->getRoute(),
            $data
        );

        if (!isset($apiResponse['response']['total_due'])) {
            return 0;
        }

        return floatToInt($apiResponse['response']['total_due']);
    }

    public function refreshClientData(int $clientId): void
    {
        $user = Auth::user();
        $data['login_token'] = $user->get('remember_token');
        /// refresh client data
        $clientData = app(CurlService::class)->chGet(
            ApiRoutesEnum::GET_CLIENT->getRoute() . "/{$clientId}",
            $data
        );
        foreach ($clientData as $clientDataKey => $clientDataVal) {
            $user->add($clientDataKey, $clientDataVal);
        }
    }
}
