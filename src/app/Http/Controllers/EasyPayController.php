<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use App\Http\Requests\ClientDueRequest;
use App\Http\Requests\ClientPaymentRequest;
use App\Http\Requests\ClientTakeMoneyRequest;
use App\Services\CurlService;
use App\Services\CurlEasyPayService;

class EasyPayController extends Controller
{
    const PAY_RESP_CODE_COMMON_ERROR = 96; // Обща грешка

    // credit hunter api:
    const EPAY_CLIENT_TAKE_MONEY = '/easypay-client-take-money';

    // easy pay mico service:
    const EPAY_CLIENT_DUE = '/pay/init';
    const EPAY_CLIENT_PAYMENT = '/pay/confirm';

    // here is Epay Notification, we do request to our api, POST request, STRING RESPONSE
    public function clientTakeMoney(ClientTakeMoneyRequest $request): string
    {
        try {
            $data = $request->validated();
        } catch (\Throwable $e) {
            \Log::error(
                'EPAY notification validation failed: '
                . $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return 'ERR=wrong_params';
        }

        try {
            $result = app(CurlService::class)->chPost(
                self::EPAY_CLIENT_TAKE_MONEY,
                [
                    'checksum' => $data['checksum'],
                    'encoded'  => $data['encoded'],
                ]
            );

            if (empty($result['success']) || empty($result['response'])) {
                return 'ERR=failed_to_notify';
            }

            return $result['response'];

        } catch (\Throwable $e) {
            \Log::error(
                'EPAY notification failed: '
                . $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return 'ERR=global_error';
        }
    }

    // here is Pay/Init Epay api, GET request, JSON RESPONSE
    public function clientDue(ClientDueRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
        } catch (\Throwable $e) {
            \Log::error(
                'EPAY pay_init validation failed: '
                . $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return response()->json([
                'STATUS' => self::PAY_RESP_CODE_COMMON_ERROR,
            ]);
        }

        try {

            $result = app(CurlEasyPayService::class)->execCurl(
                self::EPAY_CLIENT_DUE,
                $data
            );

            if (empty($result['success']) || empty($result['response'])) {
                return response()->json([
                    'STATUS' => self::PAY_RESP_CODE_COMMON_ERROR,
                ]);
            }

            return response()->json($result['response']);

        } catch (\Throwable $e) {
            \Log::error(
                'EPAY pay_init failed: '
                . $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return response()->json([
                'STATUS' => self::PAY_RESP_CODE_COMMON_ERROR,
            ]);
        }
    }

    // here is Pay/Confitm Epay api, GET request, JSON RESPONSE
    public function clientPayment(ClientPaymentRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
        } catch (\Throwable $e) {
            \Log::error(
                'EPAY pay_confirm validation failed: '
                . $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return response()->json([
                'STATUS' => self::PAY_RESP_CODE_COMMON_ERROR,
            ]);
        }

        try {

            $result = app(CurlEasyPayService::class)->execCurl(
                self::EPAY_CLIENT_PAYMENT,
                $data
            );

            if (empty($result['success']) || empty($result['response']['STATUS'])) {
                return response()->json([
                    'STATUS' => self::PAY_RESP_CODE_COMMON_ERROR,
                ]);
            }

            return response()->json($result['response']);

        } catch (\Throwable $e) {
            \Log::error(
                'EPAY pay_confirm failed: '
                . $e->getMessage() . ', '
                . $e->getLine() . ':'
                . $e->getCode()
            );

            return response()->json([
                'STATUS' => self::PAY_RESP_CODE_COMMON_ERROR,
            ]);
        }
    }
}
