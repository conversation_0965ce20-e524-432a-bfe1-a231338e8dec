<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;

class VerifyDataRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'client_id' => [
                'required',
                'numeric',
                'min:1'
            ],
            'idcard_number' => [
                'sometimes',
                'string',
            ],
            'email' => [
                'sometimes',
                'string',
                'email',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'client_id.required' => 'Client ID is required',
            'client_id.numeric' => 'Client ID cannot be an empty number',
            'client_id.min' => 'Client ID min value should 1',
        ];
    }
}
