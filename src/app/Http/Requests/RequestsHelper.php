<?php

namespace App\Http\Requests;

trait RequestsHelper {

    /**
     *
     *  @param $config - All Fields ($this->fields - Request::)
     */
    public static function generateRules($config) {
        $rules = [];
        foreach ($config as $fieldName => $fieldSetup) {
            $rules[$fieldName] = implode('|', $fieldSetup['rules']);
        } 

        return $rules;
    }

    /**
     * 
     *  @param $config - All Fields ($this->fields - Request::)
     */
    public static function generateMessages($config) {
        $messages = [];
        foreach ($config as $fieldName => $fieldSetup) {
            if (empty($fieldSetup['messages'])) { continue; }

            foreach ($fieldSetup['messages'] as $messageKey => $messageValue) {
                $messageMainKey = $fieldName .'.'. $messageKey;
                $messages[$messageMainKey] = $messageValue;
            }
        }

        return $messages;
    }
}