<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;

class NewLoanRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'product_id' => [
                'required',
                'numeric'
            ],
            'amount_requested' => [
                'required',
                'numeric',
                'regex:/^\d{2,5}(\.\d+)?$/',
            ],
            'period_requested' => [
                'nullable',
                'numeric',
                'digits_between:1,60',
            ],
            'payment_method_id' => [
                'required',
                'numeric',
                'in:1,2'
            ],
            'insurance' => [
                'sometimes',
                'string',
                'in:with_insurance,without_insurance',
            ],
        ];

        $paymentMethodId = intval($this->request->get('payment_method_id'));
        if ($paymentMethodId === 1) {
            $rules['iban'] = [
                'nullable',
                'required_if:payment_method_id,1',
                'min:16',
                'max:34',
                'regex:/^([a-zA-Z0-9]{16,34})+$/'
            ];
        }

        return $rules;
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Product ID Required',
            'product_id.numeric' => 'Product ID should be numeric',

            'amount_requested.required' => 'Amount Requested Required',
            'amount_requested.numeric' => 'Amount Requested should be numeric',
            'amount_requested.regex' => 'Amount Requested should be between 2 and 6',

            'period_requested.numeric' => 'Period Requested should be numeric',
            'period_requested.digits_between' => 'Period Requested should be between 1 and 12',

            'payment_method_id.required' => 'Payment Method ID is required',
            'payment_method_id.numeric' => 'Payment Method ID should be numeric',
            'payment_method_id.in' => 'Payment Method ID is invalid',

            'iban.required_if' => 'Задължително Поле',
            'iban.min' => 'Мин брой символи 20',
            'iban.max' => 'Макс брой символи 22',
            'iban.regex' => 'Невалиден IBAN',
        ];
    }
}
