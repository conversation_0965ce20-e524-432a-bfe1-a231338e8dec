<?php

namespace App\Http\Requests;

use App\Rules\PinValidation;
use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;
use Illuminate\Validation\Rule;

class PersonalDataRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'request_id' => [
                'required',
                'numeric',
            ],
            'email' => [
                'required',
                'email:rfc',
                'min:7',
                'max:50'
            ],
            'pin' => [
                'required',
                'min:10',
                'max:10',
                new PinValidation()
            ],
            'idcard_number' => [
                'required',
                'min:9',
                'max:9'
            ],
            'payment_method_id' => [
                'required',
                'numeric',
                'in:1,2'
            ],
            'iban' => [
                'nullable',
                'required_if:payment_method_id,1',
                Rule::when(
                    (intval($this->get('payment_method_id')) === 1),
                    [
                        'min:16',
                        'max:34',
                        'regex:/^([a-zA-Z0-9]{16,34})+$/'
                    ]
                ),
            ],
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'request_id.required' => 'Request ID Required',
            'request_id.numeric' => 'Request ID should be numeric',

            'email.required' => 'Невалиден Имейл',
            'email.email' => 'Невалиден Имейл',
            'email.min' => 'Невалиден Имейл',
            'email.max' => 'Невалиден Имейл',

            'pin.required' => 'Невалидно ЕГН',
            'pin.min' => 'Невалидно ЕГН',
            'pin.max' => 'Невалидно ЕГН',

            'idcard_number.required' => 'Невалиден номер на лична карта',
            'idcard_number.min' => 'Невалиден номер на лична карта',
            'idcard_number.max' => 'Невалиден номер на лична карта',

            'payment_method_id.required' => 'Payment Method ID is required',
            'payment_method_id.numeric' => 'Payment Method ID should be numeric',
            'payment_method_id.in' => 'Payment Method ID is invalid',

            'iban.required_if' => 'Задължително Поле',
            'iban.min' => 'Задължително Поле',
            'iban.max' => 'Задължително Поле',
            'iban.regex' => 'Невалиден IBAN',
        ];
    }
}
