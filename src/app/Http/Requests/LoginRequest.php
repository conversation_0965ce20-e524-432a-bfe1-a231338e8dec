<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;

class LoginRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'pin' => [
                'required',
                'string',
                'min:10',
                'max:10',
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'pin.required' => 'Полето е задължително / Невалидно ЕГН',
            'pin.string' => 'Полето е задължително / Невалидно ЕГН',
            'pin.min' => 'Полето е задължително / Невалидно ЕГН',
            'pin.max' => 'Полето е задължително / Невалидно ЕГН',
        ];
    }
}
