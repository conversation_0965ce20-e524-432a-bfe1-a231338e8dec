<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;

class RegisterPhoneRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'phone' => [
                'required',
                'regex:/^([0][0-9]*)$/',
                'min:10',
                'max:10'
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'phone.required' => 'Телефонен номер задължътелно поле.',
            'phone.regex' => 'Телефонен номер може да съържа само цифри.',
            'phone.min' => 'Минимална дължниа 10 символа.',
            'phone.max' => 'Максимална дължина 10 символа.',
        ];
    }
}
