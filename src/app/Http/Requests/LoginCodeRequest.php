<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;

class LoginCodeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'min:6',
                'max:6',
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'code.required' => 'Въвели сте изтекъл код!',
            'code.array' => 'Въвели сте изтекъл код!',
            'code.min' => 'Въвели сте изтекъл код!',
            'code.max' => 'Въвели сте изтекъл код!',
        ];
    }
}
