<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Log;

class AffiliateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'pin' => 'required|string|min:10|max:10',
            'idcard_number' => 'required|string|min:9|max:10',
            'email' => 'required|email:rfc',
            'phone' => 'required|string|min:5|max:12|regex:/^([0][8-9][0-9]{8})$/',
            'amount' => 'required|numeric|min:200',
            'token' => 'required|string',
            'affiliate' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'amount.min' => 'Минимална сума е :min лв.'
        ];
    }


    /**
     * When we had some validation error return it like json
     * @param Validator $validator
     * @return void
     */
    protected function failedValidation(Validator $validator): void
    {
        Log::channel('affiliates')->debug([
            'status' => false,
            'message' => 'Validation failed',
            'errors' => $validator->errors(),
        ]);

        throw new HttpResponseException(response()->json([
            'status' => false,
            'message' => 'Validation failed',
            'errors' => $validator->errors(),
        ], 400));
    }
}
