<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;

class RegisterRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'product_id' => [
                'required',
                'numeric'
            ],
            'amount_requested' => [
                'required',
                'numeric',
                'regex:/^\d{2,4}(\.\d+)?$/',
            ],
            'period_requested' => [
                'numeric',
                'digits_between:1,12',
            ],
            'phone' => [
                'required',
                'regex:/^([0][8-9][0-9]{8})$/',
                'min:10',
                'max:10'
            ],
            'agreements.personal_data' => [
                'required',
            ],
            'insurance' => [
                'sometimes',
                'string',
                'in:with_insurance,without_insurance',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'product_id.required' => 'Product ID Required',
            'product_id.numeric' => 'Product ID should be numeric',


            'amount_requested.required' => 'Amount Requested Required',
            'amount_requested.numeric' => 'Amount Requested should be numeric',
            'amount_requested.digits_between' => 'Amount Requested should be between 2 and 6',

            'period_requested.numeric' => 'Period Requested should be numeric',
            'period_requested.digits_between' => 'Period Requested should be between 1 and 12',

            'phone.required' => 'Невалиден Номер',
            'phone.regex' => 'Невалиден Номер',
            'phone.min' => 'Невалиден Номер',
            'phone.max' => 'Невалиден Номер',

            'agreements.personal_data.required' => 'Задължително Поле',
        ];
    }
}
