<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoanStatsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'product_id' => 'numeric|required',
            'amount' => 'numeric|required',
            'period' => 'numeric|required',
            'discount' => 'numeric|required',
        ];
    }
}
