<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SignContractRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'loan_id' => 'required|integer',
            'client_id' => 'required|integer',
            'login_token' => 'required|string',
            'source' => 'sometimes|string',
            'payment_method_id' => 'sometimes|integer|in:1,2',
            'iban' => 'sometimes|nullable|string',
            'insurance' => 'sometimes|string|in:with_insurance,without_insurance',
        ];
    }

    public function withValidator($validator): void
    {
        /// when we have source & source === affiliate
        /// make payment_method_id required
        $validator->sometimes('payment_method_id', 'required', function () {
            return $this->source === 'affiliate';
        });

        /// when we have payment method bank
        /// make iban is required field
        $validator->sometimes('iban', 'required', function () {
            return intval($this->payment_method_id) === 1;
        });
    }

    public function messages(): array
    {
        return [
            'payment_method_id.integer' => 'Полето за начин на плащане трябва да бъде цяло число.',

            'iban.string' => 'Полето за IBAN трябва да бъде текст.',
            'iban.required' => 'Полето за IBAN е задължително.',

            'payment_method_id.required' => 'Полето за начин на плащане е задължително.',
        ];
    }
}
