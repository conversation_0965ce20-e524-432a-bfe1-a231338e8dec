<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest as Request;

class ClientDueRequest extends Request
{
    public function rules(): array
    {
        return [
            'IDN' => ['required', 'string'],
            'MERCHANTID' => ['required', 'string'],
            'CHECKSUM' => ['required', 'string'],
            'TYPE' => ['required', 'string', 'in:CHECK,BILLING'],
            'TID' => ['string'],
        ];
    }
}
