<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReferFriendRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'refer_more' => 'sometimes|integer',
            'phones.*' => 'required_unless:refer_more,1|nullable|digits:10|regex:/^[0-9]+$/',
        ];
    }

    public function messages(): array
    {
        return [
            'phones.*.required_unless' => 'Полето за телефон е задължително.',
            'phones.*.required' => 'Полето за телефон е задължително.',
            'phones.*.digits' => 'Телефонният номер трябва да съдържа точно 10 цифри.',
            'phones.*.regex' => 'Телефонният номер може да съдържа само цифри.',
        ];
    }
}
