<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;
use Illuminate\Support\Facades\Auth;

class UpdateProfileSettingsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => 'sometimes|string|email',
            'notifications.*' => 'nullable|string|in:yes,no',
        ];
    }
}
