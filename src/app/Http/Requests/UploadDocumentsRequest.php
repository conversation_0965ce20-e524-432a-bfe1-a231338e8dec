<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadDocumentsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'selfie' => 'required|file|max:5120|mimes:png,jpg,jpeg,pdf',
        ];
    }

    public function messages(): array
    {
        return [
            'selfie.required' => 'Качването на селфи е задължително.',
            'selfie.file' => 'Каченият файл трябва да бъде валиден файл.',
            'selfie.max' => 'Файлът не трябва да надвишава 5MB.',
            'selfie.mimes' => 'Файлът трябва да бъде в един от следните формати: PNG, JPG, JPEG или PDF.',
        ];
    }
}
