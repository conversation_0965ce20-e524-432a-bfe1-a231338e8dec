<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\RequestsHelper;

class EmailUpdateRequest extends FormRequest
{
    use RequestsHelper;

    private $fields;
    private $requestsHelper;

    function __construct() {

        $this->fields = [
            'email' => [
                'rules'=> [
                    'required',
                    'email:rfc',
                    'min:7',
                    'max:50'
                ],
                'messages' => [
                    'required' => 'Невалиден Имейл',
                    'email' => 'Невалиден Имейл',
                    'min' => 'Невалиден Имейл',
                    'max' => 'Невалиден Имейл',
                ],
            ],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return RequestsHelper::generateRules($this->fields);
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return RequestsHelper::generateMessages($this->fields);
    }
}
