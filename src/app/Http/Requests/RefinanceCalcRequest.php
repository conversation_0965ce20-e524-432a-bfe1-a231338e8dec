<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RefinanceCalcRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric',
            'product_id' => 'required|numeric',
            'period' => 'nullable|numeric',
            'insurance' => 'sometimes|nullable|string|in:with_insurance,without_insurance',
        ];
    }
}
