<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DocumentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'product_id' => 'required|numeric',
            'type' => 'required|in:sef,contract',
            'amount_requested' => 'required|numeric',
            'period_requested' => 'required|numeric',

            'request_id' => 'nullable|numeric',
            'client_id' => 'nullable|numeric',
        ];
    }
}
