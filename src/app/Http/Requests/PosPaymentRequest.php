<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PosPaymentRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'pin' => 'required|string',
            'name' => 'required|string|max:150|regex:/^[A-Za-z\s\-]+$/',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:255',
            'phone' => 'required|numeric',
            'email' => 'nullable|string|max:50',
        ];
    }
}
