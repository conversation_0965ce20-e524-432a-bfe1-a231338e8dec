<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class FullDataRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     * @return array
     */
    public function rules(): array
    {
        return [
            'request_id' => [
                'required',
                'numeric',
            ],
            'first_name' => [
                'required',
                'string',
                'min:2',
                'max:50',
                'regex:/^[a-zA-Z@\.\p{Cyrillic}\s]+$/u'
            ],
            'middle_name' => [
                'nullable',
                'string',
                'min:2',
                'max:50',
                'regex:/^[a-zA-Z@\.\p{Cyrillic}\s]+$/u'
            ],
            'last_name' => [
                'required',
                'string',
                'min:2',
                'max:50',
                'regex:/^[a-zA-Z@\.\p{Cyrillic}\s]+$/u'
            ],
            'lifetime_idcard' => [
                'required',
                'in:0,1',
            ],
            'valid_date.d' => [
                'required_if:lifetime_idcard,0',
                'numeric',
                'min:1',
                'max:31'
            ],
            'valid_date.m' => [
                'required_if:lifetime_idcard,0',
                'numeric',
                'min:1',
                'max:12',
            ],
            'valid_date.y' => [
                'required_if:lifetime_idcard,0',
                'numeric',
                'digits:4',
            ],
            'city_id' => [
                'required',
                'numeric',
            ],
            'address' => [
                'required',
                'string',
            ],
            'district' => [
                'required',
                'string',
            ],
            'full_date' => [
                'required_if:lifetime_idcard,0',
                'date',
                'after_or_equal:today',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        if (intval($this->lifetime_idcard) === 1) {
            $ymdData['valid_date']['d'] = 31;
            $ymdData['valid_date']['m'] = 12;
            $ymdData['valid_date']['y'] = Carbon::now()->addYears(100)->format('Y');

            $this->merge($ymdData);
        }

        $this->merge([
            'full_date' => $this->valid_date['y'] . '-' . $this->valid_date['m'] . '-' . $this->valid_date['d'],
        ]);
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'request_id.required' => 'Request ID Required',
            'request_id.numeric' => 'Request ID should be numeric',

            'first_name.required' => 'Невалидно Име',
            'first_name.string' => 'Невалидно Име',
            'first_name.min' => 'Невалидно Име',
            'first_name.max' => 'Невалидно Име',
            'first_name.regex' => 'Невалидно Първо Име',

            'middle_name.required' => 'Невалидно Име',
            'middle_name.string' => 'Невалидно Име',
            'middle_name.min' => 'Невалидно Име',
            'middle_name.max' => 'Невалидно Име',
            'middle_name.regex' => 'Невалидно Презиме',

            'last_name.required' => 'Невалидно Име',
            'last_name.string' => 'Невалидно Име',
            'last_name.min' => 'Невалидно Име',
            'last_name.max' => 'Невалидно Име',
            'last_name.regex' => 'Невалидна Фамилия',

            'valid_date.d.required' => 'Невалидни данни',
            'valid_date.d.numeric' => 'Невалидни данни',
            'valid_date.d.min' => 'Невалидни данни',
            'valid_date.d.max' => 'Невалидни данни',

            'valid_date.m.required' => 'Невалидни данни',
            'valid_date.m.numeric' => 'Невалидни данни',
            'valid_date.m.min' => 'Невалидни данни',
            'valid_date.m.max' => 'Невалидни данни',
            'valid_date.m.gte' => 'Невалидна дата на лична карта',

            'valid_date.y.required' => 'Невалидни данни',
            'valid_date.y.numeric' => 'Невалидни данни',
            'valid_date.y.digits' => 'Невалидни данни',
            'valid_date.y.after_or_equal' => 'Невалидна дата на лична карта',
            'full_date.after_or_equal' => 'Невалидна дата на лична карта',

            'city_id.required' => 'Няма посочено населено място. Попълнете данните и опитайте отново.',
            'city_id.numeric' => 'Невалидни данни в полето град',
            'address.required' => 'Невалиден Адрес',
            'address.string' => 'Невалиден Адрес',

            'district.required' => 'Невалидни данни',
            'district.string' => 'Невалидни данни',
        ];
    }
}
