<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest as Request;

class ClientPaymentRequest extends Request
{
    public function rules(): array
    {
        return [
            'IDN' => ['required', 'string'],
            'MERCHANTID' => ['required', 'string'],
            'CHECKSUM' => ['required', 'string'],
            'TYPE' => ['required', 'string'],
            'TOTAL' => ['required', 'integer'],
            'TID' => ['required', 'string'],
            'DATE' => ['nullable', 'string'],
            'INVOICES' => ['nullable', 'string'],
        ];
    }
}
