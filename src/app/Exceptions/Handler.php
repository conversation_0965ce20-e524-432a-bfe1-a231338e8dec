<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Session\TokenMismatchException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $exception)
    {
        // dd($exception);
        if ($exception instanceof TokenMismatchException) {
            // Redirect to the home page or any other desired location
            if ($request->ajax() || $request->wantsJson()) {
                session()->flash('warning', __('Невалидна сесия. Моля започенете отначало.'));

                return response()->json([
                    'success' => false,
                    'message' => __('Невалидна сесия. Моля започенете отначало.'),
                    'redirectTo' => route('home.index')
                ]);
            }
            return to_route('home.index')->with('warning', __('Невалидна сесия. Моля започенете отначало.'));
        }

        return parent::render($request, $exception);
    }
}
