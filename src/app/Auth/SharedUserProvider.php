<?php

namespace App\Auth;

use App\Application\Enums\ApiRoutesEnum;
use App\Models\User;
use App\Services\CurlService;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\UserProvider;

class SharedUserProvider implements UserProvider
{
    public function updateRememberToken(Authenticatable $user, $token)
    {
        $user->setRememberToken($token);
    }

    public function validateCredentials(Authenticatable $user, array $credentials): User|bool
    {

        if (
            empty($credentials['client_id'])
            || empty($credentials['login_token'])
            || $credentials['client_id'] !== $user->get('client_id')
            || $credentials['login_token'] !== $user->getRememberToken()
        ) {
            return false;
        }

        // TODO:
        // we could validate token if valid here via API

        return true;
    }

    public function retrieveByCredentials(array $credentials)
    {
        return $this->buildUserFromCredentials($credentials);
    }

    public function retrieveByToken($identifier, $token)
    {
        $user = session()->get('user');

        if (is_array($user)) {
            $user = new User($user);
        }

        return $user;
    }

    public function retrieveById($identifier, $credentials = [])
    {
        $user = session()->get('user');

        if (is_array($user)) {
            $user = new User($user);
        }

        return $user;
    }

    private function buildUserFromCredentials(array $credentials): ?User
    {
        if (empty($credentials['client_id']) || empty($credentials['login_token'])) {
            return null;
        }

        $apiResponse = app(CurlService::class)->chGet(
            ApiRoutesEnum::GET_CLIENT->getRoute() . '/' . $credentials['client_id'],
            $credentials
        );
        if (empty($apiResponse['client_id'])) {
            return null;
        }

        $apiResponse['id'] = $apiResponse['client_id']; // sync laravel userId with our clientId
        $apiResponse['login_token'] = $credentials['login_token']; // refresh token
        $apiResponse['verified'] = !empty($credentials['verified']) ? $credentials['verified'] : false;

        // create user and save it to session
        $user = new User($apiResponse);

        session(['user' => $user]);


        return $user;
    }
}
