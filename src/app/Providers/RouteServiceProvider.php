<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware(['web', 'ip-check'])
                ->name($this->namespace)
                ->group(function ($router) {
                    require base_path('routes/web.php');
                    require base_path('routes/login.php');
                    require base_path('routes/request.php');
                    require base_path('routes/personal_data.php');
                    require base_path('routes/easypay.php');
                    require base_path('routes/affiliates.php');
                });

            Route::middleware(['web', 'auth', 'ip-check'])
                ->namespace($this->namespace)
                ->group(function ($router) {
                    require base_path('routes/verify.php');
                    require base_path('routes/loan.php');
                    require base_path('routes/contract.php');
                    require base_path('routes/profile.php');
                    require base_path('routes/refinance.php');
                    require base_path('routes/calendars.php');
                });

            Route::middleware(['web'])
                ->namespace($this->namespace)
                ->group(function ($router) {
                    require base_path('routes/coming-soon.php');
                });
        });
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('global', static function (Request $request) {
            $user = $request->user();
            $pin = $user ? $user?->get('pin') : null;

            return !empty($pin)
                ? Limit::perMinute(30)->by($pin)
                : Limit::perMinute(20)->by($request->ip());
        });

        RateLimiter::for('loan-status', static function (Request $request) {
            // `loan_id` may be absent if hit before validation; fall back to 'missing'
            $loanId = $request->input('loan_id', 'missing');

            return Limit::perMinutes((60 * 6), 1)
                ->by("loan:$loanId")
                ->response(function (Request $request, array $headers) use ($loanId) {
                    return response()->json([
                        'success' => false,
                        'error' => 'Too many requests for this loan_id. Please retry in 6 hours.',
                        'id' => $loanId !== 'missing' ? (int)$loanId : null,
                    ], 429, $headers);
                });
        });
    }
}
