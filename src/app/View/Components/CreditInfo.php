<?php

namespace App\View\Components;

use Illuminate\View\Component;

class CreditInfo extends Component
{
    private string $header;
    private array $creditInfo;
    private array $addonCreditInfo;
    private bool $history;
    private bool $grid;
    private bool $isFull;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        string $header,
        array  $creditInfo,
        array  $addonCreditInfo = [],
        bool   $history = false,
        bool   $grid = false,
        bool   $isFull = false
    )
    {
        $this->header = $header;
        $this->creditInfo = $creditInfo;
        $this->addonCreditInfo = $addonCreditInfo;
        $this->history = $history;
        $this->grid = $grid;
        $this->isFull = $isFull;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.credit-info', [
            'header'     => $this->header,
            'creditInfo' => $this->creditInfo,
            'addonCreditInfo' => $this->addonCreditInfo,
            'isFull'     => $this->isFull,
            'grid'       => $this->grid
            // 'history'    => $this->history,
        ]);
    }
}
