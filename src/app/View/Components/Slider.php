<?php

namespace App\View\Components;

use Illuminate\View\Component;

class Slider extends Component
{
    private bool $hasPeriod;
    private ?int $minAmount;
    private ?int $minPeriod;
    private array $product;
    private array $sliderData;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        array $sliderData,
        array $product,
        int $minAmount = null,
        int $minPeriod = null,
        bool $hasPeriod = false
    ) {
        $this->hasPeriod = $hasPeriod;
        $this->product = $product;
        $this->sliderData = $sliderData;
        $this->minAmount = $minAmount;
        $this->minPeriod = $minPeriod;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.slider', [
            'hasPeriod' => $this->hasPeriod,
            'slidersData' => $this->sliderData,
            'product' => $this->product,
            'minAmount' => $this->minAmount,
            'minPeriod' => $this->minPeriod,
        ]);
    }
}
