<?php

namespace App\View\Components;

use Illuminate\View\Component;

class DocumentDownload extends Component
{
    private string $templateType;
    private string $templateText;
    private array $loanData;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        string $templateType,
        string $templateText,
        array $loanData,
    ) {
        $this->templateType = $templateType;
        $this->templateText = $templateText;

        //  a workaround for data inconsistency from CH
        if (!empty($loanData['totalAmount'])) {
            $loanData['amount_approved'] = $loanData['totalAmount'];
            $loanData['period_approved'] = $loanData['period'];
        }

        $this->loanData = $loanData;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.document-download', [
            'template_type' => $this->templateType,
            'template_text' => $this->templateText,
            'loan_data' => $this->loanData
        ]);
    }
}
