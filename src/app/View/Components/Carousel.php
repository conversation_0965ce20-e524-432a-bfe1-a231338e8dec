<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\View\View;

class Carousel extends Component
{
    private string $id;
    private array $data;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(
        string $id,
        array  $data
    )
    {
        $this->id = $id;
        $this->data = $data;
    }

    public function render(): View
    {
        return view('components.carousel', [
            'id' => $this->id,
            'data' => $this->data,
        ]);
    }
}
