<?php

namespace App\Application\Actions;

use App\Services\CurlService;
use Illuminate\Support\Facades\Cache;

class HomeDataAction
{
    public function __construct(
        private readonly ProductsDataAction $productsDataAction
    ) {}

    public function execute(array $userData = []): array
    {
        $data = $this->productsDataAction->execute();

        // Attempt to retrieve products from cache
        $posts = Cache::remember('posts', 60 * 60, function () {
            return app(CurlService::class)->chGet('/get-blog-posts');
        });
        $data['posts'] = $posts;
        if (isset($data['posts']['status']) && !$data['posts']['status']) {
            $data['posts'] = [];
        }
        $data['posts'] = collect($data['posts']['data'] ?? [])->slice(0, 6);

        //// client rate us
        // Attempt to retrieve products from cache
        $rates = Cache::remember('rates', 60 * 60, function () use ($userData) {
            return app(CurlService::class)->chGet('/get-client-rate-us', $userData);
        });
        $data['clientRateUsRows'] = $rates;
        if (isset($data['clientRateUsRows']['status']) && !$data['clientRateUsRows']['status']) {
            $data['clientRateUsRows'] = [];
        }
        $data['clientRateUsRows'] = collect($data['clientRateUsRows']);


        $data['onWelcome'] = true;
        if (auth()->check()) {
            $client = auth()->user();
            $data['client'] = $client;
            $data['loan_data'] = $client->get('loan_data');
        }


        return $data;
    }
}
