<?php

namespace App\Application\Actions;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Illuminate\Support\Facades\Cache;

final readonly class RegisterRequestAction
{
    private const LOCK_TIMEOUT = 60;

    public function __construct(
        private CurlService $curlService
    ) {
    }

    public function execute(array $data): array
    {
        $lock = Cache::lock('new-loan-phone-' . $data['phone'], self::LOCK_TIMEOUT);

        if (!$lock->get()) {
            return ['success' => false, 'message_for_client' => __('Try again later')];
        }

        $response =  $this->curlService->chPost(ApiRoutesEnum::REGISTER_REQUEST->getRoute(), $data);

        $lock->release();

        return $response;
    }
}
