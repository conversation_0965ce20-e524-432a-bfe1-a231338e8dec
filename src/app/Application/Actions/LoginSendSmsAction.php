<?php

namespace App\Application\Actions;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;

class LoginSendSmsAction
{
    public function __construct(
        private readonly CurlService $curlService
    ) {
    }

    public function execute(array $data): array
    {
        $response = [];

        try {
            $response = $this->curlService->chPost(
                ApiRoutesEnum::SEND_SMS_CODE->getRoute(),
                $data
            );
        } catch (\Exception $exception) {
            throw new \Exception('General error: ' . $exception->getMessage());
        }

        return $response;
    }
}
