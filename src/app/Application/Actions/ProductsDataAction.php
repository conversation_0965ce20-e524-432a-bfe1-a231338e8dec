<?php

namespace App\Application\Actions;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Illuminate\Support\Facades\Cache;

class ProductsDataAction
{
    public function __construct(
        private CurlService $curlService
    ) {}

    public function execute(?int $clientId = null): array
    {
        return $this->getProductSettings($clientId);
    }

    public function getProductSettings(?int $clientId = null, bool $removeCache = false): array
    {
        $key = 'products';
        if (!empty($clientId)) {
            $key = 'products' . '.' . $clientId;
        }


        if ($removeCache) {
            Cache::forget($key);
        }


        $products = Cache::get($key);
        if (
            empty($products)
            || (isset($products['status']) && $products['status'] === false)
            || (env('APP_ENV') == 'production' && empty($products[24]['productGroup']))
        ) {
            $products = $this->curlService->chGet(ApiRoutesEnum::GET_PRODUCTS->getRoute(), ['client_id' => $clientId]);
            Cache::put($key, $products, 160 * 60);
        }

        $paymentMethods = Cache::get('payment_methods');
        if (empty($paymentMethods) || empty($paymentMethods[1])) {
            $paymentMethods = $this->curlService->chGet(ApiRoutesEnum::GET_PAYMENT_METHODS->getRoute());

             if (empty($paymentMethods[1])) {
                // Handle the case when products do not contain productGroup
                $paymentMethods = [
                    1 => 'Bank',
                    2 => 'Easypay',
                ];
            }

            Cache::put('payment_methods', $paymentMethods, 160 * 60);
        }

        if (!is_array($products) || !is_array($paymentMethods)) {
            throw new \Exception('Cant fetch product settings');
        }

        $data['products'] = $this->reorganizeProducts($products);
        $data['payment_methods'] = array_reverse($paymentMethods, true);

        return $data;
    }

    private function reorganizeProducts(array $products): array
    {
        usort($products, function ($a, $b) {
            if ($a["productGroup"] === $b["productGroup"]) {
                return 0;
            }

            return $a["productGroup"] == "payday" ? -1 : 1;
        });

        return $products;
    }
}
