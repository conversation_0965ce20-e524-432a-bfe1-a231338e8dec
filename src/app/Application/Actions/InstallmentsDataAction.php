<?php

namespace App\Application\Actions;

use Carbon\Carbon;

class InstallmentsDataAction
{
    public function execute(): array
    {
        $data = [];
        if (auth()->check()) {
            $client = auth()->user();
            $data['client'] = $client;
            $data['loan_data'] = $client->get('loan_data');

            $installments = [];
            if (!empty($data['loan_data']['installments'])) {
                foreach ($data['loan_data']['installments'] as $inst) {

                    // skip paid
                    if ($inst['paid'] == 1) {
                        continue;
                    }

                    // format date, so js could parse it properly
                    $inst['due_date'] = Carbon::parse($inst['due_date'])->format('d/m/Y');

                    // overwrite total, since on our page we need to show rests
                    $inst['total_amount'] = $inst['total_rest_amount'];
                    $inst['total_amount_eur'] = $inst['total_rest_amount_eur'];

                    $installments[] = $inst;
                }

                $data['loan_data']['installments'] = $installments;
            }
        }

        return $data;
    }
}
