<?php

namespace App\Application\Actions;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Illuminate\Support\Facades\Cache;
use RuntimeException;
use Throwable;

final readonly class  RegisterPhoneRequestAction
{
    private const LOCK_TIMEOUT = 60;

    public function __construct(
        private CurlService $curlService
    ) {
    }

    public function execute(array $data): array
    {
        try {
            $lock = Cache::lock('new-loan-phone-' . $data['phone'], self::LOCK_TIMEOUT);

            if (!$lock->get()) {
                return ['success' => false, 'message_for_client' => __('Try again later')];
            }

            $response = $this->curlService->chPost(ApiRoutesEnum::REQUEST_BY_PHONE->getRoute(), $data);

            $lock->release();

            return $response;
        } catch (Throwable $e) {
            throw new RuntimeException(__('General error'));
        }
    }
}
