<?php

namespace App\Application\Actions;

use App\Application\Enums\ApiRoutesEnum;
use App\Services\CurlService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class PersonalDataFullDataAction
{
    public function __construct(
        private readonly CurlService $curlService
    ) {
    }

    public function execute(): array
    {
        $data['monthsMap'] = $this->getMonthsMap();
        $data['cities'] = $this->getCities();
        $loanData = $data['loan_data'] = session()->all();

        $data['creditInfo'] = [
            'Сума на заявката' => $loanData['totalAmount'] . ' лв. / €' . ($loanData['totalAmount_eur'] ?? 0),
            'Период на изплащане' => $loanData['period'] . ' ' . $loanData['periodLabel'],
            'Дата на изплащане' => Carbon::parse($loanData['last_due_date'])->format('d.m.Y'),
            'ГПР' => $loanData['gpr'] . '%',
        ];
        $data['header'] = 'Информация за кредит';

        return $data;
    }

    public function getCities()
    {
        $cacheKey = __METHOD__;
        /// if we have value in cache, callback it will not be executed
        return Cache::get($cacheKey, function () use ($cacheKey) {
            $cities = $this->curlService->chGet(ApiRoutesEnum::GET_CITIES->getRoute());

            //// if we have a valid array to write it in cache
            if (!empty($cities)) {
                Cache::put($cacheKey, $cities); /// default cache time 1h
            }

            /// else return as is.
            return $cities;
        });
    }

    public function getMonthsMap(): array
    {
        return [
            0 => '',
            1 => 'Януари',
            2 => 'Февруари',
            3 => 'Март',
            4 => 'Април',
            5 => 'Май',
            6 => 'Юни',
            7 => 'Юли',
            8 => 'Август',
            9 => 'Септември',
            10 => 'Октомври',
            11 => 'Ноември',
            12 => 'Декември',
        ];
    }
}
