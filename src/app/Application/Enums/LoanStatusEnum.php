<?php

declare(strict_types=1);

namespace App\Application\Enums;

enum LoanStatusEnum: string
{
    case New = 'new';
    case Signed = 'signed';
    case Processing = 'processing';

    case Approved = 'approved';
    case Active = 'active';
    case Cancelled = 'cancelled';

    public function id(): int
    {
        return match ($this) {
            self::New => 1,
            self::Signed => 2,
            self::Processing => 3,
            self::Approved => 5,
            self::Active => 6,
            self::Cancelled => 8,
        };
    }
}
