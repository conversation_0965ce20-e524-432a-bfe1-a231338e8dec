<?php

namespace App\Application\Enums;

enum ApiRoutesEnum: string
{
    case VERIFY_CLIENT_TOKEN = '/verify-client-token';
    case GET_FILE = '/get-file';
    case GET_CLIENT = '/get-client';
    case GET_CLIENT_FILE = '/get-client-file';
    case LOAN_STATS = '/loan-stats';
    case SIGN_CONTRACT = '/sign-contract';
    case UPDATE_ID_CARD = '/update-id-card-data';
    case SEND_SMS_CODE = '/send-code';
    case VERIFY_CLIENT = '/validate-client';
    case UPDATE_REQUEST = '/update-request';
    case REFINANCE_LOAN = '/refinance-loan';
    case REFINANCE_STATS = '/refinance-stats';
    case REGISTER_REQUEST = '/register-request';
    case FINALIZE_REQUEST = '/finalize-request';
    case EARLY_REPAYMENT_CALC = '/get-early-repayment-stats';
    case GET_CITIES = '/get-cities';
    case GET_LOAN_STATUS = '/get-loan-status';
    case LOGIN = '/login';
    case LOGIN_BY_HASH = '/validateLoginHash';
    case REQUEST_FROM_PROFILE = '/request-from-profile';
    case REQUEST_BY_PHONE = '/process-phone';
    case UPDATE_PROFILE = '/update-profile';
    case GET_PRODUCTS = '/get-products';
    case GET_PAYMENT_METHODS = '/get-payment-methods';
    case SURVEY_HASH_CHECK = '/survey-hash/{hash}/{rate}';
    case UTM_TRACKER = '/store-utm';
    case UPLOAD_ID_CARD_SELFIE = '/store-id-card-selfie';

    case CLIENT_GET_ACTIVE_LOAN = '/get-client-last-active-loan';
    case CLIENT_HAS_ACTIVE_LOAN = '/client-has-active-loans';
    case CLIENT_TOTAL_DUE = '/get-client-active-loans-due';


    //// verif verification
    case SKIP_VERIFICATION = '/skip-verification';
    case GET_VERIFICATION_URL = '/get-verification-url';
    case START_VERIFICATION = '/start-verification';
    case FINISH_VERIFICATION = '/finish-verification';
    case VERIF_OFFERED = '/verif-offered';
    case GET_AFFILIATE_LIST = '/affiliate-list';
    case REGISTER_AFFILIATE_APPLICATION = '/register-affiliate-application';
    case REFER_FRIEND = '/refer-friend';
    case AFFILIATE_APPLICATION_STATUS = '/get-affiliate-loan-status';

    public function getRoute(?array $args = null): string
    {
        return $this->value;
    }
}
