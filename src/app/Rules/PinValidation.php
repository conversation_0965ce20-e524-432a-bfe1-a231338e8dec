<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class PinValidation implements Rule
{
    private static $EGN_WEIGHTS = [
        2,
        4,
        8,
        5,
        10,
        9,
        7,
        3,
        6,
    ];

    public function passes($attribute, $value)
    {
        $year = substr($value, 0, 2);
        $mon = substr($value, 2, 2);
        $day = substr($value, 4, 2);

        if ($mon > 40) {
            if (!checkdate($mon - 40, $day, $year + 2000)) {
                return false;
            }
        } else {
            if ($mon > 20) {
                if (!checkdate($mon - 20, $day, $year + 1800)) {
                    return false;
                }
            } else {
                if (!checkdate($mon, $day, $year + 1900)) {
                    return false;
                }
            }
        }
        $checksum = substr($value, 9, 1);

        $divider = 11;
        $weights = self::$EGN_WEIGHTS;

        $egnsum = 0;
        for ($i = 0; $i < 9; $i++) {
            $egnsum += substr($value, $i, 1) * $weights[$i];
        }

        $validChecksum = $egnsum % $divider;
        if ($validChecksum == 10) {
            $validChecksum = 0;
        }

        if ($checksum == $validChecksum) {
            return true;
        }
        return false;
    }

    public function message(): string
    {
        return 'Въведеното ЕГН е невалидно.';
    }
}
