<?php

namespace App\Services;

use App\Application\Enums\ApiRoutesEnum;
use Illuminate\Support\Facades\Auth;

class ChHelperServiceProvider
{

    public function setActiveLoan(): void
    {
        if (!Auth::check()) {
            return ;
        }

        $user = Auth::user();
        $clientId = $user->get('client_id');

        $result = app(CurlService::class)->chPost(
            ApiRoutesEnum::CLIENT_GET_ACTIVE_LOAN->getRoute(),
            [
                'login_token' => $user->get('remember_token'),
                'client_id' => $user->get('client_id')
            ]
        );

        if (empty($result['response']['loan'])) {
            return ;
        }


        $user->add('loan_data', $result['response']['loan']);
        $user->add('verified', true);
    }

    public function hasActiveLoan(array $data = []): bool
    {
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();
        $clientId = $user->get('client_id');

        $result = app(CurlService::class)->chPost(
            ApiRoutesEnum::CLIENT_HAS_ACTIVE_LOAN->getRoute(),
            [
                'login_token' => $user->get('remember_token'),
                'client_id' => $user->get('client_id'),
                ...$data
            ]
        );

        if (!isset($result['response']['has_active_loans'])) {
            return false;
        }

        return (bool) $result['response']['has_active_loans'];
    }
}

