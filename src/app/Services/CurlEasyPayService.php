<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CurlEasyPayService
{
    private $url = null;
    private $token = null;

    public function __construct()
    {
        $this->url = env('EMS_API_ADDRESS', '');
        $this->token = env('EMS_API_TOKEN', '');

        if (empty($this->url)) {
            throw new \Exception('Invalid easy pay api endpoint');
        }

        if (empty($this->token)) {
            throw new \Exception('Invalid authorization api key');
        }
    }

    public function execCurl(
        string $route,
        array $data,
        string $method = 'POST'
    ): array {

        try {
            $request = Http::baseUrl($this->url)
                ->asForm()
                ->withHeaders(['authorization' => $this->token]);

            if ($method === 'POST') {
                $response = $request->post($route, $data);
            } else {
                $response = $request->get($route, $data);
            }
// dd(
//     $this->url . $route,
//     $data,
//     $response->json(),
//     $response->body()
// );
            // TODO: LOG!!!
            // $route,
            // $method,
            // $data,
            // $response->json()

            return $response?->json() ?? [];

        } catch (\Exception $exception) {
            Log::channel('easypay')->debug($exception->getMessage());

            return [];
        }
    }
}
