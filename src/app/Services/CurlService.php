<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class CurlService
{
    public function __construct(private $config = [])
    {
        $env = app()->environment();
        $this->config = config('rest-api.' . $env, null);

        if (!$this->config) {
            throw new \Exception('Invalid CH Api settings.');
        }
    }

    public function chGet(string $route, array $data = [])
    {
        $headers = $this->config['headers'] ?? [];
        $authToken = $this->getAuthToken($headers, $data);

        $request = request();
        if (empty($data['ip'])) {
            $data['ip'] = $request->getClientIp();
        }
        if (empty($data['browser'])) {
            $data['browser'] = $request->userAgent();
        }
        $data['utm_tracker_id'] = Session::get('utm_tracker_id');

        $pendingRequest = Http::baseUrl($this->config['CH_API_ADDRESS'])
            ->withHeaders($headers)
            ->asForm();

        if ($authToken) {
            $pendingRequest->withToken($authToken);
        }

        $response = $pendingRequest->get($route, $data);

        $body = json_decode($response->body(), true);
        if (isset($body['message']) && $body['message'] === 'Unauthenticated.') {
            Session::put('WsTokenExpired', true);
        }

        return $body;
    }

    public function chPost(string $route, array $data = []): array
    {
        $request = request();
        $data['utm_tracker_id'] = Session::get('utm_tracker_id');
        if (empty($data['ip'])) {
            $data['ip'] = $request->getClientIp();
        }
        if (empty($data['browser'])) {
            $data['browser'] = $request->userAgent();
        }

        $data['session_id'] = $request->getSession()->getId();
        $data['last_page_accessed'] = $request->getPathInfo();

        $headers = $this->config['headers'] ?? [];
        $authToken = $this->getAuthToken($headers, $data);

        /// base setup
        $pendingRequest = Http::baseUrl($this->config['CH_API_ADDRESS'])->withHeaders($headers);

        /// when we have some files to submit
        if (!empty($data['selfie'])) {
            $pendingRequest->attach(
                    'selfie',
                    file_get_contents($data['selfie']->getPathname()),
                    $data['selfie']->getClientOriginalName()
                )
                ->asMultipart();
        } else {
            // default submit
            $pendingRequest->asForm();
        }

        if ($authToken) {
            $pendingRequest->withToken($authToken);
        }

        $response = $pendingRequest->post($route, $data);

        $body = json_decode($response->body(), true);
        if (isset($body['message']) && $body['message'] === 'Unauthenticated.') {
            Session::put('WsTokenExpired', true);
        }

        /// sometimes we have error on api and here we has empty body
        /// in that case return just empty array and log all response
        if (empty($body)) {
            Log::debug($response);

            $body = [];
        }

        return $body;
    }

    private function getAuthToken(array $headers, array $data): ?string
    {
        $authToken = null;

        /*
         * If we have token in headers from config ($this->config['headers']) or in incoming data then use it
         * else use authenticated user api token
         */
        if (!isset($headers['Authorization'])) {
            if (isset($data['login_token'])) {
                $authToken = $data['login_token'];
            } elseif (($user = auth()->user()) && ($userToken = $user->getRememberToken())) {
                $authToken = $userToken;
            }
        }

        return $authToken;
    }
}
