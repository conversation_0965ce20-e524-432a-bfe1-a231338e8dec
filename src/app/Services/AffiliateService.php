<?php

namespace App\Services;

use App\Application\Enums\ApiRoutesEnum;
use Illuminate\Support\Facades\Cache;

readonly class AffiliateService
{
    public function __construct(
        private CurlService $curlService
    ) {}

    public function checkApplicationStatus(array $data): array
    {
        return $this->curlService->chPost(ApiRoutesEnum::AFFILIATE_APPLICATION_STATUS->getRoute(), $data);
    }

    public function registerAffiliateRequest(array $data): array
    {
        return $this->curlService->chPost(ApiRoutesEnum::REGISTER_AFFILIATE_APPLICATION->getRoute(), $data);
    }

    public function getAvailableAffiliates(array $data = []): array
    {
        $affiliates = Cache::get('affiliates');

        if (!empty($affiliates)) {
            return $affiliates;
        }

        $affiliates = $this->curlService->chGet(ApiRoutesEnum::GET_AFFILIATE_LIST->getRoute(), $data);

        if (!empty($affiliates)) {
            Cache::put('affiliates', $affiliates, now()->addMinutes(60));
        }

        return $affiliates;
    }

}
